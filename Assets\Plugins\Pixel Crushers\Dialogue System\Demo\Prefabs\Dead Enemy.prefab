%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100002
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400002}
  m_Layer: 0
  m_Name: LeftHandLitFingerB
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100004
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400012}
  m_Layer: 0
  m_Name: LeftHandLitFingerA
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100006
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400014}
  m_Layer: 0
  m_Name: Bip01_R_Finger2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100008
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400004}
  m_Layer: 0
  m_Name: Bip01_R_Finger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100010
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400006}
  m_Layer: 0
  m_Name: Bip01_R_Finger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100012
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400016}
  m_Layer: 0
  m_Name: Bip01_R_Finger1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100014
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400008}
  m_Layer: 0
  m_Name: Bip01_R_Finger02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100016
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400010}
  m_Layer: 0
  m_Name: Bip01_R_Finger01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100018
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400018}
  m_Layer: 0
  m_Name: Bip01_R_Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100020
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400076}
  m_Layer: 0
  m_Name: Bip01_R_Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100022
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400020}
  m_Layer: 0
  m_Name: Bip01_R_Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100024
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400022}
  m_Layer: 0
  m_Name: Bip01_R_UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100026
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400044}
  m_Layer: 0
  m_Name: Bip01_R_Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100028
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400024}
  m_Layer: 0
  m_Name: Bip01_L_Finger21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100030
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400032}
  m_Layer: 0
  m_Name: Bip01_L_Finger2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100032
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400026}
  m_Layer: 0
  m_Name: Bip01_L_Finger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100034
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400028}
  m_Layer: 0
  m_Name: Bip01_L_Finger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100036
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400034}
  m_Layer: 0
  m_Name: Bip01_L_Finger1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100038
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400030}
  m_Layer: 0
  m_Name: Bip01_L_Finger01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100040
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400036}
  m_Layer: 0
  m_Name: Bip01_L_Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100042
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400038}
  m_Layer: 0
  m_Name: Bip01_L_Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100044
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400040}
  m_Layer: 0
  m_Name: Bip01_L_Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100046
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400042}
  m_Layer: 0
  m_Name: Bip01_L_UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100048
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400046}
  m_Layer: 0
  m_Name: Bip01_L_Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100050
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400048}
  m_Layer: 0
  m_Name: Bip01_Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100052
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400050}
  m_Layer: 0
  m_Name: Bip01_Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100054
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400052}
  m_Layer: 0
  m_Name: Bip01_Spine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100056
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400066}
  m_Layer: 0
  m_Name: Bip01_Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100058
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400054}
  m_Layer: 0
  m_Name: Bip01_R_Toe0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100060
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400056}
  m_Layer: 0
  m_Name: Bip01_R_Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100062
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400058}
  m_Layer: 0
  m_Name: Bip01_R_Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100064
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400068}
  m_Layer: 0
  m_Name: Bip01_R_Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100066
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400060}
  m_Layer: 0
  m_Name: Bip01_L_Toe0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100068
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400062}
  m_Layer: 0
  m_Name: Bip01_L_Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100070
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400064}
  m_Layer: 0
  m_Name: Bip01_L_Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100072
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400070}
  m_Layer: 0
  m_Name: Bip01_L_Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100074
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400072}
  m_Layer: 0
  m_Name: Bip01_Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100076
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400074}
  m_Layer: 0
  m_Name: Bip01_Pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100078
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400084}
  - component: {fileID: 13700006}
  m_Layer: 0
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100080
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400082}
  - component: {fileID: 13700004}
  m_Layer: 0
  m_Name: InfiltratorPistol
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &100082
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400088}
  - component: {fileID: 13700008}
  m_Layer: 0
  m_Name: AssaultGun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &100084
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400078}
  - component: {fileID: 13700000}
  m_Layer: 0
  m_Name: ReconTroopHelmet
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100086
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400086}
  m_Layer: 0
  m_Name: Bip01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100088
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400080}
  - component: {fileID: 13700002}
  m_Layer: 0
  m_Name: ReconTroop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100090
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400090}
  - component: {fileID: 13500000}
  - component: {fileID: 9518730}
  - component: {fileID: 11480880}
  - component: {fileID: 11400000}
  - component: {fileID: 11420362}
  m_Layer: 0
  m_Name: Dead Enemy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &400002
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100002}
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.017533489, y: -0.01901581, z: -0.00462614}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400012}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400004
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100008}
  m_LocalRotation: {x: 0, y: 0, z: -0.1164023, w: 0.99320215}
  m_LocalPosition: {x: -0.02746078, y: -0.0034492498, z: 0.00046687}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400006}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400006
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100010}
  m_LocalRotation: {x: 0, y: 0, z: -0.1164023, w: 0.99320215}
  m_LocalPosition: {x: -0.03224649, y: 0.00257278, z: 0.00222764}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400004}
  m_Father: {fileID: 400016}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400008
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100014}
  m_LocalRotation: {x: 0, y: 0, z: -0.1164023, w: 0.99320215}
  m_LocalPosition: {x: -0.0359169, y: -0.0072495695, z: -0.00191544}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400010}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400010
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100016}
  m_LocalRotation: {x: -0, y: 0, z: -0.1164023, w: 0.99320215}
  m_LocalPosition: {x: -0.03327984, y: -0.01047363, z: -0.00100273}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400008}
  m_Father: {fileID: 400018}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400012
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100004}
  m_LocalRotation: {x: -0.0020233344, y: -0.0074741147, z: 0.9200086, w: -0.39182177}
  m_LocalPosition: {x: -0.103507996, y: 0.02096764, z: -0.03766356}
  m_LocalScale: {x: 0.9999995, y: 0.9999988, z: 0.999999}
  m_Children:
  - {fileID: 400002}
  m_Father: {fileID: 400076}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400014
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100006}
  m_LocalRotation: {x: 0.00039799995, y: 0, z: -0, w: 0.99999994}
  m_LocalPosition: {x: -0.10548046, y: -0.00079857, z: -0.01990018}
  m_LocalScale: {x: 1, y: 1.0000004, z: 1.0000004}
  m_Children: []
  m_Father: {fileID: 400076}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400016
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100012}
  m_LocalRotation: {x: 0.00039799995, y: 0, z: -0, w: 0.99999994}
  m_LocalPosition: {x: -0.11277363, y: -0.00323029, z: 0.03297887}
  m_LocalScale: {x: 1, y: 1.0000004, z: 1.0000004}
  m_Children:
  - {fileID: 400006}
  m_Father: {fileID: 400076}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400018
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100018}
  m_LocalRotation: {x: -0.6763502, y: 0.2103259, z: 0.040216748, w: 0.7047667}
  m_LocalPosition: {x: -0.03543877, y: -0.00065559, z: 0.06791017}
  m_LocalScale: {x: 1.0000006, y: 1.0000012, z: 0.9999986}
  m_Children:
  - {fileID: 400010}
  m_Father: {fileID: 400076}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400020
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100022}
  m_LocalRotation: {x: 0, y: 0, z: 0.026705528, w: 0.9996434}
  m_LocalPosition: {x: -0.33552468, y: 0.00000001, z: -0.00000014999999}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1}
  m_Children:
  - {fileID: 400076}
  m_Father: {fileID: 400022}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400022
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100024}
  m_LocalRotation: {x: -0.027812365, y: 0.39089906, z: 0.0013795918, w: 0.92001224}
  m_LocalPosition: {x: -0.1755976, y: 0, z: 0.00000014999999}
  m_LocalScale: {x: 1.0000005, y: 0.99999976, z: 1}
  m_Children:
  - {fileID: 400020}
  m_Father: {fileID: 400044}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400024
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100028}
  m_LocalRotation: {x: 0, y: 0, z: -0.11640327, w: 0.99320203}
  m_LocalPosition: {x: -0.0330509, y: 0.0047791298, z: 0.00031238}
  m_LocalScale: {x: 1.0000005, y: 1.0000005, z: 1}
  m_Children: []
  m_Father: {fileID: 400032}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400026
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100032}
  m_LocalRotation: {x: 0, y: 0, z: -0.11640327, w: 0.99320203}
  m_LocalPosition: {x: -0.027164878, y: -0.00073532, z: -0.00170808}
  m_LocalScale: {x: 1.0000005, y: 1.0000005, z: 1}
  m_Children: []
  m_Father: {fileID: 400028}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400028
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100034}
  m_LocalRotation: {x: 0, y: 0, z: -0.11640327, w: 0.99320203}
  m_LocalPosition: {x: -0.03864689, y: 0.00494186, z: -0.00181967}
  m_LocalScale: {x: 1.0000005, y: 1.0000005, z: 1}
  m_Children:
  - {fileID: 400026}
  m_Father: {fileID: 400034}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400030
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100038}
  m_LocalRotation: {x: 0, y: 0, z: -0.11640364, w: 0.99320203}
  m_LocalPosition: {x: -0.03240257, y: -0.00579735, z: 0.00036591}
  m_LocalScale: {x: 1.0000007, y: 0.99999845, z: 1}
  m_Children: []
  m_Father: {fileID: 400036}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400032
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100030}
  m_LocalRotation: {x: -0.00039799995, y: 0, z: -0, w: 0.99999994}
  m_LocalPosition: {x: -0.098807484, y: 0.0129557, z: 0.044207778}
  m_LocalScale: {x: 1, y: 1.0000004, z: 1.0000004}
  m_Children:
  - {fileID: 400024}
  m_Father: {fileID: 400038}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400034
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100036}
  m_LocalRotation: {x: -0.00039799995, y: 0, z: -0, w: 0.99999994}
  m_LocalPosition: {x: -0.109862514, y: -0.0059758797, z: -0.03212524}
  m_LocalScale: {x: 1, y: 1.0000004, z: 1.0000004}
  m_Children:
  - {fileID: 400028}
  m_Father: {fileID: 400038}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400036
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100040}
  m_LocalRotation: {x: 0.67635024, y: -0.21032669, z: 0.04021642, w: 0.7047664}
  m_LocalPosition: {x: -0.027958259, y: -0.00014191, z: -0.059544675}
  m_LocalScale: {x: 1.0000017, y: 0.999997, z: 1.000002}
  m_Children:
  - {fileID: 400030}
  m_Father: {fileID: 400038}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400038
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100042}
  m_LocalRotation: {x: -0.70682526, y: 0, z: 0, w: 0.7073882}
  m_LocalPosition: {x: -0.23164749, y: 0.00000002, z: -0.00000014999999}
  m_LocalScale: {x: 1, y: 0.9999993, z: 1.0000002}
  m_Children:
  - {fileID: 400036}
  - {fileID: 400034}
  - {fileID: 400032}
  m_Father: {fileID: 400040}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400040
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100044}
  m_LocalRotation: {x: 0, y: 0, z: 0.026705528, w: 0.9996434}
  m_LocalPosition: {x: -0.335524, y: 0.00000003, z: 0.00000031}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1}
  m_Children:
  - {fileID: 400038}
  m_Father: {fileID: 400042}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400042
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100046}
  m_LocalRotation: {x: 0.027812373, y: -0.39089945, z: 0.0013795994, w: 0.92001206}
  m_LocalPosition: {x: -0.17559762, y: 0, z: 0.00000014999999}
  m_LocalScale: {x: 1.000001, y: 0.9999999, z: 0.99999934}
  m_Children:
  - {fileID: 400040}
  m_Father: {fileID: 400046}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400044
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100026}
  m_LocalRotation: {x: 0.6845127, y: 0.17731701, z: 0.69285554, w: -0.14125253}
  m_LocalPosition: {x: 0.04835449, y: 0.023371661, z: -0.04107431}
  m_LocalScale: {x: 1, y: 1.0000007, z: 1.0000004}
  m_Children:
  - {fileID: 400022}
  m_Father: {fileID: 400050}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400046
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100048}
  m_LocalRotation: {x: -0.68451256, y: -0.17732364, z: 0.6928532, w: -0.14125624}
  m_LocalPosition: {x: 0.04835464, y: 0.02337128, z: 0.04107449}
  m_LocalScale: {x: 1.0000005, y: 0.99999905, z: 0.9999998}
  m_Children:
  - {fileID: 400042}
  m_Father: {fileID: 400050}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400048
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100050}
  m_LocalRotation: {x: 2.105857e-14, y: -0.00000049381305, z: 0.15682812, w: 0.98762596}
  m_LocalPosition: {x: -0.07228119, y: 0.00000014999999, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 400050}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400050
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100052}
  m_LocalRotation: {x: 2.3203167e-14, y: 0.0000009743701, z: -0.224951, w: 0.9743701}
  m_LocalPosition: {x: -0.24319641, y: 0.00022475, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999995, z: 1.0000004}
  m_Children:
  - {fileID: 400048}
  - {fileID: 400046}
  - {fileID: 400044}
  m_Father: {fileID: 400052}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400052
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100054}
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.1402845, y: -0.00018424001, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400050}
  m_Father: {fileID: 400066}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400054
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100058}
  m_LocalRotation: {x: 0, y: 0, z: -0.7071069, w: 0.70710677}
  m_LocalPosition: {x: -0.13227692, y: 0.17023471, z: 0.000000049999997}
  m_LocalScale: {x: 1.000001, y: 0.999999, z: 1}
  m_Children: []
  m_Father: {fileID: 400056}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400056
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100060}
  m_LocalRotation: {x: -0.0005802075, y: -0.009555244, z: -0.06354142, w: 0.9979333}
  m_LocalPosition: {x: -0.46156988, y: 0.00000002, z: -0.00000002}
  m_LocalScale: {x: 0.99999857, y: 0.99999857, z: 1.0000005}
  m_Children:
  - {fileID: 400054}
  m_Father: {fileID: 400058}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400058
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100062}
  m_LocalRotation: {x: 0, y: 0, z: 0.08948853, w: 0.9959879}
  m_LocalPosition: {x: -0.435281, y: -0.00000001, z: 0.00000002}
  m_LocalScale: {x: 1.0000005, y: 1.0000005, z: 1}
  m_Children:
  - {fileID: 400056}
  m_Father: {fileID: 400068}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400060
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100066}
  m_LocalRotation: {x: 0, y: 0, z: -0.7071069, w: 0.70710677}
  m_LocalPosition: {x: -0.13227695, y: 0.17023475, z: 0.00000006}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400062}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400062
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100068}
  m_LocalRotation: {x: 0.0005802076, y: 0.009555239, z: -0.06354139, w: 0.9979333}
  m_LocalPosition: {x: -0.46156985, y: 0.00000003, z: 0.00000002}
  m_LocalScale: {x: 0.9999995, y: 0.99999857, z: 1.0000005}
  m_Children:
  - {fileID: 400060}
  m_Father: {fileID: 400064}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400064
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100070}
  m_LocalRotation: {x: 0, y: 0, z: 0.08948853, w: 0.9959879}
  m_LocalPosition: {x: -0.43528107, y: 0, z: -0.00000004}
  m_LocalScale: {x: 1.0000005, y: 1.0000005, z: 1}
  m_Children:
  - {fileID: 400062}
  m_Father: {fileID: 400070}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400066
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100056}
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.12334061, y: -0.0001117, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400052}
  m_Father: {fileID: 400072}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400068
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100064}
  m_LocalRotation: {x: -0.026415091, y: 0.99960524, z: 0.00027481755, w: -0.009569025}
  m_LocalPosition: {x: 0.11084411, y: -0.016105639, z: -0.1287879}
  m_LocalScale: {x: 0.99999994, y: 0.99999946, z: 1.0000004}
  m_Children:
  - {fileID: 400058}
  m_Father: {fileID: 400072}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400070
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100072}
  m_LocalRotation: {x: -0.026415119, y: 0.99960524, z: -0.00027189375, w: 0.009568777}
  m_LocalPosition: {x: 0.11084419, y: -0.01610635, z: 0.12878789}
  m_LocalScale: {x: 0.9999992, y: 0.9999999, z: 1.0000005}
  m_Children:
  - {fileID: 400064}
  m_Father: {fileID: 400072}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400072
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100074}
  m_LocalRotation: {x: -0.0000019999995, y: 0.0000004999999, z: -0.00039799995, w: 0.99999994}
  m_LocalPosition: {x: -0.11083122, y: 0.01619429, z: 0.000000109999995}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1}
  m_Children:
  - {fileID: 400070}
  - {fileID: 400068}
  - {fileID: 400066}
  m_Father: {fileID: 400074}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400074
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100076}
  m_LocalRotation: {x: -0.49999976, y: 0.50000024, z: 0.49999976, w: 0.50000024}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400072}
  m_Father: {fileID: 400086}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400076
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100020}
  m_LocalRotation: {x: 0.7068254, y: 0, z: -0, w: 0.70738816}
  m_LocalPosition: {x: -0.23164749, y: -0.00000001, z: -0.00000008}
  m_LocalScale: {x: 1, y: 1.0000004, z: 1.0000004}
  m_Children:
  - {fileID: 400018}
  - {fileID: 400016}
  - {fileID: 400014}
  - {fileID: 400012}
  m_Father: {fileID: 400020}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400078
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100084}
  m_LocalRotation: {x: 0, y: -0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400090}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400080
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100088}
  m_LocalRotation: {x: 0, y: -0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400090}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400082
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100080}
  m_LocalRotation: {x: 0, y: -0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400090}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400084
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100078}
  m_LocalRotation: {x: 0, y: -0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400090}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400086
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100086}
  m_LocalRotation: {x: -0.5, y: 0.5, z: 0.5, w: 0.5}
  m_LocalPosition: {x: 0, y: 1.028672, z: -0.05030605}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400074}
  m_Father: {fileID: 400090}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400088
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100082}
  m_LocalRotation: {x: 0, y: -0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400090}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &400090
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100090}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.1424503, y: -4.609515e-17, z: -2.1873255}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400088}
  - {fileID: 400086}
  - {fileID: 400084}
  - {fileID: 400082}
  - {fileID: 400080}
  - {fileID: 400078}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &9518730
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100090}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: a9cfab5a8b4a0a0448926ba5ca880442, type: 3}
  m_Controller: {fileID: 9100000, guid: 2ddf460389ff5974e8157b027015a158, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100090}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274b80ab5b659c646ba9338870d14625, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  overrideName: Dead Enemy
  overrideUseMessage: (spacebar to inspect)
  maxUseDistance: 5
  events:
    onSelect:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
    onDeselect:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
    onUse:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
--- !u!114 &11420362
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100090}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 32
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags: []
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: 
  questState: 0
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  luaCode: 
  sequence: 
  sequenceSpeaker: {fileID: 0}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 1
  alertMessage: 
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: Dead Enemy
  conversationConversant: {fileID: 400090}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  exclusive: 0
  skipIfNoValidEntries: 0
  stopConversationOnTriggerExit: 0
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: PixelCrushers.GameObjectUnityEvent, Assembly-CSharp-firstpass, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!114 &11480880
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100090}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 16
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags: []
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: 
  questState: 0
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  luaCode: 
  sequence: AnimatorBool(Dead)
  sequenceSpeaker: {fileID: 400090}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 0
  alertMessage: 
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: 
  conversationConversant: {fileID: 400090}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  exclusive: 0
  skipIfNoValidEntries: 1
  stopConversationOnTriggerExit: 0
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: PixelCrushers.GameObjectUnityEvent, Assembly-CSharp-firstpass, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!135 &13500000
SphereCollider:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100090}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 1
  m_Center: {x: 0, y: 0.5, z: 0}
--- !u!137 &13700000
SkinnedMeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100084}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_Materials:
  - {fileID: 2100000, guid: 95846deeca9c9e1439116cac67f8b61d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300006, guid: a9cfab5a8b4a0a0448926ba5ca880442, type: 3}
  m_Bones:
  - {fileID: 400048}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 400048}
  m_AABB:
    m_Center: {x: -0.12967685, y: -0.0057729334, z: -0.0030686446}
    m_Extent: {x: 0.15739784, y: 0.17564186, z: 0.109676495}
  m_DirtyAABB: 0
--- !u!137 &13700002
SkinnedMeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100088}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_Materials:
  - {fileID: 2100000, guid: 03402bb0844a94e4f9ef09f124ae0c8a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300004, guid: a9cfab5a8b4a0a0448926ba5ca880442, type: 3}
  m_Bones:
  - {fileID: 400086}
  - {fileID: 400074}
  - {fileID: 400072}
  - {fileID: 400052}
  - {fileID: 400046}
  - {fileID: 400042}
  - {fileID: 400040}
  - {fileID: 400038}
  - {fileID: 400036}
  - {fileID: 400030}
  - {fileID: 400034}
  - {fileID: 400028}
  - {fileID: 400026}
  - {fileID: 400032}
  - {fileID: 400024}
  - {fileID: 400044}
  - {fileID: 400022}
  - {fileID: 400020}
  - {fileID: 400076}
  - {fileID: 400018}
  - {fileID: 400010}
  - {fileID: 400008}
  - {fileID: 400016}
  - {fileID: 400006}
  - {fileID: 400004}
  - {fileID: 400014}
  - {fileID: 400012}
  - {fileID: 400002}
  - {fileID: 400070}
  - {fileID: 400064}
  - {fileID: 400062}
  - {fileID: 400060}
  - {fileID: 400068}
  - {fileID: 400058}
  - {fileID: 400056}
  - {fileID: 400054}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 400086}
  m_AABB:
    m_Center: {x: -0.07030994, y: -0.027773142, z: 0.012230873}
    m_Extent: {x: 1.0048037, y: 0.85812116, z: 1.2042449}
  m_DirtyAABB: 0
--- !u!137 &13700004
SkinnedMeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100080}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_Materials:
  - {fileID: 2100000, guid: 359754cc85114274b96e79e18b55f692, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300002, guid: a9cfab5a8b4a0a0448926ba5ca880442, type: 3}
  m_Bones:
  - {fileID: 400076}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 400076}
  m_AABB:
    m_Center: {x: -0.21810052, y: 0.008411031, z: 0.063825905}
    m_Extent: {x: 0.22153884, y: 0.046275314, z: 0.12214066}
  m_DirtyAABB: 0
--- !u!137 &13700006
SkinnedMeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100078}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_Materials:
  - {fileID: 2100000, guid: 67e66cd37fc0b794aa0a01c6e5d9a7cb, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300008, guid: a9cfab5a8b4a0a0448926ba5ca880442, type: 3}
  m_Bones:
  - {fileID: 400052}
  - {fileID: 400048}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 400052}
  m_AABB:
    m_Center: {x: -0.3790724, y: 0.014374092, z: 0.028749458}
    m_Extent: {x: 0.19937524, y: 0.27129716, z: 0.20378524}
  m_DirtyAABB: 0
--- !u!137 &13700008
SkinnedMeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100082}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_Materials:
  - {fileID: 2100000, guid: 5eff153ada2024d40ace67fb4049f1b0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: a9cfab5a8b4a0a0448926ba5ca880442, type: 3}
  m_Bones:
  - {fileID: 400076}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 400076}
  m_AABB:
    m_Center: {x: -0.14529443, y: 0.009025268, z: 0.09150079}
    m_Extent: {x: 0.43796444, y: 0.046310235, z: 0.12548113}
  m_DirtyAABB: 0
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100090}
  m_IsPrefabParent: 1
