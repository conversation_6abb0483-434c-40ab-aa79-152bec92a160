%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &125850
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 22425850}
  - component: {fileID: 22325660}
  - component: {fileID: 11426068}
  - component: {fileID: 6193717574850487428}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &22425850
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125850}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 425660}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!223 &22325660
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125850}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &11426068
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125850}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &6193717574850487428
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125850}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 960, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 1
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
--- !u!1 &125852
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 425660}
  - component: {fileID: 11426072}
  - component: {fileID: 11471062}
  - component: {fileID: 11424616}
  m_Layer: 0
  m_Name: Dialogue Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &425660
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125852}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 22425850}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &11426072
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125852}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab8b685e62a9aeb4a9472b30ec2d86d9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialDatabase: {fileID: 0}
  displaySettings:
    conversationOverrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    dialogueUI: {fileID: 101180, guid: 5ba51781a19f8a843abeb2d304d0d26e, type: 3}
    defaultCanvas: {fileID: 22325660}
    localizationSettings:
      language: 
      useSystemLanguage: 0
      textTable: {fileID: 0}
    subtitleSettings:
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      allowPCSubtitleReminders: 0
      skipPCSubtitleAfterResponseMenu: 1
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      requireContinueOnLastLine: 0
      richTextEmphases: 1
      informSequenceStartAndEnd: 0
    cameraSettings:
      sequencerCamera: {fileID: 0}
      alternateCameraObject: {fileID: 0}
      cameraAngles: {fileID: 0}
      keepCameraPositionAtConversationEnd: 0
      showSubtitleOnEmptyContinue: 0
      defaultSequence: Delay({{end}})
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      entrytagFormat: 0
      reportMissingAudioFiles: 0
      disableInternalSequencerCommands: 0
    inputSettings:
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      responseTimeoutAction: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      qteButtons:
      - Fire1
      - Fire2
      cancel:
        key: 0
        buttonName: 
      cancelConversation:
        key: 0
        buttonName: 
    barkSettings:
      allowBarksDuringConversations: 1
      barkCharsPerSecond: 0
      minBarkSeconds: 0
      defaultBarkSequence: 
    alertSettings:
      allowAlertsDuringConversations: 0
      alertCheckFrequency: 0
      alertCharsPerSecond: 0
      minAlertSeconds: 0
  persistentDataSettings:
    recordPersistentDataOn: 1
    includeActorData: 1
    includeAllItemData: 0
    includeLocationData: 0
    includeStatusAndRelationshipData: 1
    includeAllConversationFields: 0
    saveConversationSimStatusWithField: 
    saveDialogueEntrySimStatusWithField: 
    asyncGameObjectBatchSize: 1000
    asyncDialogueEntryBatchSize: 100
    initializeNewVariables: 1
  allowSimultaneousConversations: 0
  interruptActiveConversations: 0
  stopEvaluationAtFirstValid: 0
  reevaluateLinksAfterSubtitle: 0
  useLinearGroupMode: 0
  includeSimStatus: 0
  instantiateDatabase: 1
  preloadResources: 1
  warmUpConversationController: 0
  dontHideImmediateDuringWarmup: 0
  dontDestroyOnLoad: 1
  allowOnlyOneInstance: 1
  onStartTriggerWaitForSaveDataApplied: 0
  dialogueTimeMode: 0
  debugLevel: 2
--- !u!114 &11471062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125852}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3b24a5606b164c84db0d3ed3ee8256e1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_parent: {fileID: 22425850}
  m_prefabs:
  - {fileID: 168688, guid: a5c98dfcec76e1847a0aa8d7c8a93459, type: 3}
  - {fileID: 121952, guid: 720348da784b8fa49a7c48365da6f1b1, type: 3}
  - {fileID: 162630, guid: f40d1072f56eeb04f9c104b7c4a8811f, type: 3}
  m_position: 0
--- !u!114 &11424616
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125852}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dddab24af7a02a54c9631fd5c532d7c4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  inputDevice: 0
  joystickKeyCodesToCheck: 4a0100004b0100004c01000051010000
  joystickButtonsToCheck: []
  joystickAxesToCheck: []
  joystickAxisThreshold: 0.5
  keyButtonsToCheck: []
  keyCodesToCheck: 1b000000
  keyInputSwitchesModeTo: 1
  alwaysAutoFocus: 0
  detectMouseControl: 1
  mouseMoveThreshold: 0.1
  controlCursorState: 1
  enforceCursorOnPause: 0
  controlGraphicRaycasters: 0
  backKeyCodes: 4b010000
  backButtons:
  - Cancel
  submitButton: Submit
  singleton: 1
  onUseKeyboard:
    m_PersistentCalls:
      m_Calls: []
  onUseJoystick:
    m_PersistentCalls:
      m_Calls: []
  onUseMouse:
    m_PersistentCalls:
      m_Calls: []
  onUseTouch:
    m_PersistentCalls:
      m_Calls: []
