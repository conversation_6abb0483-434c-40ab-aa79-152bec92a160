# 架构重构总结

## 重构目标
将违反单一职责原则的ScriptableObject架构重构为符合设计原则的服务导向架构。

## 问题分析
原始架构中，ScriptableObject文件承担了过多的业务逻辑责任：
- **CoreGameDataSO**: 包含精力消耗、温度修改、粉丝增长计算等业务逻辑
- **AudienceDataSO**: 包含DTV增加、故事块解锁、关键词收集等业务逻辑  
- **KeywordDataSO**: 包含关键词演化、组合检查等业务逻辑

这违反了单一职责原则，ScriptableObject应该主要负责数据存储和简单的数据访问。

## 重构方案

### 1. 服务层架构
创建专门的服务类来处理业务逻辑：

#### GameDataService.cs
- **职责**: 核心游戏数据的业务逻辑
- **主要方法**:
  - `ConsumeStamina(float amount)` - 精力消耗逻辑
  - `RestoreStamina(float amount)` - 精力恢复逻辑
  - `ModifyWarmth(float delta)` - 温度修改逻辑
  - `AddFans(int amount)` - 粉丝增长逻辑
  - `ModifyMoney(float delta)` - 金钱修改逻辑
  - `CalculateFanGrowth()` - 粉丝增长计算
  - `CalculateBaseIncome()` - 基础收入计算

#### AudienceService.cs
- **职责**: 听众相关的业务逻辑
- **主要方法**:
  - `IncreaseDTV(AudienceDataSO audience, int amount)` - DTV增加逻辑
  - `UnlockStoryBlock(AudienceDataSO audience, int level)` - 故事块解锁逻辑
  - `CollectKeyword(AudienceDataSO audience, KeywordDataSO keyword)` - 关键词收集逻辑
  - `UpdateAudienceState(AudienceDataSO audience)` - 听众状态更新
  - `CleanupExpiredKeywords(AudienceDataSO audience)` - 过期关键词清理
  - `RecordInteraction(AudienceDataSO audience, bool isSuccessful, bool isResonance)` - 互动记录

#### KeywordService.cs
- **职责**: 关键词相关的业务逻辑
- **主要方法**:
  - `CreateKeyword(string keywordText, string audienceId)` - 关键词创建
  - `InitializeKeyword(KeywordDataSO keyword)` - 关键词初始化
  - `TryEvolveKeyword(KeywordDataSO keyword)` - 关键词演化逻辑
  - `ExecuteCombination(KeywordCombinationSO combination, List<KeywordDataSO> usedKeywords)` - 关键词组合逻辑
  - `IsKeywordExpired(KeywordDataSO keyword)` - 过期检查
  - `CleanupExpiredKeywords()` - 过期关键词清理

#### StoryService.cs
- **职责**: 故事和对话相关的业务逻辑
- **主要方法**:
  - `CanUnlockStoryBlock(StoryBlockSO storyBlock, AudienceDataSO audience)` - 故事块解锁条件检查
  - `ExecuteStoryBlockRewards(StoryBlockSO storyBlock, AudienceDataSO audience)` - 故事块奖励发放
  - `ExecuteDialogueOption(DialogueOptionSO option, AudienceDataSO audience, List<KeywordDataSO> usedKeywords)` - 对话选项执行
  - `CalculateDialogueSuccessRate(DialogueOptionSO option, AudienceDataSO audience, List<KeywordDataSO> usedKeywords)` - 对话成功率计算

#### MemoryService.cs
- **职责**: 心绪内阁回忆系统的业务逻辑
- **主要方法**:
  - `ActivateMemory(MemoryDataSO memory)` - 回忆激活逻辑
  - `SuppressMemory(MemoryDataSO memory)` - 回忆压制逻辑
  - `CheckAllMemoryUnlocks()` - 回忆解锁条件检查
  - `UpdateMemoryStates()` - 回忆状态更新

### 2. ScriptableObject重构
将ScriptableObject类重构为纯数据容器：

#### CoreGameDataSO.cs
- **保留**: 数据字段和简单的数据访问方法
- **移除**: 所有业务逻辑方法（ConsumeStamina, ModifyWarmth, CalculateFanGrowth等）
- **新增**: 数据状态查询方法（GetStaminaPercentage, GetWarmthStatusDescription等）

#### AudienceDataSO.cs  
- **保留**: 数据字段和简单的数据访问方法
- **移除**: 所有业务逻辑方法（IncreaseDTV, UnlockStoryBlock, CollectKeyword等）
- **保留**: 数据查询方法（HasKeyword, GetValidKeywords, GetStoryBlock等）

#### KeywordDataSO.cs
- **保留**: 数据字段和属性访问
- **移除**: 所有业务逻辑方法（Initialize, TryEvolve, CanCombineWith等）
- **保留**: 数据状态属性（IsExpired, CanEvolve等）

### 3. GameManager更新
更新GameManager使其通过服务类而不是直接调用ScriptableObject的业务逻辑：

```csharp
// 旧方式
audience.CollectKeyword(keyword);
coreGameData.ConsumeStamina(10f);

// 新方式  
AudienceService.Instance?.CollectKeyword(audience, keyword);
GameDataService.Instance?.ConsumeStamina(10f);
```

## 架构优势

### 1. 单一职责原则
- **ScriptableObject**: 专注于数据存储和简单访问
- **Service类**: 专注于业务逻辑处理
- **Manager类**: 专注于系统协调和事件管理

### 2. 可维护性提升
- 业务逻辑集中在服务类中，便于修改和扩展
- ScriptableObject结构清晰，便于设计师配置
- 代码职责明确，降低耦合度

### 3. 可测试性提升
- 业务逻辑独立于Unity对象，便于单元测试
- 服务类可以独立测试，不依赖ScriptableObject实例
- 模拟和依赖注入更容易实现

### 4. 扩展性提升
- 新增业务逻辑只需扩展对应的服务类
- 服务类之间可以相互调用，支持复杂的业务流程
- 便于实现设计模式（策略模式、观察者模式等）

## 文件结构

```
Assets/Scripts/
├── Services/
│   ├── GameDataService.cs      # 核心游戏数据服务
│   ├── AudienceService.cs      # 听众数据服务  
│   ├── KeywordService.cs       # 关键词服务
│   ├── StoryService.cs         # 故事服务
│   └── MemoryService.cs        # 回忆服务
├── ScriptableObjects/
│   ├── CoreGameDataSO.cs       # 核心游戏数据（纯数据）
│   ├── AudienceDataSO.cs       # 听众数据（纯数据）
│   ├── KeywordDataSO.cs        # 关键词数据（纯数据）
│   └── ...
├── Managers/
│   └── GameManager.cs          # 游戏管理器（已更新）
└── Save Load/
    └── ...                     # 保存加载系统
```

## 迁移指南

### 对于程序员
1. 使用服务类的静态Instance访问业务逻辑
2. 不再直接调用ScriptableObject的业务逻辑方法
3. 通过服务类进行数据修改，保持数据一致性

### 对于设计师
1. ScriptableObject配置方式保持不变
2. 数据字段和结构保持兼容
3. Inspector面板功能正常使用

## 总结
本次重构成功将混合架构转换为清晰的分层架构，ScriptableObject专注于数据存储，Service类专注于业务逻辑，Manager类专注于系统协调。这种架构更符合SOLID原则，提高了代码的可维护性、可测试性和可扩展性。
