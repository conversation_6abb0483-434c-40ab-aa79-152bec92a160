# 《深夜连线》/ Midnight Connection

> *一款关于深夜广播与情感连接的 2D 横版叙事型广播模拟游戏*

![Unity](https://img.shields.io/badge/Unity-2022.3+-000000?style=flat&logo=unity&logoColor=white)
![C#](https://img.shields.io/badge/C%23-239120?style=flat&logo=c-sharp&logoColor=white)
![Platform](https://img.shields.io/badge/Platform-PC%20%7C%20Mac-lightgrey)
![License](https://img.shields.io/badge/License-MIT-blue.svg)

## 📖 游戏简介

《深夜连线》是一款围绕深夜广播与情感连接展开的 2D 横版叙事型广播模拟游戏。玩家扮演深夜电台主持人，驾驶移动直播车在夜晚穿行于城市，通过深夜广播与不同听众展开情感连接。

## ✨ 核心特色

### 🎮 创新玩法机制

- **一对一连线**：深度对话收集关键词，解锁听众故事
- **留言板互动**：弹幕式留言系统，区分有意义与无意义互动
- **关键词推理**：通过关键词组合与回应，揭示听众内心世界
- **故事块系统**：分级解锁听众故事，模拟真实信任建立过程

## 🎯 核心数值系统 (V3-极简版)

### 核心数值

| 数值名称 | 定义 | 范围 | 设计理念 |
|---------|------|------|----------|
| **精力 (Stamina)** | 主角的行动力与身心状态综合体现 | 0~100 | 核心状态：唯一的玩家状态数值，低精力触发负面效果 |
| **温度 (Warmth)** | 当晚直播的质量或精彩程度 | 1~10 | 核心产出：唯一的直播产出衡量标准 |
| **总粉丝数 (Total Fans)** | 直播的总体影响力 | 1000~无上限 | 长期回报：玩家经营成果最直观体现 |
| **金钱 (Money)** | 游戏内硬通货 | 0~无上限 | 策略放大器：用于购买道具和升级设备 |
| **深度倾诉值 (DTV)** | 与单个听众的情感链接深度 | 0~5 | 目标导向：将关系进展具象化 |

### 核心公式

#### 精力系统
- **基础消耗**：核心操作消耗固定精力
- **精力惩罚**：精力低于30时，所有行动消耗 ×1.5
- **恢复方式**：休息一晚或使用道具

#### 粉丝增长公式
```
Δ总粉 = (基础增长 + 粉丝基数增长) × 温度乘数
- 基础增长 = 50 × (1000 / 总粉丝数)
- 粉丝基数增长 = 总粉丝数 × 0.02
- 温度乘数 = (温度 / 5)²
```

#### 金钱收入
- **基础收入** = 总粉丝数 × 0.1 × (0.7 + 随机数 × 0.6)
- **连线打赏** = 故事块等级 × 10

## 🎮 核心玩法系统

### 故事块系统
- **Level 1-5**：核心故事内容，通过深度倾诉值解锁
- **Level 6**：特殊后日谈，通过特定条件触发
- **深度倾诉值 (DTV)**：0-5，衡量听众信任度

### 一对一连线
- **触发条件**：听众DTV满足要求
- **交互界面**：主角/听众头像，对话选择
- **核心机制**：推导/回应选项，触发共鸣时刻

### 关键词与画像系统
- **关键词获取**：对话中高亮词语点击收集
- **动态特性**：易逝关键词(1-2晚保质期)与演化关键词
- **推理模式**：
  - 关键词回应：考验记忆与关怀
  - 矛盾质询：考验洞察力与博弈
  - 情感共鸣：考验同理心
  - 沉默留白：考验耐心与智慧

## 🛠️ 技术栈

- **游戏引擎**：Unity 2022.3 LTS
- **编程语言**：C#
- **UI 框架**：Unity UI + TextMeshPro
- **对话系统**：Pixel Crushers Dialogue System
- **任务系统**：Quest Machine
- **本地化**：Unity Localization Package
- **资源管理**：Unity Addressable Assets
- **版本控制**：Git

## 📁 项目结构

```
Assets/
├── Scripts/
│   ├── Data/              # 数据结构
│   ├── Managers/          # 管理器
│   ├── Player/            # 玩家控制
│   ├── NPC/              # NPC系统
│   ├── Quests/           # 任务系统
│   ├── Debugging/         # 调试工具
│   └── Enums/            # 枚举定义
├── Scenes/               # 场景文件
├── Prefabs/              # 预制体
├── Audio/                # 音频资源
├── Textures/             # 贴图资源
└── Settings/             # 配置文件
```

## 🚀 快速开始

### 环境要求

- Unity 2022.3 LTS 或更高版本
- .NET Framework 4.7.1+
- Windows 10+ / macOS 10.15+

### 运行步骤

1. 克隆仓库

```bash
git clone https://github.com/yourusername/midnight-broadcasting.git
```

2. 用 Unity 打开项目

3. 打开主场景 `Assets/Scenes/MainScene.unity`

4. 点击播放按钮开始测试

## 📋 开发进度

### 已完成

- [x] 基础项目架构搭建
- [x] 核心系统设计文档
- [x] Unity 项目初始化
- [x] 基础玩家控制器 (CarController)

### 进行中

- [ ] 核心数值系统开发 (GameStatsManager)
- [ ] 对话系统集成
- [ ] 关键词收集系统

### 计划中

- [ ] 人物画像系统
- [ ] 留言板系统实现
- [ ] 故事块系统
- [ ] 一对一连线系统
- [ ] 城市场景设计
- [ ] 音频系统集成
- [ ] UI/UX 设计实现

## 🤝 贡献指南

我们欢迎各种形式的贡献！

### 开发规范

- 遵循 [Unity C# 编码规范](https://unity.com/how-to/naming-and-code-style-tips-c-scripting-unity)
- 使用 PascalCase 命名公共成员，camelCase 命名私有成员
- 变量命名：`m_VariableName`，常量：`c_ConstantName`
- 使用 `#region` 组织代码块

### 提交流程

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add some amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 提交 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

*"每一次通话，都是一场心灵的潜入；每一个关键词，都是一块理解的拼图。"*
