using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 回忆服务
/// 负责处理心绪内阁回忆系统的业务逻辑
/// </summary>
public class MemoryService : MonoBehaviour
{
    [Header("=== 回忆数据 ===")]
    [Tooltip("所有回忆模板")]
    public List<MemoryDataSO> memoryTemplates = new List<MemoryDataSO>();

    // 单例模式
    public static MemoryService Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    #endregion

    #region 回忆解锁

    /// <summary>
    /// 检查回忆是否可以解锁
    /// </summary>
    public bool CanUnlockMemory(MemoryDataSO memory)
    {
        if (memory == null || memory.isUnlocked) return false;

        var coreData = GameDataService.Instance?.coreGameData;
        if (coreData == null) return false;

        // 检查夜晚要求
        if (coreData.currentNight < memory.requiredNight) return false;

        // 检查关键词要求
        if (memory.unlockKeywords != null && memory.unlockKeywords.Length > 0)
        {
            foreach (var requiredKeyword in memory.unlockKeywords)
            {
                if (requiredKeyword != null)
                {
                    bool hasKeyword = HasKeywordInAnyAudience(requiredKeyword.keyword);
                    if (!hasKeyword) return false;
                }
            }
        }

        // 检查听众要求
        if (memory.unlockAudiences != null && memory.unlockAudiences.Length > 0)
        {
            foreach (var requiredAudience in memory.unlockAudiences)
            {
                if (requiredAudience != null)
                {
                    bool hasAudience = HasInteractedWithAudience(requiredAudience.audienceId);
                    if (!hasAudience) return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 解锁回忆
    /// </summary>
    public bool UnlockMemory(MemoryDataSO memory)
    {
        if (!CanUnlockMemory(memory)) return false;

        memory.isUnlocked = true;
        memory.unlockTime = DateTime.Now;

        Debug.Log($"回忆解锁：{memory.memoryTitle}");
        return true;
    }

    /// <summary>
    /// 检查所有回忆的解锁条件
    /// </summary>
    public void CheckAllMemoryUnlocks()
    {
        foreach (var memory in memoryTemplates)
        {
            if (memory != null && !memory.isUnlocked)
            {
                if (CanUnlockMemory(memory))
                {
                    UnlockMemory(memory);
                }
            }
        }
    }

    #endregion

    #region 回忆激活

    /// <summary>
    /// 激活回忆
    /// </summary>
    public bool ActivateMemory(MemoryDataSO memory)
    {
        if (memory == null || !memory.isUnlocked || memory.isActive || memory.isSuppressed) 
            return false;

        // 检查是否有其他回忆正在激活
        if (HasActiveMemory())
        {
            Debug.LogWarning("已有回忆处于激活状态");
            return false;
        }

        // 激活回忆
        memory.isActive = true;
        memory.activationTime = DateTime.Now;

        // 应用回忆效果
        ApplyMemoryEffects(memory);

        // 设置持续时间
        if (memory.durationMinutes > 0)
        {
            memory.expiryTime = DateTime.Now.AddMinutes(memory.durationMinutes);
        }

        Debug.Log($"回忆激活：{memory.memoryTitle}");
        return true;
    }

    /// <summary>
    /// 压制回忆
    /// </summary>
    public bool SuppressMemory(MemoryDataSO memory)
    {
        if (memory == null || !memory.isUnlocked) return false;

        // 检查精力是否足够
        if (!GameDataService.Instance?.CanPerformAction(memory.suppressionCost) ?? true)
        {
            Debug.LogWarning("精力不足，无法压制回忆");
            return false;
        }

        // 消耗精力
        GameDataService.Instance?.ConsumeStamina(memory.suppressionCost);

        // 压制回忆
        memory.isSuppressed = true;
        memory.isActive = false;
        memory.suppressionTime = DateTime.Now;

        Debug.Log($"回忆压制：{memory.memoryTitle}，消耗精力：{memory.suppressionCost}");
        return true;
    }

    /// <summary>
    /// 取消压制回忆
    /// </summary>
    public bool UnsuppressMemory(MemoryDataSO memory)
    {
        if (memory == null || !memory.isSuppressed) return false;

        memory.isSuppressed = false;
        Debug.Log($"回忆压制解除：{memory.memoryTitle}");
        return true;
    }

    #endregion

    #region 回忆效果

    /// <summary>
    /// 应用回忆效果
    /// </summary>
    private void ApplyMemoryEffects(MemoryDataSO memory)
    {
        if (memory == null) return;

        // 应用精力效果
        if (memory.staminaEffect != 0)
        {
            if (memory.staminaEffect > 0)
                GameDataService.Instance?.RestoreStamina(memory.staminaEffect);
            else
                GameDataService.Instance?.ConsumeStamina(-memory.staminaEffect);
        }

        // 应用温度效果
        if (memory.warmthEffect != 0)
        {
            GameDataService.Instance?.ModifyWarmth(memory.warmthEffect);
        }

        Debug.Log($"回忆效果应用：{memory.memoryTitle} - 精力{memory.staminaEffect:+0;-0;0}，温度{memory.warmthEffect:+0.0;-0.0;0.0}");
    }

    /// <summary>
    /// 更新回忆状态
    /// </summary>
    public void UpdateMemoryStates()
    {
        foreach (var memory in memoryTemplates)
        {
            if (memory != null && memory.isActive)
            {
                // 检查是否过期
                if (memory.durationMinutes > 0 && DateTime.Now > memory.expiryTime)
                {
                    DeactivateMemory(memory);
                }
            }
        }
    }

    /// <summary>
    /// 停用回忆
    /// </summary>
    private void DeactivateMemory(MemoryDataSO memory)
    {
        if (memory == null) return;

        memory.isActive = false;
        Debug.Log($"回忆自动停用：{memory.memoryTitle}");
    }

    #endregion

    #region 回忆查询

    /// <summary>
    /// 检查是否有激活的回忆
    /// </summary>
    public bool HasActiveMemory()
    {
        return memoryTemplates.Any(m => m != null && m.isActive);
    }

    /// <summary>
    /// 获取当前激活的回忆
    /// </summary>
    public MemoryDataSO GetActiveMemory()
    {
        return memoryTemplates.FirstOrDefault(m => m != null && m.isActive);
    }

    /// <summary>
    /// 获取已解锁的回忆列表
    /// </summary>
    public List<MemoryDataSO> GetUnlockedMemories()
    {
        return memoryTemplates.Where(m => m != null && m.isUnlocked).ToList();
    }

    /// <summary>
    /// 获取可激活的回忆列表
    /// </summary>
    public List<MemoryDataSO> GetActivatableMemories()
    {
        return memoryTemplates.Where(m => 
            m != null && 
            m.isUnlocked && 
            !m.isActive && 
            !m.isSuppressed).ToList();
    }

    /// <summary>
    /// 根据类型获取回忆
    /// </summary>
    public List<MemoryDataSO> GetMemoriesByType(MemoryType type)
    {
        return memoryTemplates.Where(m => m != null && m.memoryType == type).ToList();
    }

    /// <summary>
    /// 获取负面回忆列表
    /// </summary>
    public List<MemoryDataSO> GetNegativeMemories()
    {
        return memoryTemplates.Where(m => m != null && m.isNegative).ToList();
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 检查是否在任何听众中拥有指定关键词
    /// </summary>
    private bool HasKeywordInAnyAudience(string keywordText)
    {
        var audienceService = AudienceService.Instance;
        if (audienceService == null) return false;

        foreach (var audience in audienceService.allAudiences)
        {
            if (audienceService.HasKeyword(audience, keywordText))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 检查是否与指定听众有过互动
    /// </summary>
    private bool HasInteractedWithAudience(string audienceId)
    {
        var audience = AudienceService.Instance?.FindAudienceById(audienceId);
        return audience != null && audience.totalInteractions > 0;
    }

    /// <summary>
    /// 获取回忆状态描述
    /// </summary>
    public string GetMemoryStatusDescription(MemoryDataSO memory)
    {
        if (memory == null) return "无效";
        if (!memory.isUnlocked) return "未解锁";
        if (memory.isSuppressed) return "已压制";
        if (memory.isActive)
        {
            if (memory.durationMinutes > 0)
            {
                var remaining = memory.expiryTime - DateTime.Now;
                if (remaining.TotalMinutes > 0)
                {
                    return $"激活中 (剩余 {remaining.TotalMinutes:F0} 分钟)";
                }
            }
            return "激活中";
        }
        return "可激活";
    }

    #endregion

    #region 数据验证

    /// <summary>
    /// 验证回忆数据
    /// </summary>
    public bool ValidateMemoryData(MemoryDataSO memory)
    {
        if (memory == null) return false;

        bool isValid = true;

        // 检查基础数据
        if (string.IsNullOrEmpty(memory.memoryTitle))
        {
            Debug.LogWarning($"回忆缺少标题");
            isValid = false;
        }

        // 检查数值范围
        if (memory.staminaEffect < -20f || memory.staminaEffect > 20f)
        {
            Debug.LogWarning($"回忆 {memory.memoryTitle} 精力效果异常：{memory.staminaEffect}");
            memory.staminaEffect = Mathf.Clamp(memory.staminaEffect, -20f, 20f);
            isValid = false;
        }

        if (memory.warmthEffect < -2f || memory.warmthEffect > 2f)
        {
            Debug.LogWarning($"回忆 {memory.memoryTitle} 温度效果异常：{memory.warmthEffect}");
            memory.warmthEffect = Mathf.Clamp(memory.warmthEffect, -2f, 2f);
            isValid = false;
        }

        if (memory.suppressionCost < 0f || memory.suppressionCost > 50f)
        {
            Debug.LogWarning($"回忆 {memory.memoryTitle} 压制成本异常：{memory.suppressionCost}");
            memory.suppressionCost = Mathf.Clamp(memory.suppressionCost, 0f, 50f);
            isValid = false;
        }

        // 清理空引用
        if (memory.unlockKeywords != null)
        {
            memory.unlockKeywords = memory.unlockKeywords.Where(k => k != null).ToArray();
        }

        if (memory.unlockAudiences != null)
        {
            memory.unlockAudiences = memory.unlockAudiences.Where(a => a != null).ToArray();
        }

        return isValid;
    }

    /// <summary>
    /// 验证所有回忆数据
    /// </summary>
    public void ValidateAllMemoryData()
    {
        foreach (var memory in memoryTemplates)
        {
            ValidateMemoryData(memory);
        }
    }

    #endregion
}
