using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 游戏数据服务
/// 负责处理核心游戏数据的业务逻辑
/// </summary>
public class GameDataService : MonoBehaviour
{
    [Header("=== 数据引用 ===")]
    [Tooltip("核心游戏数据")]
    public CoreGameDataSO coreGameData;

    // 单例模式
    public static GameDataService Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    #endregion

    #region 精力系统

    /// <summary>
    /// 消耗精力
    /// </summary>
    public bool ConsumeStamina(float amount)
    {
        if (coreGameData == null) return false;
        
        if (coreGameData.currentStamina >= amount)
        {
            coreGameData.currentStamina = Mathf.Max(0, coreGameData.currentStamina - amount);
            
            // 检查低精力状态
            if (coreGameData.currentStamina <= 20f && !coreGameData.activeNegativeStates.Contains("低精力状态"))
            {
                OnLowStaminaState();
            }
            
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// 恢复精力
    /// </summary>
    public void RestoreStamina(float amount)
    {
        if (coreGameData == null) return;
        
        coreGameData.currentStamina = Mathf.Min(100f, coreGameData.currentStamina + amount);
        
        // 移除低精力状态
        if (coreGameData.currentStamina > 20f)
        {
            coreGameData.activeNegativeStates.Remove("低精力状态");
        }
    }

    /// <summary>
    /// 检查是否可以执行需要精力的操作
    /// </summary>
    public bool CanPerformAction(float requiredStamina)
    {
        return coreGameData != null && coreGameData.currentStamina >= requiredStamina;
    }

    #endregion

    #region 温度系统

    /// <summary>
    /// 修改温度值
    /// </summary>
    public void ModifyWarmth(float delta)
    {
        if (coreGameData == null) return;
        
        float oldWarmth = coreGameData.currentWarmth;
        coreGameData.currentWarmth = Mathf.Clamp(coreGameData.currentWarmth + delta, 1f, 10f);
        
        // 温度变化事件
        if (Mathf.Abs(oldWarmth - coreGameData.currentWarmth) > 0.1f)
        {
            OnWarmthChanged(oldWarmth, coreGameData.currentWarmth);
        }
    }

    /// <summary>
    /// 获取温度等级描述
    /// </summary>
    public string GetWarmthDescription()
    {
        if (coreGameData == null) return "未知";
        
        return coreGameData.currentWarmth switch
        {
            <= 2f => "冰冷",
            <= 4f => "寒冷", 
            <= 6f => "凉爽",
            <= 8f => "温暖",
            _ => "炽热"
        };
    }

    #endregion

    #region 粉丝系统

    /// <summary>
    /// 计算粉丝增长
    /// </summary>
    public int CalculateFanGrowth()
    {
        if (coreGameData == null) return 0;
        
        // 基础增长 = 50 * (1000 / 总粉丝数)
        float baseGrowth = 50f * (1000f / coreGameData.totalFans);
        
        // 粉丝基数增长 = 总粉丝数 * 0.02
        float fanBaseGrowth = coreGameData.totalFans * 0.02f;
        
        // 温度乘数 = (温度 / 5)²
        float warmthMultiplier = Mathf.Pow(coreGameData.currentWarmth / 5f, 2f);
        
        // 总增长
        int totalGrowth = Mathf.RoundToInt((baseGrowth + fanBaseGrowth) * warmthMultiplier);
        
        return totalGrowth;
    }

    /// <summary>
    /// 增加粉丝数
    /// </summary>
    public void AddFans(int amount)
    {
        if (coreGameData == null) return;
        
        int oldFans = coreGameData.totalFans;
        coreGameData.totalFans = Mathf.Max(1000, coreGameData.totalFans + amount);
        
        Debug.Log($"粉丝数变化：{oldFans} -> {coreGameData.totalFans} (+{amount})");
    }

    /// <summary>
    /// 应用每日粉丝增长
    /// </summary>
    public void ApplyDailyFanGrowth()
    {
        int growth = CalculateFanGrowth();
        AddFans(growth);
    }

    #endregion

    #region 金钱系统

    /// <summary>
    /// 计算基础收入
    /// </summary>
    public float CalculateBaseIncome()
    {
        if (coreGameData == null) return 0f;
        
        // 基础收入 = 总粉丝数 * 0.1 * (0.7 + 随机数 * 0.6)
        float randomMultiplier = 0.7f + UnityEngine.Random.value * 0.6f;
        return coreGameData.totalFans * 0.1f * randomMultiplier;
    }

    /// <summary>
    /// 修改金钱
    /// </summary>
    public bool ModifyMoney(float delta)
    {
        if (coreGameData == null) return false;
        
        float newAmount = coreGameData.money + delta;
        if (newAmount < 0) return false; // 金钱不足
        
        coreGameData.money = newAmount;
        Debug.Log($"金钱变化：{delta:F2}，当前：{coreGameData.money:F2}");
        return true;
    }

    /// <summary>
    /// 检查是否有足够金钱
    /// </summary>
    public bool HasEnoughMoney(float amount)
    {
        return coreGameData != null && coreGameData.money >= amount;
    }

    #endregion

    #region 游戏进度

    /// <summary>
    /// 进入下一个夜晚
    /// </summary>
    public void AdvanceToNextNight()
    {
        if (coreGameData == null) return;
        
        coreGameData.currentNight++;
        coreGameData.currentPhase = GamePhase.Preparation;
        coreGameData.LastPlayTime = DateTime.Now;
        
        // 重置每晚的状态
        coreGameData.currentWarmth = 5f;
        
        // 应用每日效果
        ApplyDailyFanGrowth();
        float dailyIncome = CalculateBaseIncome();
        ModifyMoney(dailyIncome);
        
        Debug.Log($"进入第 {coreGameData.currentNight} 夜，获得收入：{dailyIncome:F2}");
    }

    /// <summary>
    /// 检查功能是否解锁
    /// </summary>
    public bool IsFeatureUnlocked(string featureName)
    {
        if (coreGameData == null) return false;
        
        return featureName switch
        {
            "心绪内阁" => coreGameData.currentNight >= 3,
            "关键词组合" => coreGameData.currentNight >= 5,
            "高级推理" => coreGameData.currentNight >= 7,
            _ => true
        };
    }

    #endregion

    #region 状态管理

    /// <summary>
    /// 添加负面状态
    /// </summary>
    public void AddNegativeState(string stateName)
    {
        if (coreGameData == null) return;
        
        if (!coreGameData.activeNegativeStates.Contains(stateName))
        {
            coreGameData.activeNegativeStates.Add(stateName);
            Debug.Log($"添加负面状态：{stateName}");
        }
    }

    /// <summary>
    /// 移除负面状态
    /// </summary>
    public void RemoveNegativeState(string stateName)
    {
        if (coreGameData == null) return;
        
        if (coreGameData.activeNegativeStates.Remove(stateName))
        {
            Debug.Log($"移除负面状态：{stateName}");
        }
    }

    /// <summary>
    /// 检查是否有特定负面状态
    /// </summary>
    public bool HasNegativeState(string stateName)
    {
        return coreGameData != null && coreGameData.activeNegativeStates.Contains(stateName);
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 低精力状态处理
    /// </summary>
    private void OnLowStaminaState()
    {
        AddNegativeState("低精力状态");
        Debug.Log("警告：精力过低，所有操作消耗增加！");
    }

    /// <summary>
    /// 温度变化事件
    /// </summary>
    private void OnWarmthChanged(float oldValue, float newValue)
    {
        Debug.Log($"温度变化：{oldValue:F1} -> {newValue:F1} ({GetWarmthDescription()})");
    }

    #endregion

    #region 数据验证

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    public bool ValidateData()
    {
        if (coreGameData == null)
        {
            Debug.LogError("核心游戏数据为空");
            return false;
        }

        bool isValid = true;

        // 检查数值范围
        if (coreGameData.currentStamina < 0 || coreGameData.currentStamina > 100)
        {
            Debug.LogWarning($"精力值异常：{coreGameData.currentStamina}");
            coreGameData.currentStamina = Mathf.Clamp(coreGameData.currentStamina, 0f, 100f);
            isValid = false;
        }

        if (coreGameData.currentWarmth < 1 || coreGameData.currentWarmth > 10)
        {
            Debug.LogWarning($"温度值异常：{coreGameData.currentWarmth}");
            coreGameData.currentWarmth = Mathf.Clamp(coreGameData.currentWarmth, 1f, 10f);
            isValid = false;
        }

        if (coreGameData.totalFans < 1000)
        {
            Debug.LogWarning($"粉丝数异常：{coreGameData.totalFans}");
            coreGameData.totalFans = Mathf.Max(1000, coreGameData.totalFans);
            isValid = false;
        }

        if (coreGameData.money < 0)
        {
            Debug.LogWarning($"金钱异常：{coreGameData.money}");
            coreGameData.money = Mathf.Max(0, coreGameData.money);
            isValid = false;
        }

        return isValid;
    }

    #endregion
}
