// Copyright (c) Midnight-Broadcasting. All rights reserved.

using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 事件通道的核心，作为ScriptableObject资源创建
/// </summary>
[CreateAssetMenu(fileName = "NewGameEvent", menuName = "Events/Game Event")]
public class GameEvent : ScriptableObject
{
    #region Private Fields
    private readonly List<GameEventListener> m_listeners = new List<GameEventListener>();
    #endregion

    #region Public Methods
    /// <summary>
    /// 触发事件，通知所有监听器
    /// </summary>
    public void Raise()
    {
        // 从后向前遍历，避免在回调中移除监听器时的问题
        for (int i = m_listeners.Count - 1; i >= 0; i--)
        {
            m_listeners[i].OnEventRaised();
        }
    }

    /// <summary>
    /// 注册监听器
    /// </summary>
    /// <param name="_listener">要注册的监听器</param>
    public void RegisterListener(GameEventListener _listener)
    {
        if (!m_listeners.Contains(_listener))
        {
            m_listeners.Add(_listener);
        }
    }

    /// <summary>
    /// 注销监听器
    /// </summary>
    /// <param name="_listener">要注销的监听器</param>
    public void UnregisterListener(GameEventListener _listener)
    {
        if (m_listeners.Contains(_listener))
        {
            m_listeners.Remove(_listener);
        }
    }
    #endregion
}