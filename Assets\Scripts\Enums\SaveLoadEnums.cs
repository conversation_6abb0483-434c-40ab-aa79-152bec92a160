using System;

/// <summary>
/// 关键词类型枚举
/// </summary>
public enum KeywordType
{
    Basic,          // 基础关键词
    Emotional,      // 情感关键词
    Temporal,       // 易逝关键词
    Evolved,        // 演化关键词
    Conclusion      // 结论关键词（通过组合生成）
}

/// <summary>
/// 故事块等级
/// </summary>
public enum StoryBlockLevel
{
    Level1 = 1,
    Level2 = 2,
    Level3 = 3,
    Level4 = 4,
    Level5 = 5,
    Level6 = 6     // 特殊后日谈
}

/// <summary>
/// 对话选项类型
/// </summary>
public enum DialogueOptionType
{
    Normal,         // 普通选项
    Numerical,      // 数值影响选项
    Branch,         // 分支选项
    KeywordResponse,    // 关键词回应
    Contradiction,      // 矛盾质询
    EmotionalResonance, // 情感共鸣
    MeaningfulSilence   // 沉默留白
}

/// <summary>
/// 存档类型
/// </summary>
public enum SaveType
{
    Manual,         // 手动存档
    Auto,           // 自动存档
    Checkpoint,     // 检查点存档
    Backup          // 备份存档
}

/// <summary>
/// 游戏阶段
/// </summary>
public enum GamePhase
{
    Preparation,    // 准备阶段
    Broadcasting,   // 直播阶段
    Connection,     // 连线阶段
    Settlement,     // 结算阶段
    Rest           // 休息阶段
}

/// <summary>
/// 听众状态
/// </summary>
public enum AudienceState
{
    Unknown,        // 未知状态
    Discovered,     // 已发现
    Contacted,      // 已联系
    Trusted,        // 已信任
    Intimate,       // 亲密关系
    Completed       // 故事完成
}
