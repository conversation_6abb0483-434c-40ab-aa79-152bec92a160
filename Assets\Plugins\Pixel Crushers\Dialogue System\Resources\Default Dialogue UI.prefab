%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: Button 6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100002
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400002}
  - 114: {fileID: 11400002}
  m_Layer: 0
  m_Name: Button 5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100004
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400004}
  - 114: {fileID: 11400010}
  m_Layer: 0
  m_Name: Button 4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100006
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400006}
  - 114: {fileID: 11400008}
  m_Layer: 0
  m_Name: Button 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100008
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400008}
  - 114: {fileID: 11400006}
  m_Layer: 0
  m_Name: Button 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100010
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400010}
  - 114: {fileID: 11400004}
  m_Layer: 0
  m_Name: Button 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100012
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400018}
  - 114: {fileID: 11400014}
  - 114: {fileID: 11400038}
  m_Layer: 0
  m_Name: Response Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100014
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400012}
  - 114: {fileID: 11400018}
  - 114: {fileID: 11400012}
  m_Layer: 0
  m_Name: PC Subtitle Line
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100016
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400020}
  - 114: {fileID: 11400020}
  - 114: {fileID: 11400046}
  m_Layer: 0
  m_Name: QTE 1 Evil
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100018
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400014}
  - 114: {fileID: 11400022}
  m_Layer: 0
  m_Name: NPC Subtitle Reminder Line
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100020
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400022}
  - 114: {fileID: 11400024}
  - 114: {fileID: 11400044}
  m_Layer: 0
  m_Name: QTE 0 Good
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100022
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400016}
  - 114: {fileID: 11400030}
  - 114: {fileID: 11400028}
  - 114: {fileID: 11400016}
  m_Layer: 0
  m_Name: NPC Subtitle Line
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100024
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400024}
  - 114: {fileID: 11400042}
  - 114: {fileID: 11400032}
  m_Layer: 0
  m_Name: Timer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100026
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400026}
  - 114: {fileID: 11400034}
  m_Layer: 0
  m_Name: GUIRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100028
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400028}
  - 114: {fileID: 11400036}
  m_Layer: 0
  m_Name: Default Dialogue UI
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100030
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400030}
  - 114: {fileID: 11400040}
  - 114: {fileID: 11400026}
  m_Layer: 0
  m_Name: Alert
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100032
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400032}
  - 114: {fileID: 11400048}
  m_Layer: 0
  m_Name: Text Field
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100034
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400034}
  - 114: {fileID: 11400050}
  m_Layer: 0
  m_Name: Label
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &100036
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400036}
  - 114: {fileID: 11400052}
  m_Layer: 0
  m_Name: Text Field Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &100038
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400038}
  - 114: {fileID: 11400056}
  - 114: {fileID: 11400054}
  m_Layer: 0
  m_Name: Text Field UI
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &186086
GameObject:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 486086}
  - 114: {fileID: 11486086}
  m_Layer: 0
  m_Name: Continue Button
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400018}
  m_RootOrder: 5
--- !u!4 &400002
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100002}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400018}
  m_RootOrder: 4
--- !u!4 &400004
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100004}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400018}
  m_RootOrder: 3
--- !u!4 &400006
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100006}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400018}
  m_RootOrder: 2
--- !u!4 &400008
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100008}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400018}
  m_RootOrder: 1
--- !u!4 &400010
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100010}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400018}
  m_RootOrder: 0
--- !u!4 &400012
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100014}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400026}
  m_RootOrder: 4
--- !u!4 &400014
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100018}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400026}
  m_RootOrder: 3
--- !u!4 &400016
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100022}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400026}
  m_RootOrder: 2
--- !u!4 &400018
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100012}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 400010}
  - {fileID: 400008}
  - {fileID: 400006}
  - {fileID: 400004}
  - {fileID: 400002}
  - {fileID: 400000}
  - {fileID: 400024}
  m_Father: {fileID: 400026}
  m_RootOrder: 6
--- !u!4 &400020
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100016}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400026}
  m_RootOrder: 1
--- !u!4 &400022
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100020}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400026}
  m_RootOrder: 0
--- !u!4 &400024
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100024}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400018}
  m_RootOrder: 6
--- !u!4 &400026
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100026}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 400022}
  - {fileID: 400020}
  - {fileID: 400016}
  - {fileID: 400014}
  - {fileID: 400012}
  - {fileID: 486086}
  - {fileID: 400018}
  - {fileID: 400030}
  - {fileID: 400038}
  m_Father: {fileID: 400028}
  m_RootOrder: 0
--- !u!4 &400028
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100028}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 400026}
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!4 &400030
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100030}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400026}
  m_RootOrder: 7
--- !u!4 &400032
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100032}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400036}
  m_RootOrder: 1
--- !u!4 &400034
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100034}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400036}
  m_RootOrder: 0
--- !u!4 &400036
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100036}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 400034}
  - {fileID: 400032}
  m_Father: {fileID: 400038}
  m_RootOrder: 0
--- !u!4 &400038
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100038}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 400036}
  m_Father: {fileID: 400026}
  m_RootOrder: 8
--- !u!4 &486086
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 186086}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 400026}
  m_RootOrder: 5
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbddc4cefd1de8e43ad762b2798cda14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 30
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Response 6
  guiStyleName: 
  clickable: 1
  disabled:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  normal:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hover:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  pressed:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hoverSound: {fileID: 8300000, guid: cdebbd691d1e7c748955b62cef466831, type: 3}
  clickSound: {fileID: 8300000, guid: 37cc5c4c34e880e4ea94f0daa8bb4aef, type: 3}
  trigger:
    key: 0
    buttonName: 
  message: OnClick
  parameter: 
  target: {fileID: 400028}
--- !u!114 &11400002
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100002}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbddc4cefd1de8e43ad762b2798cda14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: -30
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 30
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Response 5
  guiStyleName: 
  clickable: 1
  disabled:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  normal:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hover:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  pressed:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hoverSound: {fileID: 8300000, guid: cdebbd691d1e7c748955b62cef466831, type: 3}
  clickSound: {fileID: 8300000, guid: 37cc5c4c34e880e4ea94f0daa8bb4aef, type: 3}
  trigger:
    key: 0
    buttonName: 
  message: OnClick
  parameter: 
  target: {fileID: 400028}
--- !u!114 &11400004
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbddc4cefd1de8e43ad762b2798cda14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: -150
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 30
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Response 1
  guiStyleName: 
  clickable: 1
  disabled:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  normal:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hover:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  pressed:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hoverSound: {fileID: 8300000, guid: cdebbd691d1e7c748955b62cef466831, type: 3}
  clickSound: {fileID: 8300000, guid: 37cc5c4c34e880e4ea94f0daa8bb4aef, type: 3}
  trigger:
    key: 0
    buttonName: 
  message: OnClick
  parameter: 
  target: {fileID: 400028}
--- !u!114 &11400006
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100008}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbddc4cefd1de8e43ad762b2798cda14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: -120
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 30
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Response 2
  guiStyleName: 
  clickable: 1
  disabled:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  normal:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hover:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  pressed:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hoverSound: {fileID: 8300000, guid: cdebbd691d1e7c748955b62cef466831, type: 3}
  clickSound: {fileID: 8300000, guid: 37cc5c4c34e880e4ea94f0daa8bb4aef, type: 3}
  trigger:
    key: 0
    buttonName: 
  message: OnClick
  parameter: 
  target: {fileID: 400028}
--- !u!114 &11400008
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100006}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbddc4cefd1de8e43ad762b2798cda14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: -90
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 30
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Response 3
  guiStyleName: 
  clickable: 1
  disabled:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  normal:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hover:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  pressed:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hoverSound: {fileID: 8300000, guid: cdebbd691d1e7c748955b62cef466831, type: 3}
  clickSound: {fileID: 8300000, guid: 37cc5c4c34e880e4ea94f0daa8bb4aef, type: 3}
  trigger:
    key: 0
    buttonName: 
  message: OnClick
  parameter: 
  target: {fileID: 400028}
--- !u!114 &11400010
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100004}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbddc4cefd1de8e43ad762b2798cda14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: -60
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 30
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Response 4
  guiStyleName: 
  clickable: 1
  disabled:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  normal:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hover:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  pressed:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hoverSound: {fileID: 8300000, guid: cdebbd691d1e7c748955b62cef466831, type: 3}
  clickSound: {fileID: 8300000, guid: 37cc5c4c34e880e4ea94f0daa8bb4aef, type: 3}
  trigger:
    key: 0
    buttonName: 
  message: OnClick
  parameter: 
  target: {fileID: 400028}
--- !u!114 &11400012
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bf7b91960d98f1b44ba6805653d184bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  fadeInDuration: 0.2
  duration: 1
  fadeOutDuration: 0
--- !u!114 &11400014
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 86a40e129f2cbc840aa07eeabd0f6056, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 200
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order:
    - {fileID: 11400004}
    - {fileID: 11400006}
    - {fileID: 11400008}
    - {fileID: 11400010}
    - {fileID: 11400002}
    - {fileID: 11400000}
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
--- !u!114 &11400016
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bf7b91960d98f1b44ba6805653d184bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  fadeInDuration: 0.2
  duration: 1
  fadeOutDuration: 0
--- !u!114 &11400018
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06fc588754dcd504e899d843da4bb661, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 6
    alignment: 6
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 0
      value: 210
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: PC Subtitle
  guiStyleName: 
  textStyle: 2
  textStyleColor: {r: 0, g: 0, b: 0, a: 1}
  imageColor: {r: 1, g: 1, b: 1, a: 1}
  image: {fileID: 0}
  imageAnimation:
    animate: 0
    frameWidth: 64
    framesPerSecond: 1
--- !u!114 &11400020
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100016}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06fc588754dcd504e899d843da4bb661, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 8
    alignment: 8
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 0
      value: 128
    height:
      scale: 0
      value: 128
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: 
  guiStyleName: 
  textStyle: 0
  textStyleColor: {r: 0, g: 0, b: 0, a: 1}
  imageColor: {r: 1, g: 1, b: 1, a: 1}
  image: {fileID: 2800000, guid: 2af0b3d0c6b321c43b9e73aeb637d776, type: 3}
  imageAnimation:
    animate: 0
    frameWidth: 64
    framesPerSecond: 1
--- !u!114 &11400022
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100018}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06fc588754dcd504e899d843da4bb661, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 0
    alignment: 0
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 1
      value: 0.5
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: NPC Subtitle
  guiStyleName: 
  textStyle: 2
  textStyleColor: {r: 0, g: 0, b: 0, a: 1}
  imageColor: {r: 1, g: 1, b: 1, a: 1}
  image: {fileID: 0}
  imageAnimation:
    animate: 0
    frameWidth: 64
    framesPerSecond: 1
--- !u!114 &11400024
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100020}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06fc588754dcd504e899d843da4bb661, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 6
    alignment: 6
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 0
      value: 128
    height:
      scale: 0
      value: 128
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: 
  guiStyleName: 
  textStyle: 0
  textStyleColor: {r: 0, g: 0, b: 0, a: 1}
  imageColor: {r: 1, g: 1, b: 1, a: 1}
  image: {fileID: 2800000, guid: 59a5e9bd0630df84aaa81faa40b8d0ae, type: 3}
  imageAnimation:
    animate: 0
    frameWidth: 64
    framesPerSecond: 1
--- !u!114 &11400026
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100030}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bf7b91960d98f1b44ba6805653d184bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  fadeInDuration: 0.5
  duration: 1
  fadeOutDuration: 0.5
--- !u!114 &11400028
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 730b534d43b6a5b4d8e0843e523c845e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  charactersPerSecond: 50
  audioClip: {fileID: 0}
--- !u!114 &11400030
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06fc588754dcd504e899d843da4bb661, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 0
    alignment: 0
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 1
      value: 0.5
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: NPC Subtitle
  guiStyleName: 
  textStyle: 2
  textStyleColor: {r: 0, g: 0, b: 0, a: 1}
  imageColor: {r: 1, g: 1, b: 1, a: 1}
  image: {fileID: 0}
  imageAnimation:
    animate: 0
    frameWidth: 64
    framesPerSecond: 1
--- !u!114 &11400032
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100024}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b71bc7613034af34cab00b59795431c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  duration: 5
--- !u!114 &11400034
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100026}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 79b7f4b23d5fee541951feb5a0ce6030, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 0
    alignment: 0
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 1
      value: 1
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  guiSkin: {fileID: 11400000, guid: 29245abd9e583b5409a4aeadfdf5e11f, type: 2}
--- !u!114 &11400036
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100028}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85d7493b5ec1a4a489ede41c8064d254, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  guiRoot: {fileID: 11400034}
  dialogue:
    panel: {fileID: 0}
    npcSubtitle:
      panel: {fileID: 0}
      line: {fileID: 11400030}
      portraitImage: {fileID: 0}
      portraitName: {fileID: 0}
      continueButton: {fileID: 11486086}
    pcSubtitle:
      panel: {fileID: 0}
      line: {fileID: 11400018}
      portraitImage: {fileID: 0}
      portraitName: {fileID: 0}
      continueButton: {fileID: 11486086}
    responseMenu:
      buttonAlignment: 1
      showUnusedButtons: 0
      panel: {fileID: 11400014}
      pcImage: {fileID: 0}
      pcName: {fileID: 0}
      subtitleReminder:
        panel: {fileID: 0}
        line: {fileID: 11400022}
        portraitImage: {fileID: 0}
        portraitName: {fileID: 0}
        continueButton: {fileID: 0}
      timer: {fileID: 11400042}
      buttons:
      - {fileID: 11400004}
      - {fileID: 11400006}
      - {fileID: 11400008}
      - {fileID: 11400010}
      - {fileID: 11400002}
      - {fileID: 11400000}
  qteIndicators:
  - {fileID: 11400024}
  - {fileID: 11400020}
  alert:
    panel: {fileID: 0}
    line: {fileID: 11400040}
    continueButton: {fileID: 0}
--- !u!114 &11400038
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 981993f01c5b0db4499969008e1531aa, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  direction: 0
  duration: 0.3
--- !u!114 &11400040
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100030}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06fc588754dcd504e899d843da4bb661, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 4
    alignment: 4
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 1
      value: 0.5
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Alert
  guiStyleName: 
  textStyle: 2
  textStyleColor: {r: 0, g: 0, b: 0, a: 1}
  imageColor: {r: 1, g: 1, b: 1, a: 1}
  image: {fileID: 0}
  imageAnimation:
    animate: 0
    frameWidth: 64
    framesPerSecond: 1
--- !u!114 &11400042
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100024}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 58b59ad33059c2d4798d54acbc32130e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 1
    alignment: 1
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 0
      value: 128
    height:
      scale: 0
      value: 16
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: 
  guiStyleName: 
  origin: 2
  emptyImage: {fileID: 2800000, guid: a3cb6bd9e3da84a4fba74d89aeb57b88, type: 3}
  fullImage: {fileID: 2800000, guid: db2594dbf980d344eb86ef53b93320af, type: 3}
  progress: 0
--- !u!114 &11400044
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100020}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7cdb60899b9a6114e82657d5b679c74a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  interval: 0.5
--- !u!114 &11400046
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100016}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7cdb60899b9a6114e82657d5b679c74a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 0
  interval: 0.5
--- !u!114 &11400048
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100032}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61741e9f916d0e340ac16e38117976d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 2
    alignment: 2
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 4
    width:
      scale: 0
      value: 200
    height:
      scale: 0
      value: 24
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: 
  guiStyleName: 
  maxLength: 0
--- !u!114 &11400050
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100034}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06fc588754dcd504e899d843da4bb661, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 0
    alignment: 0
    x:
      scale: 0
      value: -100
    y:
      scale: 0
      value: 4
    width:
      scale: 1
      value: 1
    height:
      scale: 1
      value: 1
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Label
  guiStyleName: 
  textStyle: 0
  textStyleColor: {r: 0, g: 0, b: 0, a: 1}
  imageColor: {r: 1, g: 1, b: 1, a: 1}
  image: {fileID: 0}
  imageAnimation:
    animate: 0
    frameWidth: 64
    framesPerSecond: 1
--- !u!114 &11400052
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100036}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 86a40e129f2cbc840aa07eeabd0f6056, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 7
    alignment: 7
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 0
      value: 300
    height:
      scale: 0
      value: 100
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
--- !u!114 &11400054
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100038}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 86a40e129f2cbc840aa07eeabd0f6056, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 0
    alignment: 0
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 1
      value: 1
    height:
      scale: 1
      value: 1
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
--- !u!114 &11400056
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 100038}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 770763badfe5e144fb5374a7dcab2789, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  panel: {fileID: 11400052}
  label: {fileID: 11400050}
  textField: {fileID: 11400048}
  acceptKey: 13
  cancelKey: 27
--- !u!114 &11486086
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: *********}
  m_GameObject: {fileID: 186086}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dbddc4cefd1de8e43ad762b2798cda14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  depth: 0
  depthSortChildren: 0
  scaledRect:
    origin: 8
    alignment: 8
    x:
      scale: 0
      value: 0
    y:
      scale: 0
      value: 0
    width:
      scale: 0
      value: 160
    height:
      scale: 0
      value: 30
    minPixelWidth: 0
    minPixelHeight: 0
  autoSize:
    autoSizeWidth: 0
    autoSizeHeight: 0
    maxWidth:
      scale: 1
      value: 1
    maxHeight:
      scale: 1
      value: 1
    padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
  fit:
    above: {fileID: 0}
    below: {fileID: 0}
    leftOf: {fileID: 0}
    rightOf: {fileID: 0}
    expandToFit: 1
  navigation:
    enabled: 0
    focusFirstControlOnEnable: 1
    jumpToMousePosition: 1
    order: []
    clickButton: Fire1
    click: 32
    previous: 273
    next: 274
    axis: Vertical
    invertAxis: 1
    axisRepeatDelay: 1
    mouseWheelSensitivity: 5
  visible: 1
  clipChildren: 1
  localizedText: {fileID: 0}
  text: Continue
  guiStyleName: 
  clickable: 1
  disabled:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  normal:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hover:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  pressed:
    pixelRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    texture: {fileID: 0}
    useTexCoords: 0
    texCoords:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
    scaleMode: 2
    alphaBlend: 1
    color: {r: 1, g: 1, b: 1, a: 1}
    aspect: 0
  hoverSound: {fileID: 0}
  clickSound: {fileID: 0}
  trigger:
    key: 0
    buttonName: 
  message: OnClick
  parameter: 
  target: {fileID: 0}
--- !u!1001 &*********
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100028}
  m_IsPrefabParent: 1
