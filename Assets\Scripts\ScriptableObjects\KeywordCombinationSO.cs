using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 关键词组合配方 ScriptableObject
/// 定义关键词的组合规则和结果
/// </summary>
[CreateAssetMenu(fileName = "KeywordCombination", menuName = "Midnight Broadcasting/Keyword Combination")]
public class KeywordCombinationSO : ScriptableObject
{
    [Header("=== 组合配方 ===")]
    [Tooltip("需要的关键词（必须全部拥有）")]
    public List<KeywordDataSO> requiredKeywords = new List<KeywordDataSO>();
    
    [Tooltip("生成的结果关键词")]
    public KeywordDataSO resultKeyword;

    [Header("=== 组合信息 ===")]
    [Tooltip("组合名称")]
    public string combinationName;
    
    [Tooltip("组合提示")]
    [TextArea(2, 4)]
    public string combinationHint;
    
    [Tooltip("组合描述")]
    [TextArea(3, 5)]
    public string combinationDescription;

    [Header("=== 组合条件 ===")]
    [Tooltip("需要的最低DTV等级")]
    [Range(0, 5)]
    public int requiredDTVLevel = 0;
    
    [Tooltip("是否需要特定听众")]
    public bool requiresSpecificAudience = false;
    
    [Tooltip("关联的听众ID")]
    public string associatedAudienceId = "";

    [Header("=== 组合奖励 ===")]
    [Tooltip("组合成功时的温度奖励")]
    public float warmthReward = 0f;
    
    [Tooltip("组合成功时的精力奖励")]
    public float staminaReward = 0f;
    
    [Tooltip("组合成功时的金钱奖励")]
    public float moneyReward = 0f;

    [Header("=== 高级设置 ===")]
    [Tooltip("组合难度（影响成功率）")]
    [Range(0f, 1f)]
    public float difficulty = 0.5f;
    
    [Tooltip("是否为隐藏组合")]
    public bool isHidden = false;
    
    [Tooltip("是否为一次性组合")]
    public bool isOneTime = false;
    
    [Tooltip("是否已经使用过")]
    public bool hasBeenUsed = false;

    #region 业务逻辑方法

    /// <summary>
    /// 检查是否可以进行组合
    /// </summary>
    public bool CanCombine(List<KeywordDataSO> availableKeywords)
    {
        // 检查是否已经使用过（一次性组合）
        if (isOneTime && hasBeenUsed)
        {
            return false;
        }

        // 检查是否拥有所有必需的关键词
        foreach (var required in requiredKeywords)
        {
            if (required == null) continue;
            
            bool hasKeyword = availableKeywords.Any(k => 
                k != null && k.keyword == required.keyword && !k.IsExpired);
            
            if (!hasKeyword)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查DTV条件是否满足
    /// </summary>
    public bool CheckDTVRequirement(int currentDTV)
    {
        return currentDTV >= requiredDTVLevel;
    }

    /// <summary>
    /// 检查听众条件是否满足
    /// </summary>
    public bool CheckAudienceRequirement(string currentAudienceId)
    {
        if (!requiresSpecificAudience) return true;
        return currentAudienceId == associatedAudienceId;
    }

    /// <summary>
    /// 执行组合
    /// </summary>
    public KeywordDataSO ExecuteCombination(List<KeywordDataSO> availableKeywords, CoreGameDataSO gameData = null)
    {
        if (!CanCombine(availableKeywords))
        {
            Debug.LogWarning($"无法执行组合：{combinationName}");
            return null;
        }

        // 计算成功率
        float successRate = CalculateSuccessRate(availableKeywords);
        bool success = Random.value <= successRate;

        if (!success)
        {
            Debug.Log($"组合失败：{combinationName}（成功率：{successRate:P}）");
            return null;
        }

        // 标记为已使用
        if (isOneTime)
        {
            hasBeenUsed = true;
        }

        // 创建结果关键词
        var result = Instantiate(resultKeyword);
        result.Initialize();
        result.name = resultKeyword.name + "_Generated";

        // 应用奖励
        if (gameData != null)
        {
            ApplyRewards(gameData);
        }

        Debug.Log($"组合成功：{combinationName} -> {result.keyword}");
        return result;
    }

    /// <summary>
    /// 计算组合成功率
    /// </summary>
    private float CalculateSuccessRate(List<KeywordDataSO> availableKeywords)
    {
        float baseSuccessRate = 1f - difficulty;
        
        // 根据关键词权重调整成功率
        float weightBonus = 0f;
        foreach (var required in requiredKeywords)
        {
            var matchingKeyword = availableKeywords.FirstOrDefault(k => 
                k != null && k.keyword == required.keyword);
            
            if (matchingKeyword != null)
            {
                weightBonus += matchingKeyword.weight * 0.1f;
            }
        }

        return Mathf.Clamp01(baseSuccessRate + weightBonus);
    }

    /// <summary>
    /// 应用组合奖励
    /// </summary>
    private void ApplyRewards(CoreGameDataSO gameData)
    {
        if (warmthReward != 0f)
        {
            gameData.ModifyWarmth(warmthReward);
        }
        
        if (staminaReward != 0f)
        {
            gameData.RestoreStamina(staminaReward);
        }
        
        if (moneyReward != 0f)
        {
            gameData.ModifyMoney(moneyReward);
        }
    }

    /// <summary>
    /// 获取组合预览信息
    /// </summary>
    public string GetPreviewInfo()
    {
        string info = $"组合：{combinationName}\n";
        info += $"需要关键词：{string.Join(", ", requiredKeywords.Where(k => k != null).Select(k => k.keyword))}\n";
        info += $"结果：{(resultKeyword != null ? resultKeyword.keyword : "未知")}\n";
        
        if (requiredDTVLevel > 0)
        {
            info += $"需要DTV等级：{requiredDTVLevel}\n";
        }
        
        if (requiresSpecificAudience)
        {
            info += $"需要特定听众：{associatedAudienceId}\n";
        }
        
        return info;
    }

    /// <summary>
    /// 获取缺失的关键词
    /// </summary>
    public List<KeywordDataSO> GetMissingKeywords(List<KeywordDataSO> availableKeywords)
    {
        List<KeywordDataSO> missing = new List<KeywordDataSO>();
        
        foreach (var required in requiredKeywords)
        {
            if (required == null) continue;
            
            bool hasKeyword = availableKeywords.Any(k => 
                k != null && k.keyword == required.keyword && !k.IsExpired);
            
            if (!hasKeyword)
            {
                missing.Add(required);
            }
        }
        
        return missing;
    }

    /// <summary>
    /// 重置组合状态（用于测试）
    /// </summary>
    public void ResetCombination()
    {
        hasBeenUsed = false;
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保组合名称不为空
        if (string.IsNullOrEmpty(combinationName))
        {
            combinationName = name;
        }
        
        // 确保难度在合理范围内
        difficulty = Mathf.Clamp01(difficulty);
        
        // 确保DTV等级在合理范围内
        requiredDTVLevel = Mathf.Clamp(requiredDTVLevel, 0, 5);
        
        // 移除空的关键词引用
        requiredKeywords.RemoveAll(k => k == null);
    }

    #endregion

    #region 调试方法

    [ContextMenu("测试组合")]
    private void TestCombination()
    {
        if (Application.isPlaying && requiredKeywords.Count > 0)
        {
            // 创建测试用的关键词列表
            List<KeywordDataSO> testKeywords = new List<KeywordDataSO>(requiredKeywords);
            
            var result = ExecuteCombination(testKeywords);
            if (result != null)
            {
                Debug.Log($"测试组合成功：{result.keyword}");
            }
            else
            {
                Debug.Log($"测试组合失败：{combinationName}");
            }
        }
    }

    [ContextMenu("重置组合状态")]
    private void ResetCombinationStatus()
    {
        ResetCombination();
        Debug.Log($"组合状态已重置：{combinationName}");
    }

    #endregion
}
