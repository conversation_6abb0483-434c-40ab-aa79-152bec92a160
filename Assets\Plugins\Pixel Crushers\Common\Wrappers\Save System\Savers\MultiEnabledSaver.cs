﻿// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.Wrappers
{

    /// <summary>
    /// This wrapper for PixelCrushers.MultiEnabledSaver keeps references intact if you switch 
    /// between the compiled assembly and source code versions of the original class.
    /// </summary>
    [AddComponentMenu("Pixel Crushers/Save System/Savers/Multi Enabled Saver")]
    public class MultiEnabledSaver: PixelCrushers.MultiEnabledSaver
    {
    }

}
