# 《深夜连线》存档系统架构文档

## 概述

本存档系统基于 **ScriptableObject + Easy Save 3 (ES3)** 架构设计，为《深夜连线》游戏提供完整的数据持久化解决方案。系统采用模块化设计，支持自动保存、版本控制、错误恢复等高级功能。

## 核心架构

### 1. 数据层 (ScriptableObject)
- **CoreGameDataSO**: 核心游戏数据（精力、温暖度、粉丝数、金钱等）
- **AudienceDataSO**: 听众数据（DTV、故事解锁、关键词收集等）
- **KeywordDataSO**: 关键词数据（类型、权重、过期时间等）
- **KeywordCombinationSO**: 关键词组合配方
- **StoryBlockSO**: 故事块数据
- **DialogueOptionSO**: 对话选项数据
- **MemoryDataSO**: 回忆数据（心绪内阁系统）

### 2. 管理层
- **ScriptableObjectSaveManager**: 核心存档管理器
- **KeywordManager**: 关键词管理器
- **SaveVersionManager**: 版本控制管理器
- **GameManager**: 游戏业务逻辑管理器

### 3. 序列化层
- **SaveDataStructures**: 可序列化数据结构
- **ES3集成**: 加密、压缩、文件管理

### 4. UI层
- **SaveLoadUI**: 存档界面管理器
- **SaveSlotUI**: 存档槽位组件

### 5. 调试层
- **SaveLoadDebugger**: 调试和测试工具

## 主要特性

### ✅ 数据完整性
- ScriptableObject原生支持，保证数据类型安全
- 复杂嵌套数据结构的完整序列化
- 引用关系的正确维护

### ✅ 性能优化
- ScriptableObject内存效率高
- ES3压缩减少文件大小
- 增量保存和懒加载支持

### ✅ 安全性
- ES3加密保护存档数据
- 数据验证和完整性检查
- 防篡改机制

### ✅ 版本控制
- 自动版本检测和兼容性检查
- 数据迁移支持
- 向后兼容性保证

### ✅ 错误恢复
- 多重恢复策略
- 自动备份机制
- 损坏文件修复

### ✅ 开发友好
- Unity Inspector集成
- 完整的调试工具
- 自动化测试套件

## 文件结构

```
Assets/Scripts/
├── Save Load/
│   ├── ScriptableObjectSaveManager.cs     # 核心存档管理器
│   ├── SaveDataStructures.cs              # 序列化数据结构
│   ├── KeywordManager.cs                  # 关键词管理器
│   ├── SaveVersionManager.cs              # 版本控制管理器
│   ├── SaveLoadDebugger.cs                # 调试工具
│   └── README.md                          # 本文档
├── ScriptableObjects/
│   ├── CoreGameDataSO.cs                  # 核心游戏数据
│   ├── AudienceDataSO.cs                  # 听众数据
│   ├── KeywordDataSO.cs                   # 关键词数据
│   ├── KeywordCombinationSO.cs            # 关键词组合
│   ├── StoryBlockSO.cs                    # 故事块
│   ├── DialogueOptionSO.cs                # 对话选项
│   └── MemoryDataSO.cs                    # 回忆数据
├── Managers/
│   └── GameManager.cs                     # 游戏管理器
├── UI/
│   ├── SaveLoadUI.cs                      # 存档界面
│   └── SaveSlotUI.cs                      # 存档槽位
└── Enums/
    └── SaveLoadEnums.cs                   # 枚举定义
```

## 使用方法

### 基本保存/加载
```csharp
// 保存游戏
GameManager.Instance.SaveGame("我的存档");

// 加载游戏
GameManager.Instance.LoadGame("我的存档");

// 获取存档列表
var saves = GameManager.Instance.GetSaveSlots();
```

### 自动保存
```csharp
// 系统会在以下情况自动保存：
// 1. 夜晚结束时
// 2. 重要进度节点
// 3. 定时自动保存（可配置间隔）
```

### 关键词管理
```csharp
// 收集关键词
GameManager.Instance.CollectKeyword(audience, "孤独");

// 执行关键词组合
var combination = keywordManager.FindAvailableCombinations(keywords);
var result = GameManager.Instance.ExecuteKeywordCombination(combination, keywords);
```

### 调试功能
```csharp
// 显示调试信息
SaveLoadDebugger.Instance.ShowDebugInfo();

// 运行测试套件
SaveLoadDebugger.Instance.RunFullTest();
```

## 配置说明

### SaveManager配置
- `enableEncryption`: 是否启用加密（默认：true）
- `enableCompression`: 是否启用压缩（默认：true）
- `autoSaveInterval`: 自动保存间隔（默认：300秒）
- `maxBackupCount`: 最大备份数量（默认：5个）

### 版本控制配置
- `currentGameVersion`: 当前游戏版本
- `minimumSupportedVersion`: 最低支持版本
- `enableAutoMigration`: 是否启用自动迁移

## 扩展指南

### 添加新的数据类型
1. 创建新的ScriptableObject类
2. 在SaveDataStructures中添加对应的序列化结构
3. 在SaveManager中添加序列化/反序列化逻辑
4. 更新版本迁移规则（如需要）

### 自定义存档格式
1. 继承SaveDataStructures中的基础类
2. 实现自定义序列化逻辑
3. 在SaveManager中注册新格式

### 添加新的验证规则
1. 在SaveVersionManager中添加验证方法
2. 在SaveManager的验证流程中调用

## 性能建议

1. **合理使用自动保存**: 避免过于频繁的自动保存
2. **优化数据结构**: 避免过深的嵌套和循环引用
3. **定期清理**: 删除不需要的备份文件
4. **监控性能**: 使用调试工具监控保存/加载性能

## 故障排除

### 常见问题
1. **存档损坏**: 系统会自动尝试恢复，或使用备份文件
2. **版本不兼容**: 检查版本迁移规则是否正确
3. **性能问题**: 使用性能监控工具分析瓶颈
4. **内存泄漏**: 确保正确释放ScriptableObject引用

### 调试步骤
1. 启用调试模式
2. 查看详细日志
3. 运行自动测试
4. 检查性能报告

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础存档系统实现
- ✅ ScriptableObject数据架构
- ✅ ES3集成和加密
- ✅ 版本控制系统
- ✅ 错误恢复机制
- ✅ 调试和测试工具
- ✅ UI界面实现

### 计划功能
- 🔄 云存档支持
- 🔄 存档压缩优化
- 🔄 更多数据验证规则
- 🔄 性能进一步优化

## 技术支持

如有问题或建议，请联系开发团队或查看相关文档。

---

**注意**: 本系统需要Easy Save 3插件支持，请确保已正确安装和配置。
