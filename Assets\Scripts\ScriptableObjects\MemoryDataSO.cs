using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 回忆数据 ScriptableObject
/// 心绪内阁系统的核心数据结构
/// </summary>
[CreateAssetMenu(fileName = "MemoryData", menuName = "Midnight Broadcasting/Memory Data")]
public class MemoryDataSO : ScriptableObject
{
    [Header("=== 基础信息 ===")]
    [Tooltip("回忆标题")]
    public string memoryTitle;
    
    [Tooltip("回忆描述")]
    [TextArea(3, 6)]
    public string memoryDescription;
    
    [Tooltip("回忆类型")]
    public MemoryType memoryType = MemoryType.Positive;

    [Header("=== 视觉表现 ===")]
    [Tooltip("回忆图标")]
    public Sprite memoryIcon;
    
    [Tooltip("回忆颜色")]
    public Color memoryColor = Color.white;

    [Header("=== 解锁条件 ===")]
    [Tooltip("解锁所需的关键词")]
    public List<KeywordDataSO> unlockKeywords = new List<KeywordDataSO>();
    
    [Tooltip("解锁所需的听众")]
    public List<AudienceDataSO> unlockAudiences = new List<AudienceDataSO>();
    
    [Tooltip("解锁所需的最低夜晚数")]
    public int requiredNight = 1;

    [Header("=== 回忆效果 ===")]
    [Tooltip("激活时的精力影响")]
    [Range(-20f, 20f)]
    public float staminaEffect = 0f;
    
    [Tooltip("激活时的温度影响")]
    [Range(-2f, 2f)]
    public float warmthEffect = 0f;
    
    [Tooltip("持续时间（分钟，0表示永久）")]
    public int durationMinutes = 0;

    [Header("=== 特殊属性 ===")]
    [Tooltip("是否为负面回忆")]
    public bool isNegative = false;
    
    [Tooltip("是否可以被压制")]
    public bool canBeSuppressed = true;
    
    [Tooltip("压制所需精力")]
    [Range(0f, 50f)]
    public float suppressionCost = 10f;

    [Header("=== 状态信息 ===")]
    [Tooltip("是否已解锁")]
    public bool isUnlocked = false;
    
    [Tooltip("是否当前激活")]
    public bool isActive = false;
    
    [Tooltip("是否被压制")]
    public bool isSuppressed = false;
    
    [Tooltip("解锁时间")]
    public string unlockTimeString = "";

    #region 业务逻辑方法

    /// <summary>
    /// 检查是否可以解锁
    /// </summary>
    public bool CanUnlock(CoreGameDataSO gameData, List<AudienceDataSO> allAudiences)
    {
        if (isUnlocked) return false;

        // 检查夜晚要求
        if (gameData.currentNight < requiredNight)
        {
            return false;
        }

        // 检查关键词要求
        foreach (var requiredKeyword in unlockKeywords)
        {
            if (requiredKeyword == null) continue;
            
            bool hasKeyword = false;
            foreach (var audience in allAudiences)
            {
                if (audience.HasKeyword(requiredKeyword.keyword))
                {
                    hasKeyword = true;
                    break;
                }
            }
            
            if (!hasKeyword)
            {
                return false;
            }
        }

        // 检查听众要求
        foreach (var requiredAudience in unlockAudiences)
        {
            if (requiredAudience == null) continue;
            
            var audience = allAudiences.Find(a => a.audienceId == requiredAudience.audienceId);
            if (audience == null || audience.currentDTV < 3)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 解锁回忆
    /// </summary>
    public void UnlockMemory()
    {
        if (isUnlocked) return;
        
        isUnlocked = true;
        unlockTimeString = System.DateTime.Now.ToString();
        
        Debug.Log($"解锁回忆：{memoryTitle}");
    }

    /// <summary>
    /// 激活回忆
    /// </summary>
    public void ActivateMemory(CoreGameDataSO gameData)
    {
        if (!isUnlocked || isActive || isSuppressed) return;
        
        isActive = true;
        
        // 应用效果
        if (gameData != null)
        {
            if (staminaEffect > 0)
            {
                gameData.RestoreStamina(staminaEffect);
            }
            else if (staminaEffect < 0)
            {
                gameData.ConsumeStamina(-staminaEffect);
            }
            
            if (warmthEffect != 0f)
            {
                gameData.ModifyWarmth(warmthEffect);
            }
        }
        
        // 如果是负面回忆，添加负面状态
        if (isNegative)
        {
            gameData?.AddNegativeState($"回忆影响：{memoryTitle}");
        }
        
        Debug.Log($"激活回忆：{memoryTitle}");
        
        // 设置持续时间
        if (durationMinutes > 0)
        {
            // 这里可以启动一个计时器来自动停用回忆
            // 在实际实现中，可能需要一个回忆管理器来处理这个逻辑
        }
    }

    /// <summary>
    /// 停用回忆
    /// </summary>
    public void DeactivateMemory(CoreGameDataSO gameData)
    {
        if (!isActive) return;
        
        isActive = false;
        
        // 移除负面状态
        if (isNegative && gameData != null)
        {
            gameData.RemoveNegativeState($"回忆影响：{memoryTitle}");
        }
        
        Debug.Log($"停用回忆：{memoryTitle}");
    }

    /// <summary>
    /// 压制回忆
    /// </summary>
    public bool SuppressMemory(CoreGameDataSO gameData)
    {
        if (!canBeSuppressed || isSuppressed) return false;
        
        if (gameData != null && !gameData.CanPerformAction(suppressionCost))
        {
            Debug.LogWarning("精力不足，无法压制回忆");
            return false;
        }
        
        isSuppressed = true;
        
        if (isActive)
        {
            DeactivateMemory(gameData);
        }
        
        // 消耗精力
        gameData?.ConsumeStamina(suppressionCost);
        
        Debug.Log($"压制回忆：{memoryTitle}");
        return true;
    }

    /// <summary>
    /// 解除压制
    /// </summary>
    public void ReleaseSuppression()
    {
        if (!isSuppressed) return;
        
        isSuppressed = false;
        Debug.Log($"解除压制：{memoryTitle}");
    }

    /// <summary>
    /// 获取回忆状态描述
    /// </summary>
    public string GetStatusDescription()
    {
        if (!isUnlocked) return "未解锁";
        if (isSuppressed) return "已压制";
        if (isActive) return "激活中";
        return "可用";
    }

    /// <summary>
    /// 获取解锁条件描述
    /// </summary>
    public string GetUnlockRequirements()
    {
        List<string> requirements = new List<string>();
        
        if (requiredNight > 1)
        {
            requirements.Add($"第{requiredNight}夜");
        }
        
        foreach (var keyword in unlockKeywords)
        {
            if (keyword != null)
            {
                requirements.Add($"关键词：{keyword.keyword}");
            }
        }
        
        foreach (var audience in unlockAudiences)
        {
            if (audience != null)
            {
                requirements.Add($"听众：{audience.audienceName}（DTV≥3）");
            }
        }
        
        return string.Join("、", requirements);
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保标题不为空
        if (string.IsNullOrEmpty(memoryTitle))
        {
            memoryTitle = name;
        }
        
        // 确保数值在合理范围内
        staminaEffect = Mathf.Clamp(staminaEffect, -20f, 20f);
        warmthEffect = Mathf.Clamp(warmthEffect, -2f, 2f);
        suppressionCost = Mathf.Clamp(suppressionCost, 0f, 50f);
        requiredNight = Mathf.Max(1, requiredNight);
        durationMinutes = Mathf.Max(0, durationMinutes);
        
        // 移除空引用
        unlockKeywords.RemoveAll(k => k == null);
        unlockAudiences.RemoveAll(a => a == null);
        
        // 负面回忆的默认设置
        if (isNegative)
        {
            if (memoryColor == Color.white)
            {
                memoryColor = new Color(0.8f, 0.3f, 0.3f); // 暗红色
            }
        }
    }

    #endregion

    #region 调试方法

    [ContextMenu("强制解锁")]
    private void ForceUnlock()
    {
        if (Application.isPlaying)
        {
            UnlockMemory();
        }
    }

    [ContextMenu("切换激活状态")]
    private void ToggleActive()
    {
        if (Application.isPlaying && isUnlocked)
        {
            if (isActive)
            {
                DeactivateMemory(null);
            }
            else
            {
                ActivateMemory(null);
            }
        }
    }

    #endregion
}

/// <summary>
/// 回忆类型枚举
/// </summary>
public enum MemoryType
{
    Positive,   // 正面回忆
    Negative,   // 负面回忆
    Neutral,    // 中性回忆
    Nostalgic,  // 怀旧回忆
    Traumatic   // 创伤回忆
}
