using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;

/// <summary>
/// ScriptableObject保存管理器
/// 使用ES3进行序列化，管理所有游戏数据的保存和加载
/// </summary>
public class ScriptableObjectSaveManager : MonoBehaviour
{
    [Header("=== 核心数据引用 ===")]
    [Tooltip("核心游戏数据")]
    public CoreGameDataSO coreGameData;

    [Tooltip("所有听众数据")]
    public List<AudienceDataSO> allAudiences = new List<AudienceDataSO>();

    [Tooltip("所有回忆数据")]
    public List<MemoryDataSO> allMemories = new List<MemoryDataSO>();

    [Header("=== 保存设置 ===")]
    [Tooltip("保存文件名前缀")]
    public string saveFilePrefix = "MidnightBroadcasting";

    [Tooltip("自动保存间隔（秒）")]
    public float autoSaveInterval = 300f; // 5分钟

    [Tooltip("最大备份数量")]
    public int maxBackupCount = 5;

    [Tooltip("是否启用加密")]
    public bool enableEncryption = true;

    [Header("=== 调试设置 ===")]
    [Tooltip("是否启用调试日志")]
    public bool enableDebugLog = true;

    // 私有字段
    private float lastAutoSaveTime;
    private string currentSaveSlot = "slot1";
    private const string SAVE_VERSION = "1.0.0";

    // 单例模式
    public static ScriptableObjectSaveManager Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        // 单例模式设置
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSaveSystem();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // 尝试加载最近的存档
        LoadMostRecentSave();
    }

    private void Update()
    {
        // 自动保存检查
        if (Time.time - lastAutoSaveTime >= autoSaveInterval)
        {
            AutoSave();
        }
    }

    private void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            // 应用暂停时自动保存
            AutoSave();
        }
    }

    private void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus)
        {
            // 失去焦点时自动保存
            AutoSave();
        }
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化保存系统
    /// </summary>
    private void InitializeSaveSystem()
    {
        // 检查ES3是否可用
        if (!ES3.FileExists())
        {
            DebugLog("ES3系统初始化完成");
        }

        // 设置ES3默认设置
        ES3Settings settings = new ES3Settings();
        settings.encrypt = enableEncryption;
        settings.compressionType = ES3.CompressionType.Gzip;

        lastAutoSaveTime = Time.time;
        DebugLog("保存系统初始化完成");
    }

    #endregion

    #region 保存功能

    /// <summary>
    /// 手动保存到指定槽位
    /// </summary>
    public bool SaveGame(string slotName = null)
    {
        try
        {
            if (string.IsNullOrEmpty(slotName))
            {
                slotName = currentSaveSlot;
            }

            string fileName = GetSaveFileName(slotName);

            // 创建保存数据结构
            SaveData saveData = CreateSaveData();

            // 使用ES3保存
            ES3Settings settings = GetES3Settings();
            ES3.Save("saveData", saveData, fileName, settings);
            ES3.Save("saveVersion", SAVE_VERSION, fileName, settings);
            ES3.Save("saveTime", DateTime.Now.ToString(), fileName, settings);

            currentSaveSlot = slotName;
            DebugLog($"游戏保存成功：{fileName}");

            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"保存游戏失败：{e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 自动保存
    /// </summary>
    public void AutoSave()
    {
        if (SaveGame("autosave"))
        {
            lastAutoSaveTime = Time.time;
            DebugLog("自动保存完成");
        }
    }

    /// <summary>
    /// 创建备份
    /// </summary>
    public bool CreateBackup(string backupName = null)
    {
        if (string.IsNullOrEmpty(backupName))
        {
            backupName = $"backup_{DateTime.Now:yyyyMMdd_HHmmss}";
        }

        bool success = SaveGame(backupName);
        if (success)
        {
            CleanupOldBackups();
        }

        return success;
    }

    #endregion

    #region 加载功能

    /// <summary>
    /// 从指定槽位加载游戏
    /// </summary>
    public bool LoadGame(string slotName = null)
    {
        try
        {
            if (string.IsNullOrEmpty(slotName))
            {
                slotName = currentSaveSlot;
            }

            string fileName = GetSaveFileName(slotName);

            if (!ES3.FileExists(fileName))
            {
                DebugLog($"存档文件不存在：{fileName}");
                return false;
            }

            // 检查版本兼容性
            if (!CheckSaveVersion(fileName))
            {
                Debug.LogWarning("存档版本不兼容，尝试迁移...");
                // 这里可以添加版本迁移逻辑
            }

            // 加载保存数据
            ES3Settings settings = GetES3Settings();
            SaveData saveData = ES3.Load<SaveData>("saveData", fileName, settings);

            // 应用数据到ScriptableObjects
            ApplySaveData(saveData);

            currentSaveSlot = slotName;
            DebugLog($"游戏加载成功：{fileName}");

            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"加载游戏失败：{e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 加载最近的存档
    /// </summary>
    public bool LoadMostRecentSave()
    {
        string mostRecentFile = FindMostRecentSaveFile();
        if (!string.IsNullOrEmpty(mostRecentFile))
        {
            string slotName = Path.GetFileNameWithoutExtension(mostRecentFile)
                .Replace(saveFilePrefix + "_", "");
            return LoadGame(slotName);
        }

        DebugLog("没有找到可用的存档文件");
        return false;
    }

    #endregion

    #region 数据处理

    /// <summary>
    /// 创建保存数据
    /// </summary>
    private SaveData CreateSaveData()
    {
        SaveData saveData = new SaveData();

        // 保存核心游戏数据
        if (coreGameData != null)
        {
            saveData.coreGameData = SerializeCoreGameData(coreGameData);
        }

        // 保存听众数据
        saveData.audienceDataList = new List<SerializableAudienceData>();
        foreach (var audience in allAudiences)
        {
            if (audience != null)
            {
                saveData.audienceDataList.Add(SerializeAudienceData(audience));
            }
        }

        // 保存回忆数据
        saveData.memoryDataList = new List<SerializableMemoryData>();
        foreach (var memory in allMemories)
        {
            if (memory != null)
            {
                saveData.memoryDataList.Add(SerializeMemoryData(memory));
            }
        }

        return saveData;
    }

    /// <summary>
    /// 应用保存数据
    /// </summary>
    private void ApplySaveData(SaveData saveData)
    {
        // 应用核心游戏数据
        if (saveData.coreGameData != null && coreGameData != null)
        {
            ApplyCoreGameData(saveData.coreGameData, coreGameData);
        }

        // 应用听众数据
        if (saveData.audienceDataList != null)
        {
            foreach (var audienceData in saveData.audienceDataList)
            {
                var audience = allAudiences.Find(a => a.audienceId == audienceData.audienceId);
                if (audience != null)
                {
                    ApplyAudienceData(audienceData, audience);
                }
            }
        }

        // 应用回忆数据
        if (saveData.memoryDataList != null)
        {
            foreach (var memoryData in saveData.memoryDataList)
            {
                var memory = allMemories.Find(m => m.name == memoryData.memoryName);
                if (memory != null)
                {
                    ApplyMemoryData(memoryData, memory);
                }
            }
        }
    }

    #endregion

    #region 工具方法

    /// <summary>
    /// 获取保存文件名
    /// </summary>
    private string GetSaveFileName(string slotName)
    {
        return $"{saveFilePrefix}_{slotName}.es3";
    }

    /// <summary>
    /// 获取ES3设置
    /// </summary>
    private ES3Settings GetES3Settings()
    {
        ES3Settings settings = new ES3Settings();
        settings.encrypt = enableEncryption;
        settings.compressionType = ES3.CompressionType.Gzip;
        return settings;
    }

    /// <summary>
    /// 检查存档版本
    /// </summary>
    private bool CheckSaveVersion(string fileName)
    {
        if (ES3.KeyExists("saveVersion", fileName))
        {
            string saveVersion = ES3.Load<string>("saveVersion", fileName);
            return saveVersion == SAVE_VERSION;
        }
        return false;
    }

    /// <summary>
    /// 查找最近的存档文件
    /// </summary>
    private string FindMostRecentSaveFile()
    {
        string[] saveFiles = Directory.GetFiles(Application.persistentDataPath,
            $"{saveFilePrefix}_*.es3");

        if (saveFiles.Length == 0) return null;

        string mostRecent = saveFiles[0];
        DateTime mostRecentTime = File.GetLastWriteTime(mostRecent);

        foreach (string file in saveFiles)
        {
            DateTime fileTime = File.GetLastWriteTime(file);
            if (fileTime > mostRecentTime)
            {
                mostRecent = file;
                mostRecentTime = fileTime;
            }
        }

        return mostRecent;
    }

    /// <summary>
    /// 清理旧备份
    /// </summary>
    private void CleanupOldBackups()
    {
        string[] backupFiles = Directory.GetFiles(Application.persistentDataPath,
            $"{saveFilePrefix}_backup_*.es3");

        if (backupFiles.Length <= maxBackupCount) return;

        // 按修改时间排序，删除最旧的文件
        Array.Sort(backupFiles, (x, y) =>
            File.GetLastWriteTime(x).CompareTo(File.GetLastWriteTime(y)));

        int filesToDelete = backupFiles.Length - maxBackupCount;
        for (int i = 0; i < filesToDelete; i++)
        {
            try
            {
                File.Delete(backupFiles[i]);
                DebugLog($"删除旧备份：{backupFiles[i]}");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"删除备份文件失败：{e.Message}");
            }
        }
    }

    /// <summary>
    /// 调试日志
    /// </summary>
    private void DebugLog(string message)
    {
        if (enableDebugLog)
        {
            Debug.Log($"[SaveManager] {message}");
        }
    }

    #endregion

    #region 序列化方法

    /// <summary>
    /// 序列化核心游戏数据
    /// </summary>
    private SerializableCoreGameData SerializeCoreGameData(CoreGameDataSO data)
    {
        return new SerializableCoreGameData
        {
            currentStamina = data.currentStamina,
            currentWarmth = data.currentWarmth,
            totalFans = data.totalFans,
            money = data.money,
            currentNight = data.currentNight,
            currentPhase = data.currentPhase,
            lastPlayTimeString = data.lastPlayTimeString,
            unlockedFeatures = data.unlockedFeatures,
            activeNegativeStates = data.activeNegativeStates.ToArray()
        };
    }

    /// <summary>
    /// 序列化听众数据
    /// </summary>
    private SerializableAudienceData SerializeAudienceData(AudienceDataSO data)
    {
        return new SerializableAudienceData
        {
            audienceId = data.audienceId,
            currentDTV = data.currentDTV,
            unlockedStoryBlocks = data.unlockedStoryBlocks,
            currentState = data.currentState,
            lastInteractionTimeString = data.lastInteractionTimeString,
            totalInteractions = data.totalInteractions,
            successfulResonances = data.successfulResonances,
            failedInquiries = data.failedInquiries,
            collectedKeywordNames = data.collectedKeywords.Where(k => k != null).Select(k => k.keyword).ToArray(),
            expiredKeywordNames = data.expiredKeywords.Where(k => k != null).Select(k => k.keyword).ToArray(),
            audienceTags = data.audienceTags.ToArray()
        };
    }

    /// <summary>
    /// 序列化回忆数据
    /// </summary>
    private SerializableMemoryData SerializeMemoryData(MemoryDataSO data)
    {
        return new SerializableMemoryData
        {
            memoryName = data.name,
            isUnlocked = data.isUnlocked,
            isActive = data.isActive,
            isSuppressed = data.isSuppressed,
            unlockTimeString = data.unlockTimeString
        };
    }

    /// <summary>
    /// 应用核心游戏数据
    /// </summary>
    private void ApplyCoreGameData(SerializableCoreGameData data, CoreGameDataSO target)
    {
        target.currentStamina = data.currentStamina;
        target.currentWarmth = data.currentWarmth;
        target.totalFans = data.totalFans;
        target.money = data.money;
        target.currentNight = data.currentNight;
        target.currentPhase = data.currentPhase;
        target.lastPlayTimeString = data.lastPlayTimeString;
        target.unlockedFeatures = data.unlockedFeatures;
        target.activeNegativeStates = new List<string>(data.activeNegativeStates);
    }

    /// <summary>
    /// 应用听众数据
    /// </summary>
    private void ApplyAudienceData(SerializableAudienceData data, AudienceDataSO target)
    {
        target.currentDTV = data.currentDTV;
        target.unlockedStoryBlocks = data.unlockedStoryBlocks;
        target.currentState = data.currentState;
        target.lastInteractionTimeString = data.lastInteractionTimeString;
        target.totalInteractions = data.totalInteractions;
        target.successfulResonances = data.successfulResonances;
        target.failedInquiries = data.failedInquiries;
        target.audienceTags = new List<string>(data.audienceTags);

        // 重建关键词列表
        target.collectedKeywords.Clear();
        target.expiredKeywords.Clear();

        // 从关键词管理器重建关键词对象
        if (KeywordManager.Instance != null)
        {
            foreach (string keywordName in data.collectedKeywordNames)
            {
                var keyword = KeywordManager.Instance.CreateKeyword(keywordName, data.audienceId);
                if (keyword != null)
                {
                    target.collectedKeywords.Add(keyword);
                }
            }

            foreach (string keywordName in data.expiredKeywordNames)
            {
                var keyword = KeywordManager.Instance.CreateKeyword(keywordName, data.audienceId);
                if (keyword != null)
                {
                    target.expiredKeywords.Add(keyword);
                }
            }
        }
    }

    /// <summary>
    /// 应用回忆数据
    /// </summary>
    private void ApplyMemoryData(SerializableMemoryData data, MemoryDataSO target)
    {
        target.isUnlocked = data.isUnlocked;
        target.isActive = data.isActive;
        target.isSuppressed = data.isSuppressed;
        target.unlockTimeString = data.unlockTimeString;
    }

    #endregion

    #region 公共API

    /// <summary>
    /// 获取所有存档槽位信息
    /// </summary>
    public List<SaveSlotInfo> GetAllSaveSlots()
    {
        List<SaveSlotInfo> slots = new List<SaveSlotInfo>();

        string[] saveFiles = Directory.GetFiles(Application.persistentDataPath,
            $"{saveFilePrefix}_*.es3");

        foreach (string file in saveFiles)
        {
            try
            {
                string fileName = Path.GetFileName(file);
                string slotName = fileName.Replace(saveFilePrefix + "_", "").Replace(".es3", "");

                SaveSlotInfo slotInfo = new SaveSlotInfo
                {
                    slotName = slotName,
                    fileName = fileName,
                    saveTime = File.GetLastWriteTime(file),
                    fileSize = new FileInfo(file).Length
                };

                // 尝试读取存档信息
                if (ES3.KeyExists("saveTime", file))
                {
                    string saveTimeString = ES3.Load<string>("saveTime", file);
                    if (DateTime.TryParse(saveTimeString, out DateTime saveTime))
                    {
                        slotInfo.saveTime = saveTime;
                    }
                }

                slots.Add(slotInfo);
            }
            catch (Exception e)
            {
                Debug.LogWarning($"读取存档信息失败：{file}, {e.Message}");
            }
        }

        return slots.OrderByDescending(s => s.saveTime).ToList();
    }

    /// <summary>
    /// 删除存档
    /// </summary>
    public bool DeleteSave(string slotName)
    {
        try
        {
            string fileName = GetSaveFileName(slotName);
            string filePath = Path.Combine(Application.persistentDataPath, fileName);

            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                DebugLog($"删除存档：{fileName}");
                return true;
            }

            return false;
        }
        catch (Exception e)
        {
            Debug.LogError($"删除存档失败：{e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查存档是否存在
    /// </summary>
    public bool SaveExists(string slotName)
    {
        string fileName = GetSaveFileName(slotName);
        return ES3.FileExists(fileName);
    }

    #endregion
}
