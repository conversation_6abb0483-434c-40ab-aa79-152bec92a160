using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 听众数据 ScriptableObject
/// 包含听众信息、故事进度、关键词收集等
/// </summary>
[CreateAssetMenu(fileName = "AudienceData", menuName = "Midnight Broadcasting/Audience Data")]
public class AudienceDataSO : ScriptableObject
{
    [Header("=== 基础信息 ===")]
    [Tooltip("听众姓名")]
    public string audienceName;

    [Tooltip("听众ID（唯一标识）")]
    public string audienceId;

    [Tooltip("听众描述")]
    [TextArea(3, 5)]
    public string description;

    [Header("=== 头像系统 ===")]
    [Tooltip("模糊头像")]
    public Sprite portraitBlurred;

    [Tooltip("清晰头像")]
    public Sprite portraitClear;

    [Header("=== 故事进度 ===")]
    [Tooltip("当前深度倾诉值 (0-5)")]
    [Range(0, 5)]
    public int currentDTV = 0;

    [Tooltip("已解锁的故事块")]
    public bool[] unlockedStoryBlocks = new bool[6];  // Level 1-6

    [Tooltip("当前听众状态")]
    public AudienceState currentState = AudienceState.Unknown;

    [Tooltip("最后互动时间")]
    public string lastInteractionTimeString = "";

    [Header("=== 关键词系统 ===")]
    [Tooltip("已收集的关键词")]
    public List<KeywordDataSO> collectedKeywords = new List<KeywordDataSO>();

    [Tooltip("已过期的关键词")]
    public List<KeywordDataSO> expiredKeywords = new List<KeywordDataSO>();

    [Header("=== 故事模板 ===")]
    [Tooltip("故事块模板")]
    public List<StoryBlockSO> storyBlocks = new List<StoryBlockSO>();

    [Header("=== 互动统计 ===")]
    [Tooltip("总互动次数")]
    public int totalInteractions = 0;

    [Tooltip("成功共鸣次数")]
    public int successfulResonances = 0;

    [Tooltip("失败质询次数")]
    public int failedInquiries = 0;

    [Header("=== 特殊标签 ===")]
    [Tooltip("听众标签（用于事件触发）")]
    public List<string> audienceTags = new List<string>();

    // 运行时属性
    public DateTime LastInteractionTime
    {
        get
        {
            if (DateTime.TryParse(lastInteractionTimeString, out DateTime result))
                return result;
            return DateTime.MinValue;
        }
        set { lastInteractionTimeString = value.ToString(); }
    }

    #region 数据访问方法



    /// <summary>
    /// 检查是否拥有指定关键词
    /// </summary>
    public bool HasKeyword(string keywordText)
    {
        return collectedKeywords.Any(k => k != null && k.keyword == keywordText && !k.IsExpired);
    }

    /// <summary>
    /// 获取有效的关键词列表
    /// </summary>
    public List<KeywordDataSO> GetValidKeywords()
    {
        return collectedKeywords.Where(k => k != null && !k.IsExpired).ToList();
    }



    /// <summary>
    /// 获取当前头像
    /// </summary>
    public Sprite GetCurrentPortrait()
    {
        float completeness = GetProfileCompleteness();
        return completeness > 0.7f ? portraitClear : portraitBlurred;
    }

    /// <summary>
    /// 获取画像完整度
    /// </summary>
    public float GetProfileCompleteness()
    {
        int totalKeywords = GetTotalKeywordsCount();
        if (totalKeywords == 0) return 0f;

        int collectedCount = GetValidKeywords().Count;
        return (float)collectedCount / totalKeywords;
    }

    /// <summary>
    /// 获取总关键词数量
    /// </summary>
    private int GetTotalKeywordsCount()
    {
        // 计算所有故事块中的关键词总数
        int total = 0;
        foreach (var storyBlock in storyBlocks)
        {
            if (storyBlock != null)
            {
                total += storyBlock.rewardKeywords.Length;
            }
        }
        return total;
    }

    /// <summary>
    /// 获取指定等级的故事块
    /// </summary>
    public StoryBlockSO GetStoryBlock(int level)
    {
        return storyBlocks.FirstOrDefault(sb => sb != null && sb.level == level);
    }



    /// <summary>
    /// 获取互动成功率
    /// </summary>
    public float GetSuccessRate()
    {
        if (totalInteractions == 0) return 0f;
        return (float)successfulResonances / totalInteractions;
    }

    /// <summary>
    /// 检查是否有特定标签
    /// </summary>
    public bool HasTag(string tag)
    {
        return audienceTags.Contains(tag);
    }

    /// <summary>
    /// 添加标签
    /// </summary>
    public void AddTag(string tag)
    {
        if (!HasTag(tag))
        {
            audienceTags.Add(tag);
        }
    }

    /// <summary>
    /// 移除标签
    /// </summary>
    public void RemoveTag(string tag)
    {
        audienceTags.Remove(tag);
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保听众名称不为空
        if (string.IsNullOrEmpty(audienceName))
        {
            audienceName = name;
        }

        // 确保听众ID不为空
        if (string.IsNullOrEmpty(audienceId))
        {
            audienceId = name.Replace(" ", "_").ToLower();
        }

        // 确保DTV在合理范围内
        currentDTV = Mathf.Clamp(currentDTV, 0, 5);

        // 确保故事块数组长度正确
        if (unlockedStoryBlocks.Length != 6)
        {
            bool[] newArray = new bool[6];
            for (int i = 0; i < Mathf.Min(unlockedStoryBlocks.Length, 6); i++)
            {
                newArray[i] = unlockedStoryBlocks[i];
            }
            unlockedStoryBlocks = newArray;
        }

        // 移除空的关键词引用
        collectedKeywords.RemoveAll(k => k == null);
        expiredKeywords.RemoveAll(k => k == null);
        storyBlocks.RemoveAll(sb => sb == null);
        audienceTags.RemoveAll(tag => string.IsNullOrEmpty(tag));
    }

    #endregion

    #region 调试方法

    [ContextMenu("测试DTV增加")]
    private void TestIncreaseDTV()
    {
        if (Application.isPlaying)
        {
            IncreaseDTV(1);
        }
    }

    [ContextMenu("清理过期关键词")]
    private void TestCleanupExpiredKeywords()
    {
        if (Application.isPlaying)
        {
            CleanupExpiredKeywords();
        }
    }

    [ContextMenu("显示画像完整度")]
    private void ShowProfileCompleteness()
    {
        float completeness = GetProfileCompleteness();
        Debug.Log($"{audienceName} 画像完整度：{completeness:P}");
    }

    #endregion
}
