%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.5019608, g: 0.5019608, b: 0.5019608, a: 1}
  m_AmbientEquatorColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientGroundColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 1
    m_BakeResolution: 10
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 0
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 0
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 1024
    m_ReflectionCompression: 2
    m_MixedBakeMode: 1
    m_BakeBackend: 2
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 2
    m_PVRSampleCount: 32
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 32
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 0
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000000, guid: af80e8bff28dabf4ca8f5eb06bb60966,
    type: 2}
  m_UseShadowmask: 0
--- !u!196 &5
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666666
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &44477435
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100142, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 44477436}
  - component: {fileID: 44477438}
  - component: {fileID: 44477437}
  - component: {fileID: 44477439}
  m_Layer: 0
  m_Name: WallTypeA (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &44477436
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400142, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 44477435}
  m_LocalRotation: {x: 0.000000099748014, y: -0.7071068, z: 0.000000099748014, w: 0.7071068}
  m_LocalPosition: {x: -0, y: -0, z: 0.037}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 95
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!23 &44477437
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300106, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 44477435}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33b9bd8adf938ca43b5fc608c6c74e0a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &44477438
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300106, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 44477435}
  m_Mesh: {fileID: 4300000, guid: 80c7024b261b4e84db664b41ca4d628b, type: 3}
--- !u!65 &44477439
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 44477435}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.30000037, y: 4.2414737, z: 20}
  m_Center: {x: -0.1499998, y: 2.1207368, z: -2.0000005}
--- !u!1 &127521604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100182, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 127521605}
  - component: {fileID: 127521607}
  - component: {fileID: 127521606}
  m_Layer: 0
  m_Name: Tanker (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &127521605
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400182, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127521604}
  m_LocalRotation: {x: 0.29482514, y: 0.64280945, z: -0.29475117, w: -0.6426477}
  m_LocalPosition: {x: -2.1720588, y: -0.21448135, z: -8.380281}
  m_LocalScale: {x: 0.38863468, y: 0.3886346, z: 1.2343373}
  m_Children: []
  m_Father: {fileID: 787852766}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: -90.0144, z: 49.2772}
--- !u!23 &127521606
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300132, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127521604}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 822331cfd0cd1924d8b742e2afa46eb2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &127521607
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300132, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127521604}
  m_Mesh: {fileID: 4300000, guid: e8a3332341b71ab438b1545155cabf1c, type: 3}
--- !u!1 &510653866
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 510653867}
  m_Layer: 0
  m_Name: Tankers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &510653867
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 510653866}
  m_LocalRotation: {x: 0, y: 0.41297534, z: 0, w: 0.9107423}
  m_LocalPosition: {x: 15.81, y: 0, z: -1.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 516639387}
  - {fileID: 1932321374}
  - {fileID: 1376012471}
  m_Father: {fileID: 1895760024}
  m_RootOrder: 102
  m_LocalEulerAnglesHint: {x: 0, y: 48.7838, z: 0}
--- !u!1 &516639386
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100182, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 516639387}
  - component: {fileID: 516639389}
  - component: {fileID: 516639388}
  m_Layer: 0
  m_Name: Tanker (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &516639387
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400182, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 516639386}
  m_LocalRotation: {x: 0.29482514, y: 0.64280945, z: -0.29475117, w: -0.6426477}
  m_LocalPosition: {x: -2.1720588, y: -0.21448135, z: -8.380281}
  m_LocalScale: {x: 0.38863468, y: 0.3886346, z: 1.2343373}
  m_Children: []
  m_Father: {fileID: 510653867}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: -90.0144, z: 49.2772}
--- !u!23 &516639388
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300132, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 516639386}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 822331cfd0cd1924d8b742e2afa46eb2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &516639389
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300132, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 516639386}
  m_Mesh: {fileID: 4300000, guid: e8a3332341b71ab438b1545155cabf1c, type: 3}
--- !u!1001 &581677108
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 817197998}
    m_Modifications:
    - target: {fileID: 100088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_Name
      value: Enemy
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.x
      value: -6.55
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.z
      value: -5.88
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.874691
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.48468107
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -237.9832
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 817197999}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: OnUse
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
--- !u!1 &718924956
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100074, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 718924957}
  - component: {fileID: 718924959}
  - component: {fileID: 718924958}
  m_Layer: 0
  m_Name: WallLamp (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &718924957
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400074, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 718924956}
  m_LocalRotation: {x: -1.0165751e-15, y: 1, z: -0.000000021855694, w: -0.00000004371139}
  m_LocalPosition: {x: 2, y: 2.7858715, z: 0.06902795}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 98
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!23 &718924958
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300054, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 718924956}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 11fb2d248febdc846b81524c9ac75a5f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &718924959
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300054, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 718924956}
  m_Mesh: {fileID: 4300000, guid: c55569fb51e2839469e10cf061ee7ed3, type: 3}
--- !u!1 &724197316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 724197321}
  - component: {fileID: 724197320}
  - component: {fileID: 724197319}
  - component: {fileID: 724197318}
  - component: {fileID: 724197317}
  m_Layer: 0
  m_Name: 'Info: Increment On Destroy'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &724197317
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 724197316}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 8
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags:
    - Player
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: 
  questState: 0
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  setAnotherQuestEntryState: 0
  anotherQuestEntryNumber: 1
  anotherQuestEntryState: 0
  luaCode: 
  sequence: 
  sequenceSpeaker: {fileID: 0}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 1
  alertMessage: Increment On Destroy components increment a\nvariable when each enemy
    GameObject is destroyed.
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkEntryID: -1
  barkEntryTitle: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: 
  conversationConversant: {fileID: 0}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  startConversationEntryTitle: 
  overrideDialogueUI: {fileID: 0}
  exclusive: 0
  replace: 0
  queue: 0
  skipIfNoValidEntries: 1
  preventRestartOnSameFrameEnded: 0
  stopConversationOnTriggerExit: 0
  marginToAllowTriggerExit: 0.2
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 724197316}
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 1018410641}
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!23 &724197318
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 724197316}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &724197319
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 724197316}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &724197320
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 724197316}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &724197321
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 724197316}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6, y: 0.5, z: -2}
  m_LocalScale: {x: 1, y: 1, z: 2}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &772619051
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4240220743476432, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d4353500a4689e444a5a0ed100a259e5, type: 3}
--- !u!1 &787852765
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 787852766}
  m_Layer: 0
  m_Name: Tankers (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &787852766
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 787852765}
  m_LocalRotation: {x: 0, y: -0.37703946, z: 0, w: 0.92619723}
  m_LocalPosition: {x: -6.0705643, y: 0, z: 3.1458006}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 127521605}
  - {fileID: 1207681476}
  - {fileID: 1253456248}
  m_Father: {fileID: 1895760024}
  m_RootOrder: 103
  m_LocalEulerAnglesHint: {x: 0, y: -44.3008, z: 0}
--- !u!1 &817197997
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 817197998}
  - component: {fileID: 817198000}
  - component: {fileID: 817197999}
  m_Layer: 0
  m_Name: Enemies
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &817197998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 817197997}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 933343031}
  - {fileID: 2086461777}
  - {fileID: 2074564801}
  - {fileID: 1306235740}
  - {fileID: 1000578806}
  m_Father: {fileID: 0}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &817197999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 817197997}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 32
  condition:
    luaConditions:
    - Variable["enemiesKilled"] >= 5
    questConditions:
    - questName: Enemy Attack
      questState: 2
      checkQuestEntry: 0
      entryNumber: 0
      questEntryState: 0
    acceptedTags: []
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: Enemy Attack
  questState: 4
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  setAnotherQuestEntryState: 0
  anotherQuestEntryNumber: 1
  anotherQuestEntryState: 0
  luaCode: 
  sequence: 
  sequenceSpeaker: {fileID: 0}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 1
  alertMessage: Objective Complete:\n5 Enemies Defeated
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkEntryID: -1
  barkEntryTitle: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: 
  conversationConversant: {fileID: 0}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  startConversationEntryTitle: 
  overrideDialogueUI: {fileID: 0}
  exclusive: 0
  replace: 0
  queue: 0
  skipIfNoValidEntries: 1
  preventRestartOnSameFrameEnded: 0
  stopConversationOnTriggerExit: 0
  marginToAllowTriggerExit: 0.2
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls: []
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!114 &817198000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 817197997}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b6dc48c641bb39742af066b7a0250f33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_note: The Dialogue System Trigger is called by each Enemy's IncrementOnDestroy.
    If the quest is active and the enemiesKilled variable >= 5, it marks the quest
    successful.
--- !u!1 &832190524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 832190529}
  - component: {fileID: 832190528}
  - component: {fileID: 832190527}
  - component: {fileID: 832190526}
  - component: {fileID: 832190525}
  m_Layer: 0
  m_Name: 'Info: Bark Groups'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &832190525
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832190524}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 8
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags:
    - Player
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: 
  questState: 0
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  setAnotherQuestEntryState: 0
  anotherQuestEntryNumber: 1
  anotherQuestEntryState: 0
  luaCode: 
  sequence: 
  sequenceSpeaker: {fileID: 0}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 1
  alertMessage: Enemies belong to a Bark Group,\nallowing only one to bark at a time.
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkEntryID: -1
  barkEntryTitle: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: 
  conversationConversant: {fileID: 0}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  startConversationEntryTitle: 
  overrideDialogueUI: {fileID: 0}
  exclusive: 0
  replace: 0
  queue: 0
  skipIfNoValidEntries: 1
  preventRestartOnSameFrameEnded: 0
  stopConversationOnTriggerExit: 0
  marginToAllowTriggerExit: 0.2
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 832190524}
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!23 &832190526
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832190524}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &832190527
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832190524}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &832190528
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832190524}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &832190529
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832190524}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 3, y: 0.5, z: -3.619}
  m_LocalScale: {x: 6, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &838226341
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 100002, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100006, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100044, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100050, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100056, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_Name
      value: Tanker
      objectReference: {fileID: 0}
    - target: {fileID: 100056, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100060, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100062, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100066, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100080, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100088, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100092, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100096, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100102, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100124, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100144, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100146, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100154, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100156, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100158, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100166, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100178, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 100182, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_Name
      value: Tanker
      objectReference: {fileID: 0}
    - target: {fileID: 100182, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400068, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -6
      objectReference: {fileID: 0}
    - target: {fileID: 400074, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -6
      objectReference: {fileID: 0}
    - target: {fileID: 400074, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -180
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 56aa82917f38f54449255fb7967f7a3a, type: 3}
--- !u!1001 &896843758
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 817197998}
    m_Modifications:
    - target: {fileID: 100088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_Name
      value: Enemy
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.x
      value: 7.94
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.93
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.8829305
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.46950376
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 817197999}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: OnUse
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
--- !u!4 &933343031 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b,
    type: 3}
  m_PrefabInstance: {fileID: 1422063209}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1000578806 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b,
    type: 3}
  m_PrefabInstance: {fileID: 581677108}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1018410641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1018410646}
  - component: {fileID: 1018410645}
  - component: {fileID: 1018410644}
  - component: {fileID: 1018410643}
  - component: {fileID: 1018410642}
  m_Layer: 0
  m_Name: 'Info: Increment On Destroy'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1018410642
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018410641}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 8
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags:
    - Player
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: 
  questState: 0
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  setAnotherQuestEntryState: 0
  anotherQuestEntryNumber: 1
  anotherQuestEntryState: 0
  luaCode: 
  sequence: 
  sequenceSpeaker: {fileID: 0}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 1
  alertMessage: Increment On Destroy components increment a\nvariable when each enemy
    GameObject is destroyed.
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkEntryID: -1
  barkEntryTitle: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: 
  conversationConversant: {fileID: 0}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  startConversationEntryTitle: 
  overrideDialogueUI: {fileID: 0}
  exclusive: 0
  replace: 0
  queue: 0
  skipIfNoValidEntries: 1
  preventRestartOnSameFrameEnded: 0
  stopConversationOnTriggerExit: 0
  marginToAllowTriggerExit: 0.2
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1018410641}
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 724197316}
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!23 &1018410643
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018410641}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &1018410644
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018410641}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &1018410645
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018410641}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1018410646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018410641}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.5, z: -2}
  m_LocalScale: {x: 1, y: 1, z: 2}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1026855500 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100046, guid: 6f06dd98a5f62894a9f9c0ce7e700682,
    type: 3}
  m_PrefabInstance: {fileID: 1506695490}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1026855509
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1026855500}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bf9481d631e872c45a9f702509a148ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  tagSpecificElements: []
  layerSpecificElements: []
--- !u!1 &1132238482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100074, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1132238483}
  - component: {fileID: 1132238485}
  - component: {fileID: 1132238484}
  m_Layer: 0
  m_Name: WallLamp (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1132238483
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400074, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1132238482}
  m_LocalRotation: {x: -1.0165751e-15, y: 1, z: -0.000000021855694, w: -0.00000004371139}
  m_LocalPosition: {x: 10, y: 2.7858715, z: 0.06902795}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 100
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!23 &1132238484
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300054, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1132238482}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 11fb2d248febdc846b81524c9ac75a5f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1132238485
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300054, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1132238482}
  m_Mesh: {fileID: 4300000, guid: c55569fb51e2839469e10cf061ee7ed3, type: 3}
--- !u!1 &1134399586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100076, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1134399587}
  - component: {fileID: 1134399589}
  - component: {fileID: 1134399588}
  m_Layer: 0
  m_Name: WallDividerA (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1134399587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400076, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134399586}
  m_LocalRotation: {x: 0.000000099748014, y: -0.7071068, z: 0.000000099748014, w: 0.7071068}
  m_LocalPosition: {x: 4, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 97
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!23 &1134399588
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134399586}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7a27f547c4a7e434086a9186d53ce37d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1134399589
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134399586}
  m_Mesh: {fileID: 4300000, guid: ba4086086b6963e428f5771405982842, type: 3}
--- !u!1 &1201663572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100068, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1201663573}
  - component: {fileID: 1201663574}
  m_Layer: 0
  m_Name: Point light (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1201663573
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400068, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1201663572}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 10, y: 2.7014902, z: -1.2486317}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 101
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &1201663574
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 10800016, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1201663572}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 2
  m_Shape: 0
  m_Color: {r: 0.428143, g: 0.7045279, b: 0.9098039, a: 1}
  m_Intensity: 12.8
  m_Range: 3.88
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!1 &1207681475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1207681476}
  - component: {fileID: 1207681479}
  - component: {fileID: 1207681478}
  - component: {fileID: 1207681477}
  m_Layer: 0
  m_Name: Tanker (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1207681476
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207681475}
  m_LocalRotation: {x: -0.5000001, y: -0.5, z: 0.5, w: 0.49999994}
  m_LocalPosition: {x: -2.1720192, y: 0.158, z: -8.530014}
  m_LocalScale: {x: 0.38863468, y: 0.3886346, z: 1.2343373}
  m_Children: []
  m_Father: {fileID: 787852766}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 90}
--- !u!65 &1207681477
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6500000, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207681475}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3, y: 6, z: 2.539998}
  m_Center: {x: 2.336361, y: -0.00000047425823, z: 0.000000020924537}
--- !u!23 &1207681478
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207681475}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 822331cfd0cd1924d8b742e2afa46eb2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1207681479
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207681475}
  m_Mesh: {fileID: 4300000, guid: e8a3332341b71ab438b1545155cabf1c, type: 3}
--- !u!1 &1253456247
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1253456248}
  - component: {fileID: 1253456250}
  - component: {fileID: 1253456249}
  m_Layer: 0
  m_Name: Tanker (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1253456248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1253456247}
  m_LocalRotation: {x: 0.29482514, y: 0.64280945, z: -0.29475117, w: -0.6426477}
  m_LocalPosition: {x: -2.172, y: -0.172, z: -9.878}
  m_LocalScale: {x: 0.3886348, y: 0.38863462, z: 1.2343377}
  m_Children: []
  m_Father: {fileID: 787852766}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: -90.0144, z: 49.2772}
--- !u!23 &1253456249
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1253456247}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 822331cfd0cd1924d8b742e2afa46eb2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1253456250
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1253456247}
  m_Mesh: {fileID: 4300000, guid: e8a3332341b71ab438b1545155cabf1c, type: 3}
--- !u!4 &1306235740 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b,
    type: 3}
  m_PrefabInstance: {fileID: 2060269487}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1376012470
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1376012471}
  - component: {fileID: 1376012473}
  - component: {fileID: 1376012472}
  m_Layer: 0
  m_Name: Tanker (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1376012471
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376012470}
  m_LocalRotation: {x: 0.29482514, y: 0.64280945, z: -0.29475117, w: -0.6426477}
  m_LocalPosition: {x: -2.172, y: -0.172, z: -9.878}
  m_LocalScale: {x: 0.3886348, y: 0.38863462, z: 1.2343377}
  m_Children: []
  m_Father: {fileID: 510653867}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: -90.0144, z: 49.2772}
--- !u!23 &1376012472
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376012470}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 822331cfd0cd1924d8b742e2afa46eb2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1376012473
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376012470}
  m_Mesh: {fileID: 4300000, guid: e8a3332341b71ab438b1545155cabf1c, type: 3}
--- !u!1001 &1422063209
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 817197998}
    m_Modifications:
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.x
      value: 10.21
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.z
      value: -3.62
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.8829305
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.46950376
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 817197999}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: OnUse
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
--- !u!1 &1443171654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1443171657}
  - component: {fileID: 1443171656}
  - component: {fileID: 1443171655}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1443171655
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1443171654}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &1443171656
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1443171654}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 5
--- !u!4 &1443171657
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1443171654}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1506695490
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalPosition.z
      value: -7
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_RootOrder
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 400046, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8200000, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: panLevelCustomCurve.m_RotationOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8200000, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: reverbZoneMixCustomCurve.m_RotationOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8200002, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: panLevelCustomCurve.m_RotationOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8200002, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: reverbZoneMixCustomCurve.m_RotationOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8200002, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 11400002, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 8200000, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 6f06dd98a5f62894a9f9c0ce7e700682, type: 3}
--- !u!1 &1604786718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100076, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1604786719}
  - component: {fileID: 1604786721}
  - component: {fileID: 1604786720}
  m_Layer: 0
  m_Name: WallDividerA (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1604786719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400076, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604786718}
  m_LocalRotation: {x: 0.000000099748014, y: -0.7071068, z: 0.000000099748014, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 96
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!23 &1604786720
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604786718}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7a27f547c4a7e434086a9186d53ce37d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1604786721
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604786718}
  m_Mesh: {fileID: 4300000, guid: ba4086086b6963e428f5771405982842, type: 3}
--- !u!1 &1682202580
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100068, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1682202581}
  - component: {fileID: 1682202582}
  m_Layer: 0
  m_Name: Point light (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1682202581
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400068, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1682202580}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 4, z: -7}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 104
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &1682202582
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 10800016, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1682202580}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 2
  m_Shape: 0
  m_Color: {r: 0.428143, g: 0.7045279, b: 0.9098039, a: 1}
  m_Intensity: 6
  m_Range: 4
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!1 &1717261684
GameObject:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717261685}
  m_Layer: 0
  m_Name: GameDrawManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1717261685
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717261684}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.3121986, y: 0, z: 12.798235}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1720645376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100068, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1720645377}
  - component: {fileID: 1720645378}
  m_Layer: 0
  m_Name: Point light (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1720645377
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400068, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1720645376}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 2.7014902, z: -1.2486317}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1895760024}
  m_RootOrder: 99
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &1720645378
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 10800016, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1720645376}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 2
  m_Shape: 0
  m_Color: {r: 0.428143, g: 0.7045279, b: 0.9098039, a: 1}
  m_Intensity: 12.8
  m_Range: 3.88
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!1001 &1873632917
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 817197998}
    m_Modifications:
    - target: {fileID: 100088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_Name
      value: Enemy
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.6458955
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.8480176
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.9744626
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.22454974
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -205.95279
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 817197999}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: OnUse
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
--- !u!4 &1895760024 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400000, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 838226341}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1932321373
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1932321374}
  - component: {fileID: 1932321377}
  - component: {fileID: 1932321376}
  - component: {fileID: 1932321375}
  m_Layer: 0
  m_Name: Tanker (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1932321374
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400056, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932321373}
  m_LocalRotation: {x: -0.5000001, y: -0.5, z: 0.5, w: 0.49999994}
  m_LocalPosition: {x: -2.1720192, y: 0.158, z: -8.530014}
  m_LocalScale: {x: 0.38863468, y: 0.3886346, z: 1.2343373}
  m_Children: []
  m_Father: {fileID: 510653867}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 90}
--- !u!65 &1932321375
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6500000, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932321373}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3, y: 6, z: 2.539998}
  m_Center: {x: 2.336361, y: -0.00000047425823, z: 0.000000020924537}
--- !u!23 &1932321376
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932321373}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 822331cfd0cd1924d8b742e2afa46eb2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1932321377
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300042, guid: 56aa82917f38f54449255fb7967f7a3a,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932321373}
  m_Mesh: {fileID: 4300000, guid: e8a3332341b71ab438b1545155cabf1c, type: 3}
--- !u!1 &1951613461
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1951613462}
  m_Layer: 0
  m_Name: Spawnpoint from DemoScene1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1951613462
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1951613461}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: -8}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1966953160
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 100070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.72
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_LocalPosition.z
      value: -4.767
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_LocalRotation.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.00000016292068
      objectReference: {fileID: 0}
    - target: {fileID: 400070, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
      propertyPath: m_RootOrder
      value: 9
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5d271d9ea0c3c7c408e8785b0221d4ad, type: 3}
--- !u!1 &2006123368
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2006123369}
  - component: {fileID: 2006123373}
  - component: {fileID: 2006123372}
  - component: {fileID: 2006123371}
  - component: {fileID: 2006123370}
  m_Layer: 0
  m_Name: Door to DemoScene1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2006123369
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006123368}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 1.5, z: -9}
  m_LocalScale: {x: 4, y: 3, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2006123370
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006123368}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 32
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags: []
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: 
  questState: 0
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  setAnotherQuestEntryState: 0
  anotherQuestEntryNumber: 1
  anotherQuestEntryState: 0
  luaCode: 
  sequence: LoadLevel(DemoScene1, Spawnpoint from DemoScene2)
  sequenceSpeaker: {fileID: 0}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 1
  alertMessage: 
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkEntryID: -1
  barkEntryTitle: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: 
  conversationConversant: {fileID: 0}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  startConversationEntryTitle: 
  overrideDialogueUI: {fileID: 0}
  exclusive: 0
  replace: 0
  queue: 0
  skipIfNoValidEntries: 1
  preventRestartOnSameFrameEnded: 0
  stopConversationOnTriggerExit: 0
  marginToAllowTriggerExit: 0.2
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls: []
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!114 &2006123371
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006123368}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274b80ab5b659c646ba9338870d14625, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_overrideName: To DemoScene1
  m_overrideUseMessage: Spacebar to enter
  maxUseDistance: 3
  events:
    onSelect:
      m_PersistentCalls:
        m_Calls: []
    onDeselect:
      m_PersistentCalls:
        m_Calls: []
    onUse:
      m_PersistentCalls:
        m_Calls: []
--- !u!65 &2006123372
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006123368}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &2006123373
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006123368}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &2060269487
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 817197998}
    m_Modifications:
    - target: {fileID: 100088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_Name
      value: Enemy
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.x
      value: -5.27
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.6
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.9201088
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.39166293
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -226.116
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 817197999}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: OnUse
      objectReference: {fileID: 0}
    - target: {fileID: 11437546, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
      propertyPath: onIncrement.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 352b04850feb3eb4e9f7b144993fe65b, type: 3}
--- !u!4 &2074564801 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b,
    type: 3}
  m_PrefabInstance: {fileID: 1873632917}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &2086461777 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400088, guid: 352b04850feb3eb4e9f7b144993fe65b,
    type: 3}
  m_PrefabInstance: {fileID: 896843758}
  m_PrefabAsset: {fileID: 0}
