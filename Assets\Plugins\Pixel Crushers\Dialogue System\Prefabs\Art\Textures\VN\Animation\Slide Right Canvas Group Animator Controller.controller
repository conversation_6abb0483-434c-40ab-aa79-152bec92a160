%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!91 &9100000
AnimatorController:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Slide Right Canvas Group Animator Controller
  serializedVersion: 2
  m_AnimatorParameters:
  - m_Name: Show
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  - m_Name: Hide
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  m_AnimatorLayers:
  - serializedVersion: 3
    m_Name: Base Layer
    m_StateMachine: {fileID: 110700000}
    m_Mask: {fileID: 0}
    m_BlendingMode: 0
    m_SyncedLayerIndex: -1
    m_StateMachineMotionSetIndex: 0
    m_DefaultWeight: 0
    m_IKPass: 0
    m_SyncedLayerAffectsTiming: 0
    m_Controller: {fileID: 9100000}
--- !u!1101 &110176745
Transition:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_SrcState: {fileID: 0}
  m_DstState: {fileID: 110281693}
  m_TransitionDuration: .274879694
  m_TransitionOffset: .268148899
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Hide
    m_EventTreshold: 0
    m_ExitTime: .899999976
  m_Atomic: 0
  m_Solo: 0
  m_Mute: 0
  m_CanTransitionToSelf: 0
--- !u!1101 &110180422
Transition:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_SrcState: {fileID: 0}
  m_DstState: {fileID: 110200000}
  m_TransitionDuration: .100000001
  m_TransitionOffset: 0
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Show
    m_EventTreshold: 0
    m_ExitTime: .899999976
  m_Atomic: 0
  m_Solo: 0
  m_Mute: 0
  m_CanTransitionToSelf: 0
--- !u!1102 &110200000
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Show
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 7400000, guid: 53783d90e1ea9c44da6eea1c7fc32d33, type: 2}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 120, y: -192, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1102 &110213112
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Start
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 0}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 126, y: -252, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1102 &110281693
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Hide
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 7400000, guid: ab8e54a30d440774e8bb19917c4b221d, type: 2}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 120, y: -132, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1107 &110700000
StateMachine:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Base Layer
  m_DefaultState: {fileID: 110213112}
  m_States:
  - {fileID: 110200000}
  - {fileID: 110281693}
  - {fileID: 110213112}
  m_ChildStateMachine: []
  m_ChildStateMachinePosition: []
  m_OrderedTransitions:
    data:
      first: {fileID: 0}
      second:
      - {fileID: 110180422}
      - {fileID: 110176745}
    data:
      first: {fileID: 110200000}
      second: []
    data:
      first: {fileID: 110281693}
      second: []
  m_MotionSetCount: 1
  m_AnyStatePosition: {x: -96, y: -156, z: 0}
  m_ParentStateMachinePosition: {x: 800, y: 20, z: 0}
