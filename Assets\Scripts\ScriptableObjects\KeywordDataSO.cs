using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 关键词数据 ScriptableObject
/// 包含关键词信息、演化系统、组合配方等
/// </summary>
[CreateAssetMenu(fileName = "KeywordData", menuName = "Midnight Broadcasting/Keyword Data")]
public class KeywordDataSO : ScriptableObject
{
    [Header("=== 基础信息 ===")]
    [Tooltip("关键词文本")]
    public string keyword;

    [Tooltip("关键词类型")]
    public KeywordType type = KeywordType.Basic;

    [Tooltip("关键词描述")]
    [TextArea(2, 4)]
    public string description;

    [Header("=== 时间属性 ===")]
    [Tooltip("收集时间（字符串格式）")]
    public string collectedTimeString = "";

    [Tooltip("过期时间（字符串格式）")]
    public string expiryTimeString = "";

    [Tooltip("保质期（天数，0表示永不过期）")]
    public int shelfLifeDays = 0;

    [Header("=== 演化系统 ===")]
    [Tooltip("演化后的形态")]
    public KeywordDataSO evolvedForm;

    [Tooltip("是否已经演化")]
    public bool hasEvolved = false;

    [Tooltip("演化条件描述")]
    [TextArea(2, 3)]
    public string evolutionCondition;

    [Header("=== 组合系统 ===")]
    [Tooltip("可参与的组合配方")]
    public List<KeywordCombinationSO> possibleCombinations = new List<KeywordCombinationSO>();

    [Header("=== 游戏属性 ===")]
    [Tooltip("关键词权重（影响推理成功率）")]
    [Range(0f, 1f)]
    public float weight = 1f;

    [Tooltip("是否为稀有关键词")]
    public bool isRare = false;

    [Tooltip("关联的听众ID")]
    public string associatedAudienceId = "";

    // 运行时属性
    public DateTime CollectedTime
    {
        get
        {
            if (DateTime.TryParse(collectedTimeString, out DateTime result))
                return result;
            return DateTime.Now;
        }
        set { collectedTimeString = value.ToString(); }
    }

    public DateTime ExpiryTime
    {
        get
        {
            if (DateTime.TryParse(expiryTimeString, out DateTime result))
                return result;
            return DateTime.MaxValue;
        }
        set { expiryTimeString = value.ToString(); }
    }

    #region 业务逻辑方法

    /// <summary>
    /// 检查关键词是否已过期
    /// </summary>
    public bool IsExpired
    {
        get
        {
            if (shelfLifeDays == 0) return false; // 永不过期
            return DateTime.Now > ExpiryTime;
        }
    }

    /// <summary>
    /// 检查是否可以演化
    /// </summary>
    public bool CanEvolve
    {
        get { return evolvedForm != null && !hasEvolved; }
    }

    /// <summary>
    /// 初始化关键词（设置收集时间和过期时间）
    /// </summary>
    public void Initialize()
    {
        CollectedTime = DateTime.Now;
        if (shelfLifeDays > 0)
        {
            ExpiryTime = DateTime.Now.AddDays(shelfLifeDays);
        }
    }

    /// <summary>
    /// 尝试演化关键词
    /// </summary>
    public KeywordDataSO TryEvolve()
    {
        if (!CanEvolve) return null;

        hasEvolved = true;

        // 创建演化后的关键词实例
        var evolvedKeyword = Instantiate(evolvedForm);
        evolvedKeyword.Initialize();
        evolvedKeyword.associatedAudienceId = this.associatedAudienceId;

        Debug.Log($"关键词演化：{keyword} -> {evolvedKeyword.keyword}");
        return evolvedKeyword;
    }

    /// <summary>
    /// 检查是否可以与其他关键词组合
    /// </summary>
    public bool CanCombineWith(List<KeywordDataSO> availableKeywords)
    {
        foreach (var combination in possibleCombinations)
        {
            if (combination != null && combination.CanCombine(availableKeywords))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 获取可用的组合配方
    /// </summary>
    public List<KeywordCombinationSO> GetAvailableCombinations(List<KeywordDataSO> availableKeywords)
    {
        List<KeywordCombinationSO> availableCombinations = new List<KeywordCombinationSO>();

        foreach (var combination in possibleCombinations)
        {
            if (combination != null && combination.CanCombine(availableKeywords))
            {
                availableCombinations.Add(combination);
            }
        }

        return availableCombinations;
    }

    /// <summary>
    /// 获取剩余有效时间（小时）
    /// </summary>
    public float GetRemainingHours()
    {
        if (shelfLifeDays == 0) return float.MaxValue;

        TimeSpan remaining = ExpiryTime - DateTime.Now;
        return (float)remaining.TotalHours;
    }

    /// <summary>
    /// 检查关键词是否即将过期（24小时内）
    /// </summary>
    public bool IsExpiringSoon()
    {
        if (shelfLifeDays == 0) return false;
        return GetRemainingHours() <= 24f;
    }

    /// <summary>
    /// 获取关键词状态描述
    /// </summary>
    public string GetStatusDescription()
    {
        if (IsExpired) return "已过期";
        if (IsExpiringSoon()) return "即将过期";
        if (hasEvolved) return "已演化";
        if (CanEvolve) return "可演化";
        return "正常";
    }

    /// <summary>
    /// 复制关键词数据
    /// </summary>
    public KeywordDataSO CreateCopy()
    {
        var copy = Instantiate(this);
        copy.name = this.name + "_Copy";
        return copy;
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保关键词不为空
        if (string.IsNullOrEmpty(keyword))
        {
            keyword = name;
        }

        // 确保权重在合理范围内
        weight = Mathf.Clamp01(weight);

        // 确保保质期不为负数
        shelfLifeDays = Mathf.Max(0, shelfLifeDays);
    }

    #endregion

    #region 调试方法

    [ContextMenu("测试演化")]
    private void TestEvolution()
    {
        if (Application.isPlaying)
        {
            var evolved = TryEvolve();
            if (evolved != null)
            {
                Debug.Log($"演化成功：{keyword} -> {evolved.keyword}");
            }
            else
            {
                Debug.Log($"无法演化：{keyword}");
            }
        }
    }

    [ContextMenu("设置为即将过期")]
    private void SetExpiringSoon()
    {
        if (Application.isPlaying)
        {
            ExpiryTime = DateTime.Now.AddHours(12);
            Debug.Log($"关键词 {keyword} 设置为12小时后过期");
        }
    }

    #endregion
}
