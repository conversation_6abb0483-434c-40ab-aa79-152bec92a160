using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 关键词服务
/// 负责处理关键词相关的业务逻辑
/// </summary>
public class KeywordService : MonoBehaviour
{
    [Header("=== 关键词模板 ===")]
    [Tooltip("所有关键词模板")]
    public List<KeywordDataSO> keywordTemplates = new List<KeywordDataSO>();

    [Header("=== 组合配方 ===")]
    [Tooltip("所有关键词组合配方")]
    public List<KeywordCombinationSO> combinationTemplates = new List<KeywordCombinationSO>();

    // 运行时数据
    private List<KeywordDataSO> activeKeywords = new List<KeywordDataSO>();

    // 单例模式
    public static KeywordService Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    #endregion

    #region 关键词创建和管理

    /// <summary>
    /// 创建关键词实例
    /// </summary>
    public KeywordDataSO CreateKeyword(string keywordText, string audienceId = "")
    {
        var template = FindKeywordTemplate(keywordText);
        if (template == null)
        {
            Debug.LogWarning($"未找到关键词模板：{keywordText}");
            return null;
        }

        var instance = Instantiate(template);
        InitializeKeyword(instance);
        instance.associatedAudienceId = audienceId;
        
        activeKeywords.Add(instance);
        return instance;
    }

    /// <summary>
    /// 初始化关键词
    /// </summary>
    public void InitializeKeyword(KeywordDataSO keyword)
    {
        if (keyword == null) return;

        keyword.CollectedTime = DateTime.Now;
        if (keyword.shelfLifeDays > 0)
        {
            keyword.ExpiryTime = DateTime.Now.AddDays(keyword.shelfLifeDays);
        }
        else
        {
            keyword.ExpiryTime = DateTime.MaxValue;
        }

        Debug.Log($"关键词初始化：{keyword.keyword}，过期时间：{keyword.ExpiryTime}");
    }

    /// <summary>
    /// 查找关键词模板
    /// </summary>
    public KeywordDataSO FindKeywordTemplate(string keywordText)
    {
        return keywordTemplates.FirstOrDefault(k => k != null && k.keyword == keywordText);
    }

    /// <summary>
    /// 移除关键词
    /// </summary>
    public void RemoveKeyword(KeywordDataSO keyword)
    {
        if (keyword != null)
        {
            activeKeywords.Remove(keyword);
        }
    }

    #endregion

    #region 关键词状态检查

    /// <summary>
    /// 检查关键词是否已过期
    /// </summary>
    public bool IsKeywordExpired(KeywordDataSO keyword)
    {
        if (keyword == null) return true;
        if (keyword.shelfLifeDays == 0) return false; // 永不过期
        
        return DateTime.Now > keyword.ExpiryTime;
    }

    /// <summary>
    /// 检查是否可以演化
    /// </summary>
    public bool CanEvolve(KeywordDataSO keyword)
    {
        if (keyword == null) return false;
        return keyword.evolvedForm != null && !keyword.hasEvolved;
    }

    /// <summary>
    /// 获取剩余有效时间（小时）
    /// </summary>
    public float GetRemainingHours(KeywordDataSO keyword)
    {
        if (keyword == null) return 0f;
        if (keyword.shelfLifeDays == 0) return float.MaxValue;

        TimeSpan remaining = keyword.ExpiryTime - DateTime.Now;
        return (float)remaining.TotalHours;
    }

    /// <summary>
    /// 获取关键词状态描述
    /// </summary>
    public string GetKeywordStatusDescription(KeywordDataSO keyword)
    {
        if (keyword == null) return "无效";
        if (IsKeywordExpired(keyword)) return "已过期";
        
        float remainingHours = GetRemainingHours(keyword);
        if (remainingHours == float.MaxValue) return "永久有效";
        if (remainingHours < 24) return $"剩余 {remainingHours:F1} 小时";
        
        int remainingDays = Mathf.FloorToInt(remainingHours / 24);
        return $"剩余 {remainingDays} 天";
    }

    #endregion

    #region 关键词演化

    /// <summary>
    /// 尝试演化关键词
    /// </summary>
    public KeywordDataSO TryEvolveKeyword(KeywordDataSO keyword)
    {
        if (!CanEvolve(keyword)) return null;

        keyword.hasEvolved = true;

        // 创建演化后的关键词实例
        var evolvedKeyword = Instantiate(keyword.evolvedForm);
        InitializeKeyword(evolvedKeyword);
        evolvedKeyword.associatedAudienceId = keyword.associatedAudienceId;

        activeKeywords.Add(evolvedKeyword);
        Debug.Log($"关键词演化：{keyword.keyword} -> {evolvedKeyword.keyword}");
        
        return evolvedKeyword;
    }

    /// <summary>
    /// 检查演化条件
    /// </summary>
    public bool CheckEvolutionCondition(KeywordDataSO keyword, AudienceDataSO audience)
    {
        if (!CanEvolve(keyword) || audience == null) return false;

        // 这里可以添加更复杂的演化条件检查
        // 例如：特定的DTV等级、特定的互动次数等
        
        return audience.currentDTV >= 3; // 简单示例：DTV达到3级才能演化
    }

    /// <summary>
    /// 处理所有关键词的演化检查
    /// </summary>
    public void ProcessKeywordEvolution()
    {
        var evolutionCandidates = activeKeywords.Where(k => CanEvolve(k)).ToList();
        
        foreach (var keyword in evolutionCandidates)
        {
            var audience = AudienceService.Instance?.FindAudienceById(keyword.associatedAudienceId);
            if (CheckEvolutionCondition(keyword, audience))
            {
                TryEvolveKeyword(keyword);
            }
        }
    }

    #endregion

    #region 关键词组合

    /// <summary>
    /// 检查是否可以与其他关键词组合
    /// </summary>
    public bool CanCombineWith(KeywordDataSO keyword, List<KeywordDataSO> availableKeywords)
    {
        if (keyword == null || availableKeywords == null) return false;

        foreach (var combination in keyword.possibleCombinations)
        {
            if (combination != null && CanExecuteCombination(combination, availableKeywords))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 获取可用的组合配方
    /// </summary>
    public List<KeywordCombinationSO> GetAvailableCombinations(List<KeywordDataSO> availableKeywords)
    {
        if (availableKeywords == null) return new List<KeywordCombinationSO>();

        List<KeywordCombinationSO> availableCombinations = new List<KeywordCombinationSO>();

        foreach (var combination in combinationTemplates)
        {
            if (combination != null && CanExecuteCombination(combination, availableKeywords))
            {
                availableCombinations.Add(combination);
            }
        }

        return availableCombinations;
    }

    /// <summary>
    /// 检查是否可以执行组合
    /// </summary>
    public bool CanExecuteCombination(KeywordCombinationSO combination, List<KeywordDataSO> availableKeywords)
    {
        if (combination == null || availableKeywords == null) return false;

        // 检查是否有足够的关键词
        foreach (var requiredKeyword in combination.requiredKeywords)
        {
            if (requiredKeyword == null) continue;
            
            bool hasKeyword = availableKeywords.Any(k => 
                k != null && 
                k.keyword == requiredKeyword.keyword && 
                !IsKeywordExpired(k));
                
            if (!hasKeyword) return false;
        }

        return true;
    }

    /// <summary>
    /// 执行关键词组合
    /// </summary>
    public KeywordDataSO ExecuteCombination(KeywordCombinationSO combination, List<KeywordDataSO> usedKeywords)
    {
        if (!CanExecuteCombination(combination, usedKeywords)) return null;

        // 计算成功率
        float successRate = CalculateCombinationSuccessRate(combination, usedKeywords);
        bool success = UnityEngine.Random.value <= successRate;

        if (success && combination.resultKeyword != null)
        {
            // 创建结果关键词
            var resultKeyword = CreateKeyword(combination.resultKeyword.keyword);
            
            // 消耗使用的关键词
            foreach (var usedKeyword in usedKeywords)
            {
                if (combination.requiredKeywords.Any(rk => rk.keyword == usedKeyword.keyword))
                {
                    RemoveKeyword(usedKeyword);
                }
            }

            Debug.Log($"关键词组合成功：{combination.combinationName} -> {resultKeyword.keyword}");
            return resultKeyword;
        }
        else
        {
            Debug.Log($"关键词组合失败：{combination.combinationName}");
            return null;
        }
    }

    /// <summary>
    /// 计算组合成功率
    /// </summary>
    public float CalculateCombinationSuccessRate(KeywordCombinationSO combination, List<KeywordDataSO> usedKeywords)
    {
        if (combination == null || usedKeywords == null) return 0f;

        // 基础成功率 = 1 - 难度
        float baseRate = 1f - combination.difficulty;

        // 关键词权重加成
        float weightBonus = 0f;
        foreach (var keyword in usedKeywords)
        {
            if (keyword != null)
            {
                weightBonus += keyword.weight * 0.1f; // 每个关键词最多贡献10%
            }
        }

        // 最终成功率
        float finalRate = Mathf.Clamp01(baseRate + weightBonus);
        
        Debug.Log($"组合 {combination.combinationName} 成功率：{finalRate:P2} (基础:{baseRate:P2} + 权重加成:{weightBonus:P2})");
        return finalRate;
    }

    #endregion

    #region 关键词清理

    /// <summary>
    /// 清理过期关键词
    /// </summary>
    public void CleanupExpiredKeywords()
    {
        var expiredKeywords = activeKeywords.Where(k => IsKeywordExpired(k)).ToList();
        
        foreach (var expired in expiredKeywords)
        {
            activeKeywords.Remove(expired);
            Debug.Log($"清理过期关键词：{expired.keyword}");
        }
    }

    /// <summary>
    /// 清理所有无效关键词
    /// </summary>
    public void CleanupInvalidKeywords()
    {
        activeKeywords.RemoveAll(k => k == null);
        CleanupExpiredKeywords();
    }

    #endregion

    #region 数据查询

    /// <summary>
    /// 获取所有活跃关键词
    /// </summary>
    public List<KeywordDataSO> GetActiveKeywords()
    {
        return activeKeywords.Where(k => k != null && !IsKeywordExpired(k)).ToList();
    }

    /// <summary>
    /// 根据类型获取关键词
    /// </summary>
    public List<KeywordDataSO> GetKeywordsByType(KeywordType type)
    {
        return activeKeywords.Where(k => k != null && k.keywordType == type && !IsKeywordExpired(k)).ToList();
    }

    /// <summary>
    /// 根据听众ID获取关键词
    /// </summary>
    public List<KeywordDataSO> GetKeywordsByAudience(string audienceId)
    {
        return activeKeywords.Where(k => 
            k != null && 
            k.associatedAudienceId == audienceId && 
            !IsKeywordExpired(k)).ToList();
    }

    /// <summary>
    /// 获取即将过期的关键词
    /// </summary>
    public List<KeywordDataSO> GetExpiringSoonKeywords(float hoursThreshold = 24f)
    {
        return activeKeywords.Where(k => 
            k != null && 
            !IsKeywordExpired(k) && 
            GetRemainingHours(k) <= hoursThreshold && 
            GetRemainingHours(k) != float.MaxValue).ToList();
    }

    #endregion

    #region 数据验证

    /// <summary>
    /// 验证关键词数据
    /// </summary>
    public bool ValidateKeywordData(KeywordDataSO keyword)
    {
        if (keyword == null) return false;

        bool isValid = true;

        // 检查基础数据
        if (string.IsNullOrEmpty(keyword.keyword))
        {
            Debug.LogWarning($"关键词缺少文本内容");
            isValid = false;
        }

        // 检查权重范围
        if (keyword.weight < 0f || keyword.weight > 1f)
        {
            Debug.LogWarning($"关键词 {keyword.keyword} 权重异常：{keyword.weight}");
            keyword.weight = Mathf.Clamp01(keyword.weight);
            isValid = false;
        }

        // 检查保质期
        if (keyword.shelfLifeDays < 0)
        {
            Debug.LogWarning($"关键词 {keyword.keyword} 保质期异常：{keyword.shelfLifeDays}");
            keyword.shelfLifeDays = Mathf.Max(0, keyword.shelfLifeDays);
            isValid = false;
        }

        return isValid;
    }

    /// <summary>
    /// 验证所有关键词数据
    /// </summary>
    public void ValidateAllKeywordData()
    {
        foreach (var keyword in keywordTemplates)
        {
            ValidateKeywordData(keyword);
        }

        foreach (var keyword in activeKeywords)
        {
            ValidateKeywordData(keyword);
        }
    }

    #endregion
}
