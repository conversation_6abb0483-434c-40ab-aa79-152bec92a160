﻿// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.Wrappers
{

    /// <summary>
    /// This script deselects the previous selectable when the pointer enters this one.
    /// </summary>
    [AddComponentMenu("Pixel Crushers/Common/UI/Deselect Previous On Pointer Enter")]
    public class DeselectPreviousOnPointerEnter : PixelCrushers.DeselectPreviousOnPointerEnter

    {
    }

}
