using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 存档槽位UI组件
/// 显示单个存档槽位的信息和状态
/// </summary>
public class SaveSlotUI : MonoBehaviour
{
    [Header("=== UI组件 ===")]
    [Tooltip("槽位背景")]
    public Image backgroundImage;
    
    [Tooltip("选中状态指示器")]
    public GameObject selectedIndicator;
    
    [Tooltip("存档名称文本")]
    public TextMeshProUGUI slotNameText;
    
    [Tooltip("保存时间文本")]
    public TextMeshProUGUI saveTimeText;
    
    [Tooltip("文件大小文本")]
    public TextMeshProUGUI fileSizeText;
    
    [Tooltip("存档描述文本")]
    public TextMeshProUGUI descriptionText;
    
    [Tooltip("存档预览图")]
    public Image previewImage;
    
    [Tooltip("空槽位提示")]
    public GameObject emptySlotIndicator;
    
    [Tooltip("点击按钮")]
    public Button clickButton;

    [Header("=== 视觉设置 ===")]
    [Tooltip("正常状态颜色")]
    public Color normalColor = Color.white;
    
    [Tooltip("选中状态颜色")]
    public Color selectedColor = Color.cyan;
    
    [Tooltip("空槽位颜色")]
    public Color emptyColor = Color.gray;

    // 数据
    public int slotIndex { get; private set; }
    public SaveSlotInfo saveInfo { get; private set; }
    private SaveLoadUI parentUI;
    private bool isSelected;

    #region 初始化

    /// <summary>
    /// 初始化槽位
    /// </summary>
    public void Initialize(int index, SaveSlotInfo info, SaveLoadUI parent)
    {
        slotIndex = index;
        saveInfo = info;
        parentUI = parent;
        
        // 绑定点击事件
        if (clickButton != null)
        {
            clickButton.onClick.RemoveAllListeners();
            clickButton.onClick.AddListener(OnSlotClicked);
        }
        
        UpdateDisplay();
    }

    #endregion

    #region 显示更新

    /// <summary>
    /// 更新显示
    /// </summary>
    private void UpdateDisplay()
    {
        bool isEmpty = saveInfo == null;
        
        // 显示/隐藏空槽位指示器
        if (emptySlotIndicator != null)
            emptySlotIndicator.SetActive(isEmpty);
        
        if (isEmpty)
        {
            ShowEmptySlot();
        }
        else
        {
            ShowSaveInfo();
        }
        
        UpdateVisualState();
    }

    /// <summary>
    /// 显示空槽位
    /// </summary>
    private void ShowEmptySlot()
    {
        if (slotNameText != null)
            slotNameText.text = $"槽位 {slotIndex + 1}";
            
        if (saveTimeText != null)
            saveTimeText.text = "空";
            
        if (fileSizeText != null)
            fileSizeText.text = "";
            
        if (descriptionText != null)
            descriptionText.text = "点击创建新存档";
            
        if (previewImage != null)
            previewImage.gameObject.SetActive(false);
    }

    /// <summary>
    /// 显示存档信息
    /// </summary>
    private void ShowSaveInfo()
    {
        if (slotNameText != null)
            slotNameText.text = saveInfo.slotName;
            
        if (saveTimeText != null)
            saveTimeText.text = saveInfo.GetSaveTimeString();
            
        if (fileSizeText != null)
            fileSizeText.text = saveInfo.GetFileSizeString();
            
        if (descriptionText != null)
            descriptionText.text = string.IsNullOrEmpty(saveInfo.description) ? "无描述" : saveInfo.description;
            
        if (previewImage != null)
        {
            previewImage.gameObject.SetActive(true);
            // 这里可以加载存档预览图
            // LoadPreviewImage();
        }
    }

    /// <summary>
    /// 更新视觉状态
    /// </summary>
    private void UpdateVisualState()
    {
        Color targetColor;
        
        if (saveInfo == null)
        {
            targetColor = emptyColor;
        }
        else if (isSelected)
        {
            targetColor = selectedColor;
        }
        else
        {
            targetColor = normalColor;
        }
        
        if (backgroundImage != null)
            backgroundImage.color = targetColor;
            
        if (selectedIndicator != null)
            selectedIndicator.SetActive(isSelected);
    }

    #endregion

    #region 交互

    /// <summary>
    /// 槽位点击事件
    /// </summary>
    private void OnSlotClicked()
    {
        if (parentUI != null)
        {
            parentUI.SelectSaveSlot(this);
        }
    }

    /// <summary>
    /// 设置选中状态
    /// </summary>
    public void SetSelected(bool selected)
    {
        isSelected = selected;
        UpdateVisualState();
    }

    /// <summary>
    /// 设置存档信息
    /// </summary>
    public void SetSaveInfo(SaveSlotInfo info)
    {
        saveInfo = info;
        UpdateDisplay();
    }

    #endregion

    #region 动画效果

    /// <summary>
    /// 播放选中动画
    /// </summary>
    public void PlaySelectAnimation()
    {
        // 简单的缩放动画
        LeanTween.cancel(gameObject);
        LeanTween.scale(gameObject, Vector3.one * 1.05f, 0.1f)
            .setEase(LeanTweenType.easeOutBack)
            .setOnComplete(() => {
                LeanTween.scale(gameObject, Vector3.one, 0.1f)
                    .setEase(LeanTweenType.easeInBack);
            });
    }

    /// <summary>
    /// 播放悬停动画
    /// </summary>
    public void PlayHoverAnimation()
    {
        if (!isSelected)
        {
            LeanTween.cancel(gameObject);
            LeanTween.scale(gameObject, Vector3.one * 1.02f, 0.1f)
                .setEase(LeanTweenType.easeOutQuad);
        }
    }

    /// <summary>
    /// 播放离开悬停动画
    /// </summary>
    public void PlayExitHoverAnimation()
    {
        if (!isSelected)
        {
            LeanTween.cancel(gameObject);
            LeanTween.scale(gameObject, Vector3.one, 0.1f)
                .setEase(LeanTweenType.easeInQuad);
        }
    }

    #endregion

    #region Unity事件

    private void OnDestroy()
    {
        // 清理动画
        LeanTween.cancel(gameObject);
    }

    #endregion

    #region 预览图加载（可选功能）

    /// <summary>
    /// 加载预览图
    /// </summary>
    private void LoadPreviewImage()
    {
        // 这里可以实现存档预览图的加载逻辑
        // 例如：从存档文件中读取截图数据
        // 或者根据存档数据生成预览图
        
        if (previewImage != null && saveInfo != null)
        {
            // 示例：设置默认预览图
            // previewImage.sprite = defaultPreviewSprite;
        }
    }

    /// <summary>
    /// 生成预览图
    /// </summary>
    public static Texture2D GeneratePreviewImage(SaveData saveData)
    {
        // 这里可以根据存档数据生成预览图
        // 例如：显示游戏进度、角色状态等信息
        
        int width = 200;
        int height = 150;
        Texture2D preview = new Texture2D(width, height);
        
        // 简单的渐变背景
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                float t = (float)y / height;
                Color color = Color.Lerp(Color.blue, Color.cyan, t);
                preview.SetPixel(x, y, color);
            }
        }
        
        preview.Apply();
        return preview;
    }

    #endregion
}
