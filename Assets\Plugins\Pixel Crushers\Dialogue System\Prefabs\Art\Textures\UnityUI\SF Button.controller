%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Normal
  serializedVersion: 4
  m_AnimationType: 2
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings: []
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: 1
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_Events: []
--- !u!74 &7400002
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Highlighted
  serializedVersion: 4
  m_AnimationType: 2
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.r
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.0784313679
        outSlope: -.0784313679
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.g
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.215686262
        outSlope: -.215686262
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.b
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.a
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -50
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: Background
    classID: 224
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 4080383872
      attribute: 4215373228
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 2334886179
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 2526845255
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 304273561
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 2033536083
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: 0
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.r
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.0784313679
        outSlope: -.0784313679
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.g
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.215686262
        outSlope: -.215686262
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.b
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.a
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -50
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: Background
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_Events: []
--- !u!74 &7400004
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Pressed
  serializedVersion: 4
  m_AnimationType: 2
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: .247548997
        outSlope: .247548997
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.r
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: .247548997
        outSlope: .247548997
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.g
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: .247548997
        outSlope: .247548997
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.b
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.a
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -40
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: Background
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -10
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: Background/Label
    classID: 224
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 4080383872
      attribute: 2526845255
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 4215373228
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 2334886179
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 304273561
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 4080383872
      attribute: 2033536083
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 2187107191
      attribute: 2033536083
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: 0
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: .247548997
        outSlope: .247548997
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.r
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: .247548997
        outSlope: .247548997
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.g
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: .247548997
        outSlope: .247548997
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.b
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Color.a
    path: Background
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -40
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: Background
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -10
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: Background/Label
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_Events: []
--- !u!74 &7400006
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Disabled
  serializedVersion: 4
  m_AnimationType: 2
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings: []
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: 1
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_Events: []
--- !u!91 &9100000
AnimatorController:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: SF Button
  serializedVersion: 2
  m_AnimatorParameters:
  - m_Name: Normal
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  - m_Name: Highlighted
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  - m_Name: Pressed
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  - m_Name: Disabled
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  m_AnimatorLayers:
  - serializedVersion: 3
    m_Name: Base Layer
    m_StateMachine: {fileID: 110700000}
    m_Mask: {fileID: 0}
    m_BlendingMode: 0
    m_SyncedLayerIndex: -1
    m_StateMachineMotionSetIndex: 0
    m_DefaultWeight: 0
    m_IKPass: 0
    m_SyncedLayerAffectsTiming: 0
    m_Controller: {fileID: 9100000}
--- !u!1101 &110100000
Transition:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_SrcState: {fileID: 0}
  m_DstState: {fileID: 110200000}
  m_TransitionDuration: .100000001
  m_TransitionOffset: 0
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Normal
    m_EventTreshold: 0
    m_ExitTime: .899999976
  m_Atomic: 0
  m_Solo: 0
  m_Mute: 0
  m_CanTransitionToSelf: 0
--- !u!1101 &110159816
Transition:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_SrcState: {fileID: 0}
  m_DstState: {fileID: 110259816}
  m_TransitionDuration: .100000001
  m_TransitionOffset: 0
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Pressed
    m_EventTreshold: 0
    m_ExitTime: .899999976
  m_Atomic: 0
  m_Solo: 0
  m_Mute: 0
  m_CanTransitionToSelf: 0
--- !u!1101 &110169531
Transition:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_SrcState: {fileID: 0}
  m_DstState: {fileID: 110269531}
  m_TransitionDuration: .100000001
  m_TransitionOffset: 0
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Highlighted
    m_EventTreshold: 0
    m_ExitTime: .899999976
  m_Atomic: 0
  m_Solo: 0
  m_Mute: 0
  m_CanTransitionToSelf: 0
--- !u!1101 &110193284
Transition:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_SrcState: {fileID: 0}
  m_DstState: {fileID: 110293284}
  m_TransitionDuration: .100000001
  m_TransitionOffset: 0
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Disabled
    m_EventTreshold: 0
    m_ExitTime: .899999976
  m_Atomic: 0
  m_Solo: 0
  m_Mute: 0
  m_CanTransitionToSelf: 0
--- !u!1102 &110200000
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Normal
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 7400000}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 300, y: 0, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1102 &110230839
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Start
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 0}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 300, y: -60, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1102 &110259816
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Pressed
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 7400004}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 300, y: 120, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1102 &110269531
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Highlighted
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 7400002}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 300, y: 60, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1102 &110293284
State:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Disabled
  m_Speed: 1
  m_CycleOffset: 0
  m_Motions:
  - {fileID: 7400006}
  m_ParentStateMachine: {fileID: 110700000}
  m_Position: {x: 300, y: 180, z: 0}
  m_IKOnFeet: 0
  m_Mirror: 0
  m_Tag: 
--- !u!1107 &110700000
StateMachine:
  serializedVersion: 2
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Base Layer
  m_DefaultState: {fileID: 110230839}
  m_States:
  - {fileID: 110200000}
  - {fileID: 110269531}
  - {fileID: 110259816}
  - {fileID: 110293284}
  - {fileID: 110230839}
  m_ChildStateMachine: []
  m_ChildStateMachinePosition: []
  m_OrderedTransitions:
    data:
      first: {fileID: 0}
      second:
      - {fileID: 110100000}
      - {fileID: 110169531}
      - {fileID: 110159816}
      - {fileID: 110193284}
    data:
      first: {fileID: 110200000}
      second: []
    data:
      first: {fileID: 110269531}
      second: []
    data:
      first: {fileID: 110259816}
      second: []
    data:
      first: {fileID: 110293284}
      second: []
  m_MotionSetCount: 1
  m_AnyStatePosition: {x: 50, y: 20, z: 0}
  m_ParentStateMachinePosition: {x: 800, y: 20, z: 0}
