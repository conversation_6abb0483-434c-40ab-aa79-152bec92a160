using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 故事块 ScriptableObject
/// 包含故事内容、对话选项、解锁条件和奖励
/// </summary>
[CreateAssetMenu(fileName = "StoryBlock", menuName = "Midnight Broadcasting/Story Block")]
public class StoryBlockSO : ScriptableObject
{
    [Header("=== 基础信息 ===")]
    [Tooltip("故事块等级 (1-6)")]
    [Range(1, 6)]
    public int level = 1;
    
    [Tooltip("故事标题")]
    public string title;
    
    [Tooltip("故事内容")]
    [TextArea(5, 10)]
    public string content;

    [Header("=== 解锁条件 ===")]
    [Tooltip("需要的DTV等级")]
    [Range(0, 5)]
    public int requiredDTV = 1;
    
    [Tooltip("需要的关键词")]
    public List<KeywordDataSO> requiredKeywords = new List<KeywordDataSO>();
    
    [Tooltip("需要的前置故事块")]
    public List<StoryBlockSO> prerequisiteStories = new List<StoryBlockSO>();

    [Header("=== 对话选项 ===")]
    [Tooltip("对话选项")]
    public List<DialogueOptionSO> dialogueOptions = new List<DialogueOptionSO>();

    [Header("=== 奖励系统 ===")]
    [Tooltip("奖励关键词")]
    public KeywordDataSO[] rewardKeywords = new KeywordDataSO[0];
    
    [Tooltip("精力奖励")]
    public float staminaReward = 0f;
    
    [Tooltip("温度奖励")]
    public float warmthReward = 0f;
    
    [Tooltip("金钱奖励")]
    public int moneyReward = 0;

    [Header("=== 特殊设置 ===")]
    [Tooltip("是否为特殊后日谈 (Level 6)")]
    public bool isEpilogue = false;
    
    [Tooltip("特殊触发条件")]
    [TextArea(2, 4)]
    public string specialCondition;
    
    [Tooltip("是否为一次性故事")]
    public bool isOneTime = true;
    
    [Tooltip("是否已经触发过")]
    public bool hasBeenTriggered = false;

    #region 业务逻辑方法

    /// <summary>
    /// 检查是否可以解锁此故事块
    /// </summary>
    public bool CanUnlock(AudienceDataSO audience)
    {
        // 检查是否已经触发过（一次性故事）
        if (isOneTime && hasBeenTriggered)
        {
            return false;
        }

        // 检查DTV要求
        if (audience.currentDTV < requiredDTV)
        {
            return false;
        }

        // 检查必需关键词
        foreach (var requiredKeyword in requiredKeywords)
        {
            if (requiredKeyword == null) continue;
            
            if (!audience.HasKeyword(requiredKeyword.keyword))
            {
                return false;
            }
        }

        // 检查前置故事块
        foreach (var prerequisite in prerequisiteStories)
        {
            if (prerequisite == null) continue;
            
            int prereqLevel = prerequisite.level;
            if (prereqLevel >= 1 && prereqLevel <= 6)
            {
                if (!audience.unlockedStoryBlocks[prereqLevel - 1])
                {
                    return false;
                }
            }
        }

        // Level 6 特殊条件检查
        if (level == 6 && isEpilogue)
        {
            return CheckEpilogueConditions(audience);
        }

        return true;
    }

    /// <summary>
    /// 触发故事块
    /// </summary>
    public void TriggerStory(AudienceDataSO audience, CoreGameDataSO gameData)
    {
        if (!CanUnlock(audience))
        {
            Debug.LogWarning($"无法触发故事块：{title}");
            return;
        }

        // 标记为已触发
        if (isOneTime)
        {
            hasBeenTriggered = true;
        }

        // 应用奖励
        ApplyRewards(audience, gameData);

        Debug.Log($"触发故事块：{title} (Level {level})");
    }

    /// <summary>
    /// 应用奖励
    /// </summary>
    private void ApplyRewards(AudienceDataSO audience, CoreGameDataSO gameData)
    {
        // 奖励关键词
        foreach (var rewardKeyword in rewardKeywords)
        {
            if (rewardKeyword != null)
            {
                audience.CollectKeyword(rewardKeyword);
            }
        }

        // 数值奖励
        if (gameData != null)
        {
            if (staminaReward != 0f)
            {
                gameData.RestoreStamina(staminaReward);
            }
            
            if (warmthReward != 0f)
            {
                gameData.ModifyWarmth(warmthReward);
            }
            
            if (moneyReward != 0)
            {
                gameData.ModifyMoney(moneyReward);
            }
        }
    }

    /// <summary>
    /// 检查后日谈条件
    /// </summary>
    private bool CheckEpilogueConditions(AudienceDataSO audience)
    {
        // 检查是否完成了前5个故事块
        for (int i = 0; i < 5; i++)
        {
            if (!audience.unlockedStoryBlocks[i])
            {
                return false;
            }
        }

        // 可以在这里添加更多特殊条件
        // 例如：特定的关键词组合、时间条件等

        return true;
    }

    /// <summary>
    /// 获取缺失的解锁条件
    /// </summary>
    public List<string> GetMissingRequirements(AudienceDataSO audience)
    {
        List<string> missing = new List<string>();

        // 检查DTV
        if (audience.currentDTV < requiredDTV)
        {
            missing.Add($"需要DTV等级：{requiredDTV}（当前：{audience.currentDTV}）");
        }

        // 检查关键词
        foreach (var requiredKeyword in requiredKeywords)
        {
            if (requiredKeyword != null && !audience.HasKeyword(requiredKeyword.keyword))
            {
                missing.Add($"需要关键词：{requiredKeyword.keyword}");
            }
        }

        // 检查前置故事
        foreach (var prerequisite in prerequisiteStories)
        {
            if (prerequisite != null)
            {
                int prereqLevel = prerequisite.level;
                if (prereqLevel >= 1 && prereqLevel <= 6)
                {
                    if (!audience.unlockedStoryBlocks[prereqLevel - 1])
                    {
                        missing.Add($"需要完成前置故事：{prerequisite.title}");
                    }
                }
            }
        }

        return missing;
    }

    /// <summary>
    /// 获取故事预览信息
    /// </summary>
    public string GetPreviewInfo()
    {
        string info = $"故事块 Level {level}：{title}\n";
        info += $"需要DTV：{requiredDTV}\n";
        
        if (requiredKeywords.Count > 0)
        {
            var keywordNames = requiredKeywords.Where(k => k != null).Select(k => k.keyword);
            info += $"需要关键词：{string.Join(", ", keywordNames)}\n";
        }
        
        if (prerequisiteStories.Count > 0)
        {
            var storyTitles = prerequisiteStories.Where(s => s != null).Select(s => s.title);
            info += $"前置故事：{string.Join(", ", storyTitles)}\n";
        }
        
        return info;
    }

    /// <summary>
    /// 重置故事状态（用于测试）
    /// </summary>
    public void ResetStory()
    {
        hasBeenTriggered = false;
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保等级在合理范围内
        level = Mathf.Clamp(level, 1, 6);
        
        // 确保DTV要求在合理范围内
        requiredDTV = Mathf.Clamp(requiredDTV, 0, 5);
        
        // 确保标题不为空
        if (string.IsNullOrEmpty(title))
        {
            title = $"故事块 Level {level}";
        }
        
        // Level 6 自动设置为后日谈
        if (level == 6)
        {
            isEpilogue = true;
        }
        
        // 移除空引用
        requiredKeywords.RemoveAll(k => k == null);
        prerequisiteStories.RemoveAll(s => s == null);
        dialogueOptions.RemoveAll(d => d == null);
    }

    #endregion

    #region 调试方法

    [ContextMenu("显示解锁条件")]
    private void ShowUnlockRequirements()
    {
        Debug.Log(GetPreviewInfo());
    }

    [ContextMenu("重置故事状态")]
    private void ResetStoryStatus()
    {
        ResetStory();
        Debug.Log($"故事状态已重置：{title}");
    }

    #endregion
}
