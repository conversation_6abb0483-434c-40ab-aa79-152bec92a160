using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 核心游戏数据 ScriptableObject
/// 包含玩家状态、游戏进度、心绪内阁等核心数据
/// </summary>
[CreateAssetMenu(fileName = "CoreGameData", menuName = "Midnight Broadcasting/Core Game Data")]
public class CoreGameDataSO : ScriptableObject
{
    [Header("=== 核心数值系统 ===")]
    [Tooltip("精力值 (0-100)")]
    [Range(0f, 100f)]
    public float currentStamina = 100f;
    
    [Tooltip("当前温度值 (1-10)")]
    [Range(1f, 10f)]
    public float currentWarmth = 5f;
    
    [Tooltip("总粉丝数")]
    public int totalFans = 1000;
    
    [Tooltip("金钱")]
    public float money = 0f;

    [Header("=== 游戏进度 ===")]
    [Tooltip("当前夜晚数")]
    public int currentNight = 1;
    
    [Tooltip("当前游戏阶段")]
    public GamePhase currentPhase = GamePhase.Preparation;
    
    [Tooltip("最后游戏时间")]
    public string lastPlayTimeString = "";
    
    [Tooltip("解锁的功能")]
    public bool[] unlockedFeatures = new bool[10];

    [Header("=== 心绪内阁系统 ===")]
    [Tooltip("已解锁的回忆")]
    public List<MemoryDataSO> unlockedMemories = new List<MemoryDataSO>();
    
    [Tooltip("当前负面状态")]
    public List<string> activeNegativeStates = new List<string>();

    [Header("=== 配置参数 ===")]
    [Tooltip("精力惩罚阈值")]
    public float staminaPenaltyThreshold = 30f;
    
    [Tooltip("精力惩罚倍数")]
    public float staminaPenaltyMultiplier = 1.5f;

    // 运行时属性
    public DateTime LastPlayTime
    {
        get 
        { 
            if (DateTime.TryParse(lastPlayTimeString, out DateTime result))
                return result;
            return DateTime.Now;
        }
        set { lastPlayTimeString = value.ToString(); }
    }

    #region 业务逻辑方法

    /// <summary>
    /// 检查是否可以执行需要精力的操作
    /// </summary>
    public bool CanPerformAction(float staminaCost)
    {
        float actualCost = staminaCost;
        if (currentStamina < staminaPenaltyThreshold)
        {
            actualCost *= staminaPenaltyMultiplier;
        }
        return currentStamina >= actualCost;
    }

    /// <summary>
    /// 消耗精力
    /// </summary>
    public void ConsumeStamina(float amount)
    {
        float actualCost = amount;
        if (currentStamina < staminaPenaltyThreshold)
        {
            actualCost *= staminaPenaltyMultiplier;
        }
        
        currentStamina = Mathf.Max(0, currentStamina - actualCost);
        
        if (currentStamina < staminaPenaltyThreshold)
        {
            OnLowStaminaState();
        }
    }

    /// <summary>
    /// 恢复精力
    /// </summary>
    public void RestoreStamina(float amount)
    {
        currentStamina = Mathf.Min(100f, currentStamina + amount);
    }

    /// <summary>
    /// 修改温度值
    /// </summary>
    public void ModifyWarmth(float delta)
    {
        currentWarmth = Mathf.Clamp(currentWarmth + delta, 1f, 10f);
    }

    /// <summary>
    /// 增加粉丝数
    /// </summary>
    public void AddFans(int amount)
    {
        totalFans = Mathf.Max(1000, totalFans + amount);
    }

    /// <summary>
    /// 修改金钱
    /// </summary>
    public void ModifyMoney(float delta)
    {
        money = Mathf.Max(0, money + delta);
    }

    /// <summary>
    /// 进入下一个夜晚
    /// </summary>
    public void AdvanceToNextNight()
    {
        currentNight++;
        currentPhase = GamePhase.Preparation;
        LastPlayTime = DateTime.Now;
        
        // 重置每晚的状态
        currentWarmth = 5f;
    }

    /// <summary>
    /// 解锁功能
    /// </summary>
    public void UnlockFeature(int featureIndex)
    {
        if (featureIndex >= 0 && featureIndex < unlockedFeatures.Length)
        {
            unlockedFeatures[featureIndex] = true;
        }
    }

    /// <summary>
    /// 检查功能是否已解锁
    /// </summary>
    public bool IsFeatureUnlocked(int featureIndex)
    {
        if (featureIndex >= 0 && featureIndex < unlockedFeatures.Length)
        {
            return unlockedFeatures[featureIndex];
        }
        return false;
    }

    /// <summary>
    /// 添加负面状态
    /// </summary>
    public void AddNegativeState(string stateName)
    {
        if (!activeNegativeStates.Contains(stateName))
        {
            activeNegativeStates.Add(stateName);
        }
    }

    /// <summary>
    /// 移除负面状态
    /// </summary>
    public void RemoveNegativeState(string stateName)
    {
        activeNegativeStates.Remove(stateName);
    }

    /// <summary>
    /// 检查是否有特定负面状态
    /// </summary>
    public bool HasNegativeState(string stateName)
    {
        return activeNegativeStates.Contains(stateName);
    }

    /// <summary>
    /// 低精力状态处理
    /// </summary>
    private void OnLowStaminaState()
    {
        AddNegativeState("低精力状态");
        Debug.Log("警告：精力过低，所有操作消耗增加！");
    }

    /// <summary>
    /// 计算粉丝增长
    /// </summary>
    public int CalculateFanGrowth()
    {
        // 基础增长 = 50 * (1000 / 总粉丝数)
        float baseGrowth = 50f * (1000f / totalFans);
        
        // 粉丝基数增长 = 总粉丝数 * 0.02
        float fanBaseGrowth = totalFans * 0.02f;
        
        // 温度乘数 = (温度 / 5)²
        float warmthMultiplier = Mathf.Pow(currentWarmth / 5f, 2f);
        
        // 总增长
        int totalGrowth = Mathf.RoundToInt((baseGrowth + fanBaseGrowth) * warmthMultiplier);
        
        return totalGrowth;
    }

    /// <summary>
    /// 计算基础收入
    /// </summary>
    public float CalculateBaseIncome()
    {
        // 基础收入 = 总粉丝数 * 0.1 * (0.7 + 随机数 * 0.6)
        float randomMultiplier = 0.7f + UnityEngine.Random.value * 0.6f;
        return totalFans * 0.1f * randomMultiplier;
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保数值在合理范围内
        currentStamina = Mathf.Clamp(currentStamina, 0f, 100f);
        currentWarmth = Mathf.Clamp(currentWarmth, 1f, 10f);
        totalFans = Mathf.Max(1000, totalFans);
        money = Mathf.Max(0, money);
        currentNight = Mathf.Max(1, currentNight);
    }

    #endregion
}
