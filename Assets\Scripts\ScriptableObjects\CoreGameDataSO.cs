using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 核心游戏数据 ScriptableObject
/// 包含玩家状态、游戏进度、心绪内阁等核心数据
/// </summary>
[CreateAssetMenu(fileName = "CoreGameData", menuName = "Midnight Broadcasting/Core Game Data")]
public class CoreGameDataSO : ScriptableObject
{
    [Header("=== 核心数值系统 ===")]
    [Tooltip("精力值 (0-100)")]
    [Range(0f, 100f)]
    public float currentStamina = 100f;

    [Tooltip("当前温度值 (1-10)")]
    [Range(1f, 10f)]
    public float currentWarmth = 5f;

    [Tooltip("总粉丝数")]
    public int totalFans = 1000;

    [Tooltip("金钱")]
    public float money = 0f;

    [Header("=== 游戏进度 ===")]
    [Tooltip("当前夜晚数")]
    public int currentNight = 1;

    [Tooltip("当前游戏阶段")]
    public GamePhase currentPhase = GamePhase.Preparation;

    [Tooltip("最后游戏时间")]
    public string lastPlayTimeString = "";

    [Tooltip("解锁的功能")]
    public bool[] unlockedFeatures = new bool[10];

    [Header("=== 心绪内阁系统 ===")]
    [Tooltip("已解锁的回忆")]
    public List<MemoryDataSO> unlockedMemories = new List<MemoryDataSO>();

    [Tooltip("当前负面状态")]
    public List<string> activeNegativeStates = new List<string>();

    [Header("=== 配置参数 ===")]
    [Tooltip("精力惩罚阈值")]
    public float staminaPenaltyThreshold = 30f;

    [Tooltip("精力惩罚倍数")]
    public float staminaPenaltyMultiplier = 1.5f;

    // 运行时属性
    public DateTime LastPlayTime
    {
        get
        {
            if (DateTime.TryParse(lastPlayTimeString, out DateTime result))
                return result;
            return DateTime.Now;
        }
        set { lastPlayTimeString = value.ToString(); }
    }

    #region 业务逻辑方法

    /// <summary>
    /// 检查是否可以执行需要精力的操作
    /// </summary>
    public bool CanPerformAction(float staminaCost)
    {
        float actualCost = staminaCost;
        if (currentStamina < staminaPenaltyThreshold)
        {
            actualCost *= staminaPenaltyMultiplier;
        }
        return currentStamina >= actualCost;
    }

    #region 数据访问方法

    /// <summary>
    /// 检查是否可以执行需要精力的行动
    /// </summary>
    public bool CanPerformAction(float requiredStamina)
    {
        return currentStamina >= requiredStamina;
    }

    /// <summary>
    /// 获取精力百分比
    /// </summary>
    public float GetStaminaPercentage()
    {
        return currentStamina / 100f;
    }

    /// <summary>
    /// 获取温度状态描述
    /// </summary>
    public string GetWarmthStatusDescription()
    {
        if (currentWarmth >= 8f) return "温暖";
        if (currentWarmth >= 6f) return "舒适";
        if (currentWarmth >= 4f) return "适中";
        if (currentWarmth >= 2f) return "偏冷";
        return "寒冷";
    }

    /// <summary>
    /// 获取粉丝等级描述
    /// </summary>
    public string GetFanLevelDescription()
    {
        if (totalFans >= 100000) return "知名主播";
        if (totalFans >= 50000) return "人气主播";
        if (totalFans >= 10000) return "小有名气";
        if (totalFans >= 5000) return "新兴主播";
        return "初出茅庐";
    }

    #endregion

    /// <summary>
    /// 进入下一个夜晚
    /// </summary>
    public void AdvanceToNextNight()
    {
        currentNight++;
        currentPhase = GamePhase.Preparation;
        LastPlayTime = DateTime.Now;

        // 重置每晚的状态
        currentWarmth = 5f;
    }

    /// <summary>
    /// 解锁功能
    /// </summary>
    public void UnlockFeature(int featureIndex)
    {
        if (featureIndex >= 0 && featureIndex < unlockedFeatures.Length)
        {
            unlockedFeatures[featureIndex] = true;
        }
    }

    /// <summary>
    /// 检查功能是否已解锁
    /// </summary>
    public bool IsFeatureUnlocked(int featureIndex)
    {
        if (featureIndex >= 0 && featureIndex < unlockedFeatures.Length)
        {
            return unlockedFeatures[featureIndex];
        }
        return false;
    }

    /// <summary>
    /// 添加负面状态
    /// </summary>
    public void AddNegativeState(string stateName)
    {
        if (!activeNegativeStates.Contains(stateName))
        {
            activeNegativeStates.Add(stateName);
        }
    }

    /// <summary>
    /// 移除负面状态
    /// </summary>
    public void RemoveNegativeState(string stateName)
    {
        activeNegativeStates.Remove(stateName);
    }

    /// <summary>
    /// 检查是否有特定负面状态
    /// </summary>
    public bool HasNegativeState(string stateName)
    {
        return activeNegativeStates.Contains(stateName);
    }



    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保数值在合理范围内
        currentStamina = Mathf.Clamp(currentStamina, 0f, 100f);
        currentWarmth = Mathf.Clamp(currentWarmth, 1f, 10f);
        totalFans = Mathf.Max(1000, totalFans);
        money = Mathf.Max(0, money);
        currentNight = Mathf.Max(1, currentNight);
    }

    #endregion
}
