%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400000}
  - component: {fileID: 11433020}
  - component: {fileID: 3300000}
  - component: {fileID: 2300000}
  - component: {fileID: 6500000}
  - component: {fileID: 11400004}
  - component: {fileID: 11476800}
  - component: {fileID: 11400000}
  m_Layer: 0
  m_Name: Terminal
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 1
  m_IsActive: 1
--- !u!1 &175264
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 406160}
  m_Layer: 0
  m_Name: Terminal Camera Angle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 2.3348134, y: -0.006051123, z: -4.226097}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 406160}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!4 &406160
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 175264}
  m_LocalRotation: {x: 0.69822323, y: 0, z: 0, w: 0.7158801}
  m_LocalPosition: {x: 0.711, y: 0.837, z: 1.666}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400000}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 88.5692, y: 0, z: 0}
--- !u!23 &2300000
MeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_Materials:
  - {fileID: 2100000, guid: da530438de201db409b41164a5192f40, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &3300000
MeshFilter:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Mesh: {fileID: 4300000, guid: bb72364520946c44d99f048ac2993daa, type: 3}
--- !u!65 &6500000
BoxCollider:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.8480582, y: 1.6034693, z: 2.0121474}
  m_Center: {x: 0.00000011920929, y: -0.00000005960466, z: 1.0060737}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 205c6d4e7606f8d40b5d2763ea70a42d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  priority: 0
  ui: {fileID: 100038, guid: 1fd0b032134ab05499b23cce5db54723, type: 2}
--- !u!114 &11400004
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274b80ab5b659c646ba9338870d14625, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  overrideName: 
  overrideUseMessage: (spacebar to log in)
  maxUseDistance: 5
  events:
    onSelect:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
    onDeselect:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
    onUse:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
--- !u!114 &11433020
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b6dc48c641bb39742af066b7a0250f33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_note: This Usable's Dialogue System Trigger runs a conversation the Player's Selectors
    sends an OnUse message. Its Override Dialogue UI component runs the conversation
    through a custom dialogue UI.
--- !u!114 &11476800
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c593457cd8105e148906690e1707c592, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  trigger: 32
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags: []
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  setQuestState: 1
  questName: 
  questState: 0
  setQuestEntryState: 0
  questEntryNumber: 1
  questEntryState: 0
  luaCode: 
  sequence: 
  sequenceSpeaker: {fileID: 0}
  sequenceListener: {fileID: 0}
  waitOneFrameOnStartOrEnable: 1
  alertMessage: 
  textTable: {fileID: 0}
  alertDuration: 0
  sendMessages: []
  barkSource: 0
  barkConversation: 
  barkText: 
  barkTextSequence: 
  barker: {fileID: 0}
  barkTarget: {fileID: 0}
  barkOrder: 0
  allowBarksDuringConversations: 0
  skipBarkIfNoValidEntries: 0
  cacheBarkLines: 0
  conversation: Terminal
  conversationConversant: {fileID: 0}
  conversationActor: {fileID: 0}
  startConversationEntryID: -1
  exclusive: 0
  skipIfNoValidEntries: 0
  stopConversationOnTriggerExit: 0
  stopConversationIfTooFar: 0
  maxConversationDistance: 5
  monitorConversationDistanceFrequency: 1
  showCursorDuringConversation: 0
  pauseGameDuringConversation: 0
  setActiveActions: []
  setEnabledActions: []
  setAnimatorStateActions: []
  onExecute:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: PixelCrushers.GameObjectUnityEvent, Assembly-CSharp-firstpass, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  useConversationTitlePicker: 1
  useBarkTitlePicker: 1
  useQuestNamePicker: 1
  selectedDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
