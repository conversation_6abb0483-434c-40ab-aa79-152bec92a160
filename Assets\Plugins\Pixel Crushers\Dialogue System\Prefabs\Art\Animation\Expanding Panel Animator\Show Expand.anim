%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Show Expand
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0, y: 0, z: 1}
        inSlope: {x: 12, y: 1.1999999, z: 0}
        outSlope: {x: 12, y: 1.1999999, z: 0}
        tangentMode: 0
      - time: 0.083333336
        value: {x: 1, y: 0.1, z: 1}
        inSlope: {x: 6, y: 5.9999995, z: 0}
        outSlope: {x: 6, y: 5.9999995, z: 0}
        tangentMode: 0
      - time: 0.16666667
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 10.799999, z: 0}
        outSlope: {x: 0, y: 10.799999, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 6
        outSlope: 6
        tangentMode: 10
      - time: 0.16666667
        value: 1
        inSlope: 6
        outSlope: 6
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 3
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 1574349066
      script: {fileID: 0}
      classID: 225
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.16666667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 12
        outSlope: 12
        tangentMode: 10
      - time: 0.083333336
        value: 1
        inSlope: 6
        outSlope: 6
        tangentMode: 10
      - time: 0.16666667
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 1.1999999
        outSlope: 1.1999999
        tangentMode: 10
      - time: 0.083333336
        value: 0.1
        inSlope: 5.9999995
        outSlope: 5.9999995
        tangentMode: 10
      - time: 0.16666667
        value: 1
        inSlope: 10.799999
        outSlope: 10.799999
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 0.16666667
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 6
        outSlope: 6
        tangentMode: 10
      - time: 0.16666667
        value: 1
        inSlope: 6
        outSlope: 6
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
