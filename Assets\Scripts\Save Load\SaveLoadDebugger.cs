using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;

/// <summary>
/// 存档系统调试器
/// 提供存档系统的调试、测试和监控功能
/// </summary>
public class SaveLoadDebugger : MonoBehaviour
{
    [Header("=== 调试设置 ===")]
    [Tooltip("是否启用调试模式")]
    public bool enableDebugMode = true;
    
    [Tooltip("是否显示详细日志")]
    public bool showDetailedLogs = false;
    
    [Tooltip("是否监控文件变化")]
    public bool monitorFileChanges = true;

    [Header("=== 测试设置 ===")]
    [Tooltip("测试存档数量")]
    public int testSaveCount = 5;
    
    [Tooltip("是否自动运行测试")]
    public bool autoRunTests = false;
    
    [Tooltip("测试间隔（秒）")]
    public float testInterval = 10f;

    [Header("=== 性能监控 ===")]
    [Tooltip("是否监控性能")]
    public bool enablePerformanceMonitoring = true;
    
    [Tooltip("性能采样间隔（秒）")]
    public float performanceSampleInterval = 1f;

    // 调试数据
    private List<SaveLoadOperation> operationHistory = new List<SaveLoadOperation>();
    private Dictionary<string, float> performanceMetrics = new Dictionary<string, float>();
    private float lastPerformanceSample;
    private int totalOperations;
    private int successfulOperations;
    private int failedOperations;

    // 单例模式
    public static SaveLoadDebugger Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeDebugger();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        if (autoRunTests)
        {
            InvokeRepeating(nameof(RunAutomaticTests), testInterval, testInterval);
        }
    }

    private void Update()
    {
        if (enablePerformanceMonitoring && Time.time - lastPerformanceSample >= performanceSampleInterval)
        {
            SamplePerformanceMetrics();
            lastPerformanceSample = Time.time;
        }
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化调试器
    /// </summary>
    private void InitializeDebugger()
    {
        if (!enableDebugMode) return;

        // 订阅存档系统事件
        if (ScriptableObjectSaveManager.Instance != null)
        {
            // 这里可以订阅保存/加载事件
            Debug.Log("存档调试器已初始化");
        }

        // 初始化性能指标
        performanceMetrics["SaveTime"] = 0f;
        performanceMetrics["LoadTime"] = 0f;
        performanceMetrics["FileSize"] = 0f;
        performanceMetrics["MemoryUsage"] = 0f;
    }

    #endregion

    #region 操作记录

    /// <summary>
    /// 记录保存操作
    /// </summary>
    public void RecordSaveOperation(string fileName, bool success, float duration, long fileSize, string errorMessage = "")
    {
        if (!enableDebugMode) return;

        var operation = new SaveLoadOperation
        {
            operationType = SaveLoadOperationType.Save,
            fileName = fileName,
            success = success,
            duration = duration,
            fileSize = fileSize,
            errorMessage = errorMessage,
            timestamp = DateTime.Now
        };

        operationHistory.Add(operation);
        totalOperations++;
        
        if (success)
            successfulOperations++;
        else
            failedOperations++;

        if (showDetailedLogs)
        {
            Debug.Log($"保存操作记录：{fileName}, 成功:{success}, 耗时:{duration:F3}s, 大小:{fileSize}字节");
        }

        // 限制历史记录数量
        if (operationHistory.Count > 100)
        {
            operationHistory.RemoveAt(0);
        }
    }

    /// <summary>
    /// 记录加载操作
    /// </summary>
    public void RecordLoadOperation(string fileName, bool success, float duration, string errorMessage = "")
    {
        if (!enableDebugMode) return;

        var operation = new SaveLoadOperation
        {
            operationType = SaveLoadOperationType.Load,
            fileName = fileName,
            success = success,
            duration = duration,
            errorMessage = errorMessage,
            timestamp = DateTime.Now
        };

        operationHistory.Add(operation);
        totalOperations++;
        
        if (success)
            successfulOperations++;
        else
            failedOperations++;

        if (showDetailedLogs)
        {
            Debug.Log($"加载操作记录：{fileName}, 成功:{success}, 耗时:{duration:F3}s");
        }

        // 限制历史记录数量
        if (operationHistory.Count > 100)
        {
            operationHistory.RemoveAt(0);
        }
    }

    #endregion

    #region 性能监控

    /// <summary>
    /// 采样性能指标
    /// </summary>
    private void SamplePerformanceMetrics()
    {
        // 内存使用量
        long memoryUsage = GC.GetTotalMemory(false);
        performanceMetrics["MemoryUsage"] = memoryUsage / (1024f * 1024f); // MB

        // 计算平均保存时间
        var saveOps = operationHistory.Where(op => op.operationType == SaveLoadOperationType.Save && op.success).ToList();
        if (saveOps.Count > 0)
        {
            performanceMetrics["SaveTime"] = saveOps.Average(op => op.duration);
        }

        // 计算平均加载时间
        var loadOps = operationHistory.Where(op => op.operationType == SaveLoadOperationType.Load && op.success).ToList();
        if (loadOps.Count > 0)
        {
            performanceMetrics["LoadTime"] = loadOps.Average(op => op.duration);
        }

        // 计算平均文件大小
        var fileSizes = operationHistory.Where(op => op.operationType == SaveLoadOperationType.Save && op.success).Select(op => op.fileSize).ToList();
        if (fileSizes.Count > 0)
        {
            performanceMetrics["FileSize"] = fileSizes.Average() / 1024f; // KB
        }
    }

    /// <summary>
    /// 获取性能报告
    /// </summary>
    public PerformanceReport GetPerformanceReport()
    {
        return new PerformanceReport
        {
            totalOperations = totalOperations,
            successfulOperations = successfulOperations,
            failedOperations = failedOperations,
            successRate = totalOperations > 0 ? (float)successfulOperations / totalOperations : 0f,
            averageSaveTime = performanceMetrics.GetValueOrDefault("SaveTime", 0f),
            averageLoadTime = performanceMetrics.GetValueOrDefault("LoadTime", 0f),
            averageFileSize = performanceMetrics.GetValueOrDefault("FileSize", 0f),
            currentMemoryUsage = performanceMetrics.GetValueOrDefault("MemoryUsage", 0f),
            recentOperations = operationHistory.TakeLast(10).ToList()
        };
    }

    #endregion

    #region 自动测试

    /// <summary>
    /// 运行自动测试
    /// </summary>
    private void RunAutomaticTests()
    {
        if (!enableDebugMode) return;

        StartCoroutine(RunTestSuite());
    }

    /// <summary>
    /// 运行测试套件
    /// </summary>
    private System.Collections.IEnumerator RunTestSuite()
    {
        Debug.Log("开始运行存档系统测试套件");

        // 测试1：基本保存/加载
        yield return StartCoroutine(TestBasicSaveLoad());

        // 测试2：多槽位保存
        yield return StartCoroutine(TestMultiSlotSave());

        // 测试3：错误恢复
        yield return StartCoroutine(TestErrorRecovery());

        // 测试4：性能测试
        yield return StartCoroutine(TestPerformance());

        Debug.Log("存档系统测试套件完成");
    }

    /// <summary>
    /// 测试基本保存/加载
    /// </summary>
    private System.Collections.IEnumerator TestBasicSaveLoad()
    {
        Debug.Log("测试：基本保存/加载");

        var saveManager = ScriptableObjectSaveManager.Instance;
        if (saveManager == null)
        {
            Debug.LogError("SaveManager未找到");
            yield break;
        }

        // 保存测试
        string testSlot = "test_basic";
        bool saveResult = saveManager.SaveGame(testSlot);
        
        yield return new WaitForSeconds(0.1f);

        // 加载测试
        bool loadResult = saveManager.LoadGame(testSlot);

        Debug.Log($"基本保存/加载测试结果：保存{(saveResult ? "成功" : "失败")}，加载{(loadResult ? "成功" : "失败")}");

        // 清理测试文件
        saveManager.DeleteSave(testSlot);
    }

    /// <summary>
    /// 测试多槽位保存
    /// </summary>
    private System.Collections.IEnumerator TestMultiSlotSave()
    {
        Debug.Log("测试：多槽位保存");

        var saveManager = ScriptableObjectSaveManager.Instance;
        if (saveManager == null) yield break;

        List<string> testSlots = new List<string>();
        
        // 创建多个测试存档
        for (int i = 0; i < testSaveCount; i++)
        {
            string slotName = $"test_slot_{i}";
            testSlots.Add(slotName);
            
            bool result = saveManager.SaveGame(slotName);
            Debug.Log($"槽位 {slotName} 保存{(result ? "成功" : "失败")}");
            
            yield return new WaitForSeconds(0.05f);
        }

        // 验证所有存档
        var allSlots = saveManager.GetAllSaveSlots();
        int foundTestSlots = allSlots.Count(slot => slot.slotName.StartsWith("test_slot_"));
        
        Debug.Log($"多槽位测试结果：创建{testSaveCount}个，找到{foundTestSlots}个");

        // 清理测试文件
        foreach (string slot in testSlots)
        {
            saveManager.DeleteSave(slot);
        }
    }

    /// <summary>
    /// 测试错误恢复
    /// </summary>
    private System.Collections.IEnumerator TestErrorRecovery()
    {
        Debug.Log("测试：错误恢复");

        // 这里可以添加错误恢复的测试逻辑
        // 例如：创建损坏的存档文件，然后测试恢复功能

        yield return new WaitForSeconds(0.1f);
        Debug.Log("错误恢复测试完成");
    }

    /// <summary>
    /// 测试性能
    /// </summary>
    private System.Collections.IEnumerator TestPerformance()
    {
        Debug.Log("测试：性能测试");

        var saveManager = ScriptableObjectSaveManager.Instance;
        if (saveManager == null) yield break;

        float startTime = Time.realtimeSinceStartup;
        
        // 连续保存/加载测试
        for (int i = 0; i < 10; i++)
        {
            string slotName = $"perf_test_{i}";
            saveManager.SaveGame(slotName);
            yield return new WaitForEndOfFrame();
            saveManager.LoadGame(slotName);
            yield return new WaitForEndOfFrame();
            saveManager.DeleteSave(slotName);
        }

        float totalTime = Time.realtimeSinceStartup - startTime;
        Debug.Log($"性能测试完成：10次保存/加载/删除操作耗时 {totalTime:F3} 秒");
    }

    #endregion

    #region 调试界面

    /// <summary>
    /// 显示调试信息
    /// </summary>
    [ContextMenu("显示调试信息")]
    public void ShowDebugInfo()
    {
        if (!enableDebugMode) return;

        var report = GetPerformanceReport();
        
        Debug.Log("=== 存档系统调试信息 ===");
        Debug.Log($"总操作数：{report.totalOperations}");
        Debug.Log($"成功操作：{report.successfulOperations}");
        Debug.Log($"失败操作：{report.failedOperations}");
        Debug.Log($"成功率：{report.successRate:P2}");
        Debug.Log($"平均保存时间：{report.averageSaveTime:F3}秒");
        Debug.Log($"平均加载时间：{report.averageLoadTime:F3}秒");
        Debug.Log($"平均文件大小：{report.averageFileSize:F2}KB");
        Debug.Log($"当前内存使用：{report.currentMemoryUsage:F2}MB");
    }

    /// <summary>
    /// 清除调试数据
    /// </summary>
    [ContextMenu("清除调试数据")]
    public void ClearDebugData()
    {
        operationHistory.Clear();
        totalOperations = 0;
        successfulOperations = 0;
        failedOperations = 0;
        
        foreach (var key in performanceMetrics.Keys.ToList())
        {
            performanceMetrics[key] = 0f;
        }
        
        Debug.Log("调试数据已清除");
    }

    /// <summary>
    /// 运行完整测试
    /// </summary>
    [ContextMenu("运行完整测试")]
    public void RunFullTest()
    {
        if (Application.isPlaying)
        {
            StartCoroutine(RunTestSuite());
        }
    }

    #endregion
}
