%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Slide Right Hide
  serializedVersion: 4
  m_AnimationType: 2
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: .25
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 6400
        outSlope: 6400
        tangentMode: 10
      - time: .25
        value: 1600
        inSlope: 6400
        outSlope: 6400
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 1574349066
      script: {fileID: 0}
      classID: 225
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 1460864421
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: .25
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      - time: .25
        value: 0
        inSlope: -4
        outSlope: -4
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_Alpha
    path: 
    classID: 225
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 6400
        outSlope: 6400
        tangentMode: 10
      - time: .25
        value: 1600
        inSlope: 6400
        outSlope: 6400
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_Events: []
