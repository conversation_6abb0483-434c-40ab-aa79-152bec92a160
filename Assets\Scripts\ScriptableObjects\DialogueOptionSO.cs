using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 对话选项 ScriptableObject
/// 包含选项文本、类型、效果和条件
/// </summary>
[CreateAssetMenu(fileName = "DialogueOption", menuName = "Midnight Broadcasting/Dialogue Option")]
public class DialogueOptionSO : ScriptableObject
{
    [Header("=== 基础信息 ===")]
    [Tooltip("选项文本")]
    [TextArea(2, 4)]
    public string optionText;

    [Tooltip("选项类型")]
    public DialogueOptionType optionType = DialogueOptionType.Normal;

    [Tooltip("选项描述")]
    [TextArea(2, 3)]
    public string description;

    [Header("=== 显示条件 ===")]
    [Tooltip("需要的关键词")]
    public List<KeywordDataSO> requiredKeywords = new List<KeywordDataSO>();

    [Tooltip("需要的DTV等级")]
    [Range(0, 5)]
    public int requiredDTV = 0;

    [Tooltip("需要的精力值")]
    [Range(0f, 100f)]
    public float requiredStamina = 0f;

    [Header("=== 选择效果 ===")]
    [Tooltip("DTV变化")]
    [Range(-2, 2)]
    public int dtvChange = 0;

    [Tooltip("精力变化")]
    [Range(-50f, 50f)]
    public float staminaChange = 0f;

    [Tooltip("温度变化")]
    [Range(-3f, 3f)]
    public float warmthChange = 0f;

    [Tooltip("金钱变化")]
    public int moneyChange = 0;

    [Header("=== 关键词效果 ===")]
    [Tooltip("奖励关键词")]
    public List<KeywordDataSO> rewardKeywords = new List<KeywordDataSO>();

    [Tooltip("消耗的关键词")]
    public List<KeywordDataSO> consumedKeywords = new List<KeywordDataSO>();

    [Header("=== 成功率设置 ===")]
    [Tooltip("基础成功率")]
    [Range(0f, 1f)]
    public float baseSuccessRate = 1f;

    [Tooltip("关键词权重影响")]
    public bool useKeywordWeight = true;

    [Header("=== 后续效果 ===")]
    [Tooltip("触发的后续对话")]
    public DialogueOptionSO followUpOption;

    [Tooltip("解锁的故事块等级")]
    public int unlockStoryLevel = 0;

    #region 业务逻辑方法

    /// <summary>
    /// 检查选项是否可用
    /// </summary>
    public bool IsAvailable(AudienceDataSO audience, CoreGameDataSO gameData)
    {
        // 检查DTV要求
        if (audience.currentDTV < requiredDTV)
        {
            return false;
        }

        // 检查精力要求
        if (gameData != null && gameData.currentStamina < requiredStamina)
        {
            return false;
        }

        // 检查必需关键词
        foreach (var requiredKeyword in requiredKeywords)
        {
            if (requiredKeyword != null && !audience.HasKeyword(requiredKeyword.keyword))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 执行选项效果
    /// </summary>
    public bool ExecuteOption(AudienceDataSO audience, CoreGameDataSO gameData)
    {
        if (!IsAvailable(audience, gameData))
        {
            Debug.LogWarning($"选项不可用：{optionText}");
            return false;
        }

        // 计算成功率
        float successRate = CalculateSuccessRate(audience);
        bool success = Random.value <= successRate;

        // 记录互动
        audience.RecordInteraction(success);

        if (success)
        {
            ApplyEffects(audience, gameData);
            Debug.Log($"选项成功：{optionText}");
        }
        else
        {
            ApplyFailureEffects(audience, gameData);
            Debug.Log($"选项失败：{optionText}");
        }

        return success;
    }

    /// <summary>
    /// 计算成功率
    /// </summary>
    private float CalculateSuccessRate(AudienceDataSO audience)
    {
        float successRate = baseSuccessRate;

        if (useKeywordWeight)
        {
            // 根据关键词权重调整成功率
            float weightBonus = 0f;
            foreach (var keyword in requiredKeywords)
            {
                if (keyword != null && audience.HasKeyword(keyword.keyword))
                {
                    var audienceKeyword = audience.GetValidKeywords()
                        .FirstOrDefault(k => k.keyword == keyword.keyword);
                    if (audienceKeyword != null)
                    {
                        weightBonus += audienceKeyword.weight * 0.1f;
                    }
                }
            }
            successRate += weightBonus;
        }

        // 根据选项类型调整成功率
        switch (optionType)
        {
            case DialogueOptionType.Contradiction:
                successRate *= 0.7f; // 矛盾质询更难成功
                break;
            case DialogueOptionType.EmotionalResonance:
                successRate *= 1.2f; // 情感共鸣更容易成功
                break;
            case DialogueOptionType.MeaningfulSilence:
                successRate *= 0.9f; // 沉默留白需要技巧
                break;
        }

        return Mathf.Clamp01(successRate);
    }

    /// <summary>
    /// 应用成功效果
    /// </summary>
    private void ApplyEffects(AudienceDataSO audience, CoreGameDataSO gameData)
    {
        // 应用DTV变化
        if (dtvChange > 0)
        {
            audience.IncreaseDTV(dtvChange);
        }
        else if (dtvChange < 0)
        {
            audience.DecreaseDTV(-dtvChange);
        }

        // 应用数值变化
        if (gameData != null)
        {
            if (staminaChange > 0)
            {
                gameData.RestoreStamina(staminaChange);
            }
            else if (staminaChange < 0)
            {
                gameData.ConsumeStamina(-staminaChange);
            }

            if (warmthChange != 0f)
            {
                gameData.ModifyWarmth(warmthChange);
            }

            if (moneyChange != 0)
            {
                gameData.ModifyMoney(moneyChange);
            }
        }

        // 奖励关键词
        foreach (var rewardKeyword in rewardKeywords)
        {
            if (rewardKeyword != null)
            {
                audience.CollectKeyword(rewardKeyword);
            }
        }

        // 消耗关键词
        foreach (var consumedKeyword in consumedKeywords)
        {
            if (consumedKeyword != null)
            {
                var keywordToRemove = audience.collectedKeywords
                    .FirstOrDefault(k => k.keyword == consumedKeyword.keyword);
                if (keywordToRemove != null)
                {
                    audience.collectedKeywords.Remove(keywordToRemove);
                }
            }
        }

        // 解锁故事块
        if (unlockStoryLevel > 0)
        {
            audience.UnlockStoryBlock(unlockStoryLevel);
        }
    }

    /// <summary>
    /// 应用失败效果
    /// </summary>
    private void ApplyFailureEffects(AudienceDataSO audience, CoreGameDataSO gameData)
    {
        // 失败时的惩罚效果
        switch (optionType)
        {
            case DialogueOptionType.Contradiction:
                // 矛盾质询失败，DTV可能下降
                if (Random.value < 0.3f)
                {
                    audience.DecreaseDTV(1);
                }
                break;

            case DialogueOptionType.KeywordResponse:
                // 关键词回应失败，消耗精力
                if (gameData != null)
                {
                    gameData.ConsumeStamina(5f);
                }
                break;
        }
    }

    /// <summary>
    /// 获取选项预览信息
    /// </summary>
    public string GetPreviewInfo()
    {
        string info = $"选项：{optionText}\n";
        info += $"类型：{optionType}\n";

        if (requiredDTV > 0)
        {
            info += $"需要DTV：{requiredDTV}\n";
        }

        if (requiredStamina > 0)
        {
            info += $"需要精力：{requiredStamina}\n";
        }

        if (requiredKeywords.Count > 0)
        {
            var keywordNames = requiredKeywords.Where(k => k != null).Select(k => k.keyword);
            info += $"需要关键词：{string.Join(", ", keywordNames)}\n";
        }

        return info;
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 确保选项文本不为空
        if (string.IsNullOrEmpty(optionText))
        {
            optionText = name;
        }

        // 确保数值在合理范围内
        requiredDTV = Mathf.Clamp(requiredDTV, 0, 5);
        requiredStamina = Mathf.Clamp(requiredStamina, 0f, 100f);
        dtvChange = Mathf.Clamp(dtvChange, -2, 2);
        staminaChange = Mathf.Clamp(staminaChange, -50f, 50f);
        warmthChange = Mathf.Clamp(warmthChange, -3f, 3f);
        baseSuccessRate = Mathf.Clamp01(baseSuccessRate);
        unlockStoryLevel = Mathf.Clamp(unlockStoryLevel, 0, 6);

        // 移除空引用
        requiredKeywords.RemoveAll(k => k == null);
        rewardKeywords.RemoveAll(k => k == null);
        consumedKeywords.RemoveAll(k => k == null);
    }

    #endregion
}
