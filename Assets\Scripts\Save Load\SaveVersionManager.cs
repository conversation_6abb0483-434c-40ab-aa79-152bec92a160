using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 存档版本管理器
/// 处理存档版本兼容性、数据迁移和错误恢复
/// </summary>
public class SaveVersionManager : MonoBehaviour
{
    [Header("=== 版本信息 ===")]
    [Tooltip("当前游戏版本")]
    public string currentGameVersion = "1.0.0";

    [Tooltip("支持的最低存档版本")]
    public string minimumSupportedVersion = "1.0.0";

    [Header("=== 迁移设置 ===")]
    [Tooltip("是否启用自动迁移")]
    public bool enableAutoMigration = true;

    [Tooltip("迁移前是否创建备份")]
    public bool backupBeforeMigration = true;

    [Tooltip("是否显示迁移进度")]
    public bool showMigrationProgress = true;

    [Header("=== 错误恢复 ===")]
    [Tooltip("是否启用错误恢复")]
    public bool enableErrorRecovery = true;

    [Tooltip("最大恢复尝试次数")]
    public int maxRecoveryAttempts = 3;

    // 版本迁移规则
    private Dictionary<string, Func<SaveData, SaveData>> migrationRules;

    // 单例模式
    public static SaveVersionManager Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeMigrationRules();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化迁移规则
    /// </summary>
    private void InitializeMigrationRules()
    {
        migrationRules = new Dictionary<string, Func<SaveData, SaveData>>();

        // 添加版本迁移规则
        // 例如：从1.0.0迁移到1.1.0
        migrationRules["1.0.0->1.1.0"] = MigrateFrom100To110;

        Debug.Log("存档版本管理器初始化完成");
    }

    #endregion

    #region 版本检查

    /// <summary>
    /// 检查存档版本兼容性
    /// </summary>
    public SaveVersionCheckResult CheckSaveVersion(string saveVersion)
    {
        var result = new SaveVersionCheckResult();
        result.saveVersion = saveVersion;
        result.currentVersion = currentGameVersion;

        if (string.IsNullOrEmpty(saveVersion))
        {
            result.isCompatible = false;
            result.needsMigration = false;
            result.errorMessage = "存档版本信息缺失";
            return result;
        }

        // 检查版本格式
        if (!IsValidVersionFormat(saveVersion))
        {
            result.isCompatible = false;
            result.needsMigration = false;
            result.errorMessage = "存档版本格式无效";
            return result;
        }

        // 检查是否为当前版本
        if (saveVersion == currentGameVersion)
        {
            result.isCompatible = true;
            result.needsMigration = false;
            return result;
        }

        // 检查是否低于最低支持版本
        if (CompareVersions(saveVersion, minimumSupportedVersion) < 0)
        {
            result.isCompatible = false;
            result.needsMigration = false;
            result.errorMessage = $"存档版本过低，最低支持版本：{minimumSupportedVersion}";
            return result;
        }

        // 检查是否需要迁移
        if (CompareVersions(saveVersion, currentGameVersion) < 0)
        {
            result.isCompatible = true;
            result.needsMigration = true;
            result.migrationPath = GetMigrationPath(saveVersion, currentGameVersion);
            return result;
        }

        // 版本过高
        result.isCompatible = false;
        result.needsMigration = false;
        result.errorMessage = "存档版本过高，请更新游戏";
        return result;
    }

    /// <summary>
    /// 比较版本号
    /// </summary>
    private int CompareVersions(string version1, string version2)
    {
        var v1Parts = version1.Split('.');
        var v2Parts = version2.Split('.');

        int maxLength = Mathf.Max(v1Parts.Length, v2Parts.Length);

        for (int i = 0; i < maxLength; i++)
        {
            int v1Part = i < v1Parts.Length ? int.Parse(v1Parts[i]) : 0;
            int v2Part = i < v2Parts.Length ? int.Parse(v2Parts[i]) : 0;

            if (v1Part < v2Part) return -1;
            if (v1Part > v2Part) return 1;
        }

        return 0;
    }

    /// <summary>
    /// 检查版本格式是否有效
    /// </summary>
    private bool IsValidVersionFormat(string version)
    {
        if (string.IsNullOrEmpty(version)) return false;

        var parts = version.Split('.');
        if (parts.Length != 3) return false;

        foreach (var part in parts)
        {
            if (!int.TryParse(part, out _)) return false;
        }

        return true;
    }

    #endregion

    #region 数据迁移

    /// <summary>
    /// 迁移存档数据
    /// </summary>
    public SaveMigrationResult MigrateSaveData(SaveData saveData, string fromVersion, string toVersion)
    {
        var result = new SaveMigrationResult();
        result.originalVersion = fromVersion;
        result.targetVersion = toVersion;
        result.originalData = saveData;

        try
        {
            if (!enableAutoMigration)
            {
                result.success = false;
                result.errorMessage = "自动迁移已禁用";
                return result;
            }

            // 创建备份
            if (backupBeforeMigration)
            {
                result.backupCreated = CreateMigrationBackup(saveData, fromVersion);
            }

            // 获取迁移路径
            var migrationPath = GetMigrationPath(fromVersion, toVersion);
            if (migrationPath.Count == 0)
            {
                result.success = false;
                result.errorMessage = $"无法找到从 {fromVersion} 到 {toVersion} 的迁移路径";
                return result;
            }

            // 执行迁移
            SaveData migratedData = saveData;
            foreach (var step in migrationPath)
            {
                if (migrationRules.ContainsKey(step))
                {
                    migratedData = migrationRules[step](migratedData);
                    result.migrationSteps.Add(step);
                }
                else
                {
                    result.success = false;
                    result.errorMessage = $"缺少迁移规则：{step}";
                    return result;
                }
            }

            result.migratedData = migratedData;
            result.success = true;

            Debug.Log($"存档迁移成功：{fromVersion} -> {toVersion}");
        }
        catch (Exception e)
        {
            result.success = false;
            result.errorMessage = $"迁移过程中发生错误：{e.Message}";
            Debug.LogError($"存档迁移失败：{e.Message}");
        }

        return result;
    }

    /// <summary>
    /// 获取迁移路径
    /// </summary>
    private List<string> GetMigrationPath(string fromVersion, string toVersion)
    {
        List<string> path = new List<string>();

        // 简单的线性迁移路径
        // 在实际项目中，可能需要更复杂的路径查找算法
        string currentVersion = fromVersion;

        while (currentVersion != toVersion)
        {
            string nextVersion = GetNextVersion(currentVersion);
            if (nextVersion == null) break;

            string migrationKey = $"{currentVersion}->{nextVersion}";
            if (migrationRules.ContainsKey(migrationKey))
            {
                path.Add(migrationKey);
                currentVersion = nextVersion;
            }
            else
            {
                break;
            }
        }

        return path;
    }

    /// <summary>
    /// 获取下一个版本
    /// </summary>
    private string GetNextVersion(string currentVersion)
    {
        // 这里应该根据实际的版本发布历史来实现
        // 简化实现：假设只有1.0.0 -> 1.1.0的升级路径
        switch (currentVersion)
        {
            case "1.0.0":
                return "1.1.0";
            default:
                return null;
        }
    }

    /// <summary>
    /// 创建迁移备份
    /// </summary>
    private bool CreateMigrationBackup(SaveData saveData, string version)
    {
        try
        {
            string backupName = $"migration_backup_{version}_{DateTime.Now:yyyyMMdd_HHmmss}";

            // 使用ES3保存备份
            ES3Settings settings = new ES3Settings();
            settings.encrypt = true;
            settings.compressionType = ES3.CompressionType.Gzip;

            string fileName = $"MidnightBroadcasting_{backupName}.es3";
            ES3.Save("saveData", saveData, fileName, settings);
            ES3.Save("backupInfo", new BackupInfo
            {
                originalVersion = version,
                backupTime = DateTime.Now.ToString(),
                backupReason = "Migration"
            }, fileName, settings);

            Debug.Log($"迁移备份已创建：{fileName}");
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"创建迁移备份失败：{e.Message}");
            return false;
        }
    }

    #endregion

    #region 具体迁移规则

    /// <summary>
    /// 从1.0.0迁移到1.1.0
    /// </summary>
    private SaveData MigrateFrom100To110(SaveData saveData)
    {
        // 示例迁移：添加新字段、修改数据结构等
        Debug.Log("执行1.0.0到1.1.0的迁移");

        // 在这里添加具体的迁移逻辑
        // 例如：
        // - 添加新的数据字段
        // - 转换旧的数据格式
        // - 修复数据不一致问题

        return saveData;
    }

    #endregion

    #region 错误恢复

    /// <summary>
    /// 尝试恢复损坏的存档
    /// </summary>
    public SaveRecoveryResult TryRecoverSave(string fileName)
    {
        var result = new SaveRecoveryResult();
        result.fileName = fileName;

        if (!enableErrorRecovery)
        {
            result.success = false;
            result.errorMessage = "错误恢复已禁用";
            return result;
        }

        for (int attempt = 1; attempt <= maxRecoveryAttempts; attempt++)
        {
            try
            {
                result.attempts = attempt;

                // 尝试不同的恢复策略
                switch (attempt)
                {
                    case 1:
                        // 尝试忽略加密
                        result = TryRecoverWithoutEncryption(fileName);
                        break;
                    case 2:
                        // 尝试忽略压缩
                        result = TryRecoverWithoutCompression(fileName);
                        break;
                    case 3:
                        // 尝试部分数据恢复
                        result = TryPartialRecovery(fileName);
                        break;
                }

                if (result.success) break;
            }
            catch (Exception e)
            {
                result.errorMessage = e.Message;
                Debug.LogWarning($"恢复尝试 {attempt} 失败：{e.Message}");
            }
        }

        return result;
    }

    /// <summary>
    /// 尝试不使用加密恢复
    /// </summary>
    private SaveRecoveryResult TryRecoverWithoutEncryption(string fileName)
    {
        var result = new SaveRecoveryResult();
        result.fileName = fileName;
        result.recoveryMethod = "无加密恢复";

        try
        {
            ES3Settings settings = new ES3Settings();
            settings.encrypt = false;
            settings.compressionType = ES3.CompressionType.Gzip;

            SaveData saveData = ES3.Load<SaveData>("saveData", fileName, settings);
            result.recoveredData = saveData;
            result.success = true;

            Debug.Log("使用无加密方式恢复成功");
        }
        catch (Exception e)
        {
            result.success = false;
            result.errorMessage = e.Message;
        }

        return result;
    }

    /// <summary>
    /// 尝试不使用压缩恢复
    /// </summary>
    private SaveRecoveryResult TryRecoverWithoutCompression(string fileName)
    {
        var result = new SaveRecoveryResult();
        result.fileName = fileName;
        result.recoveryMethod = "无压缩恢复";

        try
        {
            ES3Settings settings = new ES3Settings();
            settings.encrypt = true;
            settings.compressionType = ES3.CompressionType.None;

            SaveData saveData = ES3.Load<SaveData>("saveData", fileName, settings);
            result.recoveredData = saveData;
            result.success = true;

            Debug.Log("使用无压缩方式恢复成功");
        }
        catch (Exception e)
        {
            result.success = false;
            result.errorMessage = e.Message;
        }

        return result;
    }

    /// <summary>
    /// 尝试部分数据恢复
    /// </summary>
    private SaveRecoveryResult TryPartialRecovery(string fileName)
    {
        var result = new SaveRecoveryResult();
        result.fileName = fileName;
        result.recoveryMethod = "部分数据恢复";

        try
        {
            ES3Settings settings = new ES3Settings();
            SaveData partialData = new SaveData();

            // 尝试逐个恢复数据块
            if (ES3.KeyExists("coreGameData", fileName))
            {
                partialData.coreGameData = ES3.Load<SerializableCoreGameData>("coreGameData", fileName, settings);
            }

            if (ES3.KeyExists("audienceDataList", fileName))
            {
                partialData.audienceDataList = ES3.Load<List<SerializableAudienceData>>("audienceDataList", fileName, settings);
            }

            if (ES3.KeyExists("memoryDataList", fileName))
            {
                partialData.memoryDataList = ES3.Load<List<SerializableMemoryData>>("memoryDataList", fileName, settings);
            }

            result.recoveredData = partialData;
            result.success = true;
            result.isPartialRecovery = true;

            Debug.Log("部分数据恢复成功");
        }
        catch (Exception e)
        {
            result.success = false;
            result.errorMessage = e.Message;
        }

        return result;
    }

    #endregion
}
