using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 存档界面UI管理器
/// 提供用户友好的存档/加载界面
/// </summary>
public class SaveLoadUI : MonoBehaviour
{
    [Header("=== UI引用 ===")]
    [Tooltip("存档槽位容器")]
    public Transform saveSlotContainer;
    
    [Toolt<PERSON>("存档槽位预制体")]
    public GameObject saveSlotPrefab;
    
    [Tooltip("保存按钮")]
    public Button saveButton;
    
    [Tooltip("加载按钮")]
    public Button loadButton;
    
    [Tooltip("删除按钮")]
    public Button deleteButton;
    
    [Tooltip("新建存档按钮")]
    public Button newSaveButton;

    [Header("=== 信息显示 ===")]
    [Tooltip("存档信息文本")]
    public TextMeshProUGUI saveInfoText;
    
    [Tooltip("状态信息文本")]
    public TextMeshProUGUI statusText;
    
    [Tooltip("存档预览图")]
    public Image savePreviewImage;

    [Header("=== 输入框 ===")]
    [Tooltip("存档名称输入框")]
    public TMP_InputField saveNameInput;

    [Header("=== 确认对话框 ===")]
    [Tooltip("确认对话框")]
    public GameObject confirmDialog;
    
    [Tooltip("确认对话框文本")]
    public TextMeshProUGUI confirmText;
    
    [Tooltip("确认按钮")]
    public Button confirmButton;
    
    [Tooltip("取消按钮")]
    public Button cancelButton;

    // 私有字段
    private List<SaveSlotUI> saveSlots = new List<SaveSlotUI>();
    private SaveSlotUI selectedSlot;
    private ScriptableObjectSaveManager saveManager;
    private System.Action pendingAction;

    #region Unity生命周期

    private void Start()
    {
        InitializeUI();
        RefreshSaveSlots();
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        saveManager = ScriptableObjectSaveManager.Instance;
        
        // 绑定按钮事件
        if (saveButton != null)
            saveButton.onClick.AddListener(OnSaveButtonClicked);
            
        if (loadButton != null)
            loadButton.onClick.AddListener(OnLoadButtonClicked);
            
        if (deleteButton != null)
            deleteButton.onClick.AddListener(OnDeleteButtonClicked);
            
        if (newSaveButton != null)
            newSaveButton.onClick.AddListener(OnNewSaveButtonClicked);

        // 绑定确认对话框事件
        if (confirmButton != null)
            confirmButton.onClick.AddListener(OnConfirmButtonClicked);
            
        if (cancelButton != null)
            cancelButton.onClick.AddListener(OnCancelButtonClicked);

        // 初始状态
        UpdateButtonStates();
        HideConfirmDialog();
        
        if (statusText != null)
            statusText.text = "请选择存档槽位";
    }

    #endregion

    #region 存档槽位管理

    /// <summary>
    /// 刷新存档槽位
    /// </summary>
    public void RefreshSaveSlots()
    {
        // 清除现有槽位
        foreach (var slot in saveSlots)
        {
            if (slot != null && slot.gameObject != null)
                Destroy(slot.gameObject);
        }
        saveSlots.Clear();

        if (saveManager == null) return;

        // 获取所有存档
        var allSaves = saveManager.GetAllSaveSlots();
        
        // 添加空槽位（用于新建存档）
        for (int i = 0; i < 6; i++) // 6个存档槽位
        {
            CreateSaveSlot(i, null);
        }

        // 填充现有存档
        foreach (var saveInfo in allSaves)
        {
            var slot = saveSlots.Find(s => s.slotIndex < allSaves.Count);
            if (slot != null)
            {
                slot.SetSaveInfo(saveInfo);
            }
        }
    }

    /// <summary>
    /// 创建存档槽位
    /// </summary>
    private void CreateSaveSlot(int index, SaveSlotInfo saveInfo)
    {
        if (saveSlotPrefab == null || saveSlotContainer == null) return;

        GameObject slotObj = Instantiate(saveSlotPrefab, saveSlotContainer);
        SaveSlotUI slotUI = slotObj.GetComponent<SaveSlotUI>();
        
        if (slotUI == null)
            slotUI = slotObj.AddComponent<SaveSlotUI>();

        slotUI.Initialize(index, saveInfo, this);
        saveSlots.Add(slotUI);
    }

    /// <summary>
    /// 选择存档槽位
    /// </summary>
    public void SelectSaveSlot(SaveSlotUI slot)
    {
        // 取消之前的选择
        if (selectedSlot != null)
            selectedSlot.SetSelected(false);

        // 选择新槽位
        selectedSlot = slot;
        if (selectedSlot != null)
        {
            selectedSlot.SetSelected(true);
            UpdateSaveInfo();
        }

        UpdateButtonStates();
    }

    /// <summary>
    /// 更新存档信息显示
    /// </summary>
    private void UpdateSaveInfo()
    {
        if (selectedSlot == null || saveInfoText == null) return;

        var saveInfo = selectedSlot.saveInfo;
        if (saveInfo != null)
        {
            string info = $"存档名称：{saveInfo.slotName}\n";
            info += $"保存时间：{saveInfo.GetSaveTimeString()}\n";
            info += $"文件大小：{saveInfo.GetFileSizeString()}\n";
            info += $"描述：{saveInfo.description}";
            
            saveInfoText.text = info;
        }
        else
        {
            saveInfoText.text = "空存档槽位";
        }
    }

    /// <summary>
    /// 更新按钮状态
    /// </summary>
    private void UpdateButtonStates()
    {
        bool hasSelection = selectedSlot != null;
        bool hasValidSave = hasSelection && selectedSlot.saveInfo != null;

        if (loadButton != null)
            loadButton.interactable = hasValidSave;
            
        if (deleteButton != null)
            deleteButton.interactable = hasValidSave;
            
        if (saveButton != null)
            saveButton.interactable = hasSelection;
    }

    #endregion

    #region 按钮事件

    /// <summary>
    /// 保存按钮点击
    /// </summary>
    private void OnSaveButtonClicked()
    {
        if (selectedSlot == null) return;

        string slotName = selectedSlot.saveInfo?.slotName ?? $"存档_{selectedSlot.slotIndex + 1}";
        
        if (saveNameInput != null && !string.IsNullOrEmpty(saveNameInput.text))
        {
            slotName = saveNameInput.text;
        }

        // 如果槽位已有存档，显示确认对话框
        if (selectedSlot.saveInfo != null)
        {
            ShowConfirmDialog($"确定要覆盖存档 '{slotName}' 吗？", () => PerformSave(slotName));
        }
        else
        {
            PerformSave(slotName);
        }
    }

    /// <summary>
    /// 加载按钮点击
    /// </summary>
    private void OnLoadButtonClicked()
    {
        if (selectedSlot?.saveInfo == null) return;

        ShowConfirmDialog($"确定要加载存档 '{selectedSlot.saveInfo.slotName}' 吗？当前进度将会丢失。", 
            () => PerformLoad(selectedSlot.saveInfo.slotName));
    }

    /// <summary>
    /// 删除按钮点击
    /// </summary>
    private void OnDeleteButtonClicked()
    {
        if (selectedSlot?.saveInfo == null) return;

        ShowConfirmDialog($"确定要删除存档 '{selectedSlot.saveInfo.slotName}' 吗？此操作无法撤销。", 
            () => PerformDelete(selectedSlot.saveInfo.slotName));
    }

    /// <summary>
    /// 新建存档按钮点击
    /// </summary>
    private void OnNewSaveButtonClicked()
    {
        // 找到第一个空槽位
        var emptySlot = saveSlots.Find(s => s.saveInfo == null);
        if (emptySlot != null)
        {
            SelectSaveSlot(emptySlot);
            
            if (saveNameInput != null)
            {
                saveNameInput.text = $"新存档_{System.DateTime.Now:MMdd_HHmm}";
                saveNameInput.Select();
            }
        }
        else
        {
            ShowStatus("没有可用的空槽位", true);
        }
    }

    #endregion

    #region 确认对话框

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    private void ShowConfirmDialog(string message, System.Action action)
    {
        if (confirmDialog == null) return;

        pendingAction = action;
        
        if (confirmText != null)
            confirmText.text = message;
            
        confirmDialog.SetActive(true);
    }

    /// <summary>
    /// 隐藏确认对话框
    /// </summary>
    private void HideConfirmDialog()
    {
        if (confirmDialog != null)
            confirmDialog.SetActive(false);
            
        pendingAction = null;
    }

    /// <summary>
    /// 确认按钮点击
    /// </summary>
    private void OnConfirmButtonClicked()
    {
        pendingAction?.Invoke();
        HideConfirmDialog();
    }

    /// <summary>
    /// 取消按钮点击
    /// </summary>
    private void OnCancelButtonClicked()
    {
        HideConfirmDialog();
    }

    #endregion

    #region 存档操作

    /// <summary>
    /// 执行保存
    /// </summary>
    private void PerformSave(string slotName)
    {
        if (saveManager == null) return;

        ShowStatus("正在保存...", false);
        
        bool success = saveManager.SaveGame(slotName);
        
        if (success)
        {
            ShowStatus($"存档 '{slotName}' 保存成功", false);
            RefreshSaveSlots();
        }
        else
        {
            ShowStatus("保存失败", true);
        }
    }

    /// <summary>
    /// 执行加载
    /// </summary>
    private void PerformLoad(string slotName)
    {
        if (saveManager == null) return;

        ShowStatus("正在加载...", false);
        
        bool success = saveManager.LoadGame(slotName);
        
        if (success)
        {
            ShowStatus($"存档 '{slotName}' 加载成功", false);
        }
        else
        {
            ShowStatus("加载失败", true);
        }
    }

    /// <summary>
    /// 执行删除
    /// </summary>
    private void PerformDelete(string slotName)
    {
        if (saveManager == null) return;

        bool success = saveManager.DeleteSave(slotName);
        
        if (success)
        {
            ShowStatus($"存档 '{slotName}' 删除成功", false);
            RefreshSaveSlots();
            selectedSlot = null;
            UpdateButtonStates();
            
            if (saveInfoText != null)
                saveInfoText.text = "";
        }
        else
        {
            ShowStatus("删除失败", true);
        }
    }

    #endregion

    #region 状态显示

    /// <summary>
    /// 显示状态信息
    /// </summary>
    private void ShowStatus(string message, bool isError)
    {
        if (statusText == null) return;

        statusText.text = message;
        statusText.color = isError ? Color.red : Color.white;
        
        // 3秒后清除状态
        Invoke(nameof(ClearStatus), 3f);
    }

    /// <summary>
    /// 清除状态信息
    /// </summary>
    private void ClearStatus()
    {
        if (statusText != null)
        {
            statusText.text = "";
            statusText.color = Color.white;
        }
    }

    #endregion

    #region 公共接口

    /// <summary>
    /// 打开存档界面
    /// </summary>
    public void OpenSaveLoadUI()
    {
        gameObject.SetActive(true);
        RefreshSaveSlots();
    }

    /// <summary>
    /// 关闭存档界面
    /// </summary>
    public void CloseSaveLoadUI()
    {
        gameObject.SetActive(false);
        HideConfirmDialog();
    }

    #endregion
}
