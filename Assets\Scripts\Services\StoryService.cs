using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 故事服务
/// 负责处理故事块和对话选项的业务逻辑
/// </summary>
public class StoryService : MonoBehaviour
{
    [Header("=== 故事数据 ===")]
    [Tooltip("所有故事块模板")]
    public List<StoryBlockSO> storyBlockTemplates = new List<StoryBlockSO>();
    
    [Tooltip("所有对话选项模板")]
    public List<DialogueOptionSO> dialogueOptionTemplates = new List<DialogueOptionSO>();

    // 单例模式
    public static StoryService Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    #endregion

    #region 故事块管理

    /// <summary>
    /// 检查故事块是否可以解锁
    /// </summary>
    public bool CanUnlockStoryBlock(StoryBlockSO storyBlock, AudienceDataSO audience)
    {
        if (storyBlock == null || audience == null) return false;

        // 检查DTV要求
        if (audience.currentDTV < storyBlock.level) return false;

        // 检查前置故事块
        if (storyBlock.level > 1)
        {
            bool hasPreviousBlock = audience.unlockedStoryBlocks[storyBlock.level - 2];
            if (!hasPreviousBlock) return false;
        }

        // 检查特殊解锁条件
        if (!CheckSpecialUnlockConditions(storyBlock, audience)) return false;

        return true;
    }

    /// <summary>
    /// 检查特殊解锁条件
    /// </summary>
    private bool CheckSpecialUnlockConditions(StoryBlockSO storyBlock, AudienceDataSO audience)
    {
        // 检查是否需要特定关键词
        if (storyBlock.requiredKeywords != null && storyBlock.requiredKeywords.Length > 0)
        {
            foreach (var requiredKeyword in storyBlock.requiredKeywords)
            {
                if (requiredKeyword != null)
                {
                    bool hasKeyword = AudienceService.Instance?.HasKeyword(audience, requiredKeyword.keyword) ?? false;
                    if (!hasKeyword) return false;
                }
            }
        }

        // 检查是否需要特定互动次数
        if (storyBlock.requiredInteractions > 0)
        {
            if (audience.totalInteractions < storyBlock.requiredInteractions) return false;
        }

        // 检查是否需要特定成功共鸣次数
        if (storyBlock.requiredSuccessfulResonances > 0)
        {
            if (audience.successfulResonances < storyBlock.requiredSuccessfulResonances) return false;
        }

        return true;
    }

    /// <summary>
    /// 执行故事块解锁奖励
    /// </summary>
    public void ExecuteStoryBlockRewards(StoryBlockSO storyBlock, AudienceDataSO audience)
    {
        if (storyBlock == null || audience == null) return;

        // 奖励关键词
        foreach (var keywordTemplate in storyBlock.rewardKeywords)
        {
            if (keywordTemplate != null)
            {
                AudienceService.Instance?.CollectKeyword(audience, keywordTemplate);
            }
        }

        // 奖励金钱
        if (storyBlock.rewardMoney > 0)
        {
            GameDataService.Instance?.ModifyMoney(storyBlock.rewardMoney);
        }

        // 奖励粉丝
        if (storyBlock.rewardFans > 0)
        {
            GameDataService.Instance?.AddFans(storyBlock.rewardFans);
        }

        Debug.Log($"故事块奖励已发放：{storyBlock.title}");
    }

    #endregion

    #region 对话选项处理

    /// <summary>
    /// 计算对话选项成功率
    /// </summary>
    public float CalculateDialogueSuccessRate(DialogueOptionSO option, AudienceDataSO audience, List<KeywordDataSO> usedKeywords)
    {
        if (option == null || audience == null) return 0f;

        // 基础成功率
        float baseRate = option.baseSuccessRate;

        // 关键词权重加成
        float keywordBonus = CalculateKeywordBonus(option, usedKeywords);

        // 听众状态加成
        float audienceBonus = CalculateAudienceBonus(option, audience);

        // 选项类型加成
        float typeBonus = CalculateOptionTypeBonus(option, audience);

        // 最终成功率
        float finalRate = Mathf.Clamp01(baseRate + keywordBonus + audienceBonus + typeBonus);

        Debug.Log($"对话选项 {option.optionText} 成功率：{finalRate:P2} " +
                 $"(基础:{baseRate:P2} + 关键词:{keywordBonus:P2} + 听众:{audienceBonus:P2} + 类型:{typeBonus:P2})");

        return finalRate;
    }

    /// <summary>
    /// 计算关键词加成
    /// </summary>
    private float CalculateKeywordBonus(DialogueOptionSO option, List<KeywordDataSO> usedKeywords)
    {
        if (usedKeywords == null || usedKeywords.Count == 0) return 0f;

        float bonus = 0f;
        
        foreach (var keyword in usedKeywords)
        {
            if (keyword != null)
            {
                // 检查关键词类型匹配
                if (option.preferredKeywordTypes.Contains(keyword.keywordType))
                {
                    bonus += keyword.weight * 0.15f; // 匹配类型额外加成
                }
                else
                {
                    bonus += keyword.weight * 0.1f; // 基础权重加成
                }
            }
        }

        return Mathf.Min(bonus, 0.3f); // 最大30%加成
    }

    /// <summary>
    /// 计算听众状态加成
    /// </summary>
    private float CalculateAudienceBonus(DialogueOptionSO option, AudienceDataSO audience)
    {
        float bonus = 0f;

        // DTV等级加成
        bonus += audience.currentDTV * 0.05f; // 每级DTV增加5%

        // 互动历史加成
        if (audience.totalInteractions > 0)
        {
            float successRatio = (float)audience.successfulResonances / audience.totalInteractions;
            bonus += successRatio * 0.1f; // 成功率高的听众更容易成功
        }

        // 听众状态加成
        switch (audience.currentState)
        {
            case AudienceState.FullyTrusted:
                bonus += 0.15f;
                break;
            case AudienceState.Trusting:
                bonus += 0.1f;
                break;
            case AudienceState.Cautious:
                bonus += 0.05f;
                break;
        }

        return bonus;
    }

    /// <summary>
    /// 计算选项类型加成
    /// </summary>
    private float CalculateOptionTypeBonus(DialogueOptionSO option, AudienceDataSO audience)
    {
        float bonus = 0f;

        switch (option.optionType)
        {
            case DialogueOptionType.EmotionalResonance:
                // 情感共鸣在高DTV时更有效
                if (audience.currentDTV >= 3)
                    bonus += 0.1f;
                break;

            case DialogueOptionType.LogicalAnalysis:
                // 逻辑分析在低DTV时更有效
                if (audience.currentDTV <= 2)
                    bonus += 0.1f;
                break;

            case DialogueOptionType.Contradiction:
                // 质询在中等DTV时最有效
                if (audience.currentDTV >= 2 && audience.currentDTV <= 3)
                    bonus += 0.15f;
                else
                    bonus -= 0.1f; // 其他情况下有负面影响
                break;

            case DialogueOptionType.Encouragement:
                // 鼓励总是有轻微正面效果
                bonus += 0.05f;
                break;

            case DialogueOptionType.Guidance:
                // 引导在高信任度时更有效
                if (audience.currentState == AudienceState.FullyTrusted)
                    bonus += 0.12f;
                break;
        }

        return bonus;
    }

    /// <summary>
    /// 执行对话选项效果
    /// </summary>
    public DialogueResult ExecuteDialogueOption(DialogueOptionSO option, AudienceDataSO audience, List<KeywordDataSO> usedKeywords)
    {
        if (option == null || audience == null)
        {
            return new DialogueResult { success = false, message = "无效的对话选项或听众" };
        }

        // 计算成功率
        float successRate = CalculateDialogueSuccessRate(option, audience, usedKeywords);
        bool success = UnityEngine.Random.value <= successRate;

        var result = new DialogueResult
        {
            success = success,
            option = option,
            audience = audience,
            usedKeywords = usedKeywords ?? new List<KeywordDataSO>()
        };

        if (success)
        {
            // 执行成功效果
            ExecuteSuccessEffects(option, audience, result);
        }
        else
        {
            // 执行失败效果
            ExecuteFailureEffects(option, audience, result);
        }

        // 记录互动
        bool isResonance = option.optionType == DialogueOptionType.EmotionalResonance;
        AudienceService.Instance?.RecordInteraction(audience, success, isResonance);

        return result;
    }

    /// <summary>
    /// 执行成功效果
    /// </summary>
    private void ExecuteSuccessEffects(DialogueOptionSO option, AudienceDataSO audience, DialogueResult result)
    {
        // DTV变化
        if (option.dtvChange > 0)
        {
            AudienceService.Instance?.IncreaseDTV(audience, option.dtvChange);
            result.dtvChange = option.dtvChange;
        }

        // 精力变化
        if (option.staminaChange != 0)
        {
            if (option.staminaChange > 0)
                GameDataService.Instance?.RestoreStamina(option.staminaChange);
            else
                GameDataService.Instance?.ConsumeStamina(-option.staminaChange);
            result.staminaChange = option.staminaChange;
        }

        // 温度变化
        if (option.warmthChange != 0)
        {
            GameDataService.Instance?.ModifyWarmth(option.warmthChange);
            result.warmthChange = option.warmthChange;
        }

        // 金钱变化
        if (option.moneyChange != 0)
        {
            GameDataService.Instance?.ModifyMoney(option.moneyChange);
            result.moneyChange = option.moneyChange;
        }

        result.message = option.successMessage;
        Debug.Log($"对话成功：{option.optionText} -> {option.successMessage}");
    }

    /// <summary>
    /// 执行失败效果
    /// </summary>
    private void ExecuteFailureEffects(DialogueOptionSO option, AudienceDataSO audience, DialogueResult result)
    {
        // 失败通常有负面效果
        float staminaCost = 5f; // 基础精力消耗
        
        // 质询失败消耗更多精力
        if (option.optionType == DialogueOptionType.Contradiction)
        {
            staminaCost = 10f;
        }

        GameDataService.Instance?.ConsumeStamina(staminaCost);
        result.staminaChange = -staminaCost;

        result.message = option.failureMessage;
        Debug.Log($"对话失败：{option.optionText} -> {option.failureMessage}");
    }

    #endregion

    #region 数据查询

    /// <summary>
    /// 根据等级获取故事块
    /// </summary>
    public List<StoryBlockSO> GetStoryBlocksByLevel(StoryBlockLevel level)
    {
        return storyBlockTemplates.Where(sb => sb != null && sb.blockLevel == level).ToList();
    }

    /// <summary>
    /// 根据类型获取对话选项
    /// </summary>
    public List<DialogueOptionSO> GetDialogueOptionsByType(DialogueOptionType type)
    {
        return dialogueOptionTemplates.Where(do => do != null && do.optionType == type).ToList();
    }

    /// <summary>
    /// 获取适合当前DTV的对话选项
    /// </summary>
    public List<DialogueOptionSO> GetSuitableDialogueOptions(AudienceDataSO audience)
    {
        if (audience == null) return new List<DialogueOptionSO>();

        return dialogueOptionTemplates.Where(option => 
            option != null && 
            option.requiredDTVLevel <= audience.currentDTV).ToList();
    }

    #endregion

    #region 数据验证

    /// <summary>
    /// 验证故事块数据
    /// </summary>
    public bool ValidateStoryBlockData(StoryBlockSO storyBlock)
    {
        if (storyBlock == null) return false;

        bool isValid = true;

        // 检查基础数据
        if (string.IsNullOrEmpty(storyBlock.title))
        {
            Debug.LogWarning($"故事块缺少标题");
            isValid = false;
        }

        // 检查等级范围
        if (storyBlock.level < 1 || storyBlock.level > 6)
        {
            Debug.LogWarning($"故事块 {storyBlock.title} 等级异常：{storyBlock.level}");
            storyBlock.level = Mathf.Clamp(storyBlock.level, 1, 6);
            isValid = false;
        }

        // 清理空引用
        if (storyBlock.rewardKeywords != null)
        {
            storyBlock.rewardKeywords = storyBlock.rewardKeywords.Where(k => k != null).ToArray();
        }

        return isValid;
    }

    /// <summary>
    /// 验证对话选项数据
    /// </summary>
    public bool ValidateDialogueOptionData(DialogueOptionSO option)
    {
        if (option == null) return false;

        bool isValid = true;

        // 检查基础数据
        if (string.IsNullOrEmpty(option.optionText))
        {
            Debug.LogWarning($"对话选项缺少文本");
            isValid = false;
        }

        // 检查成功率范围
        if (option.baseSuccessRate < 0f || option.baseSuccessRate > 1f)
        {
            Debug.LogWarning($"对话选项 {option.optionText} 成功率异常：{option.baseSuccessRate}");
            option.baseSuccessRate = Mathf.Clamp01(option.baseSuccessRate);
            isValid = false;
        }

        // 检查DTV要求
        if (option.requiredDTVLevel < 0 || option.requiredDTVLevel > 5)
        {
            Debug.LogWarning($"对话选项 {option.optionText} DTV要求异常：{option.requiredDTVLevel}");
            option.requiredDTVLevel = Mathf.Clamp(option.requiredDTVLevel, 0, 5);
            isValid = false;
        }

        return isValid;
    }

    #endregion
}

/// <summary>
/// 对话结果
/// </summary>
[System.Serializable]
public class DialogueResult
{
    public bool success;
    public string message;
    public DialogueOptionSO option;
    public AudienceDataSO audience;
    public List<KeywordDataSO> usedKeywords;
    public int dtvChange;
    public float staminaChange;
    public float warmthChange;
    public float moneyChange;
}
