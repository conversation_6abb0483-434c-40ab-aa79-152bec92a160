using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// 游戏管理器
/// 统一管理游戏状态、业务逻辑和数据流
/// </summary>
public class GameManager : MonoBehaviour
{
    [Header("=== 核心数据引用 ===")]
    [Tooltip("核心游戏数据")]
    public CoreGameDataSO coreGameData;

    [Tooltip("所有听众数据")]
    public List<AudienceDataSO> allAudiences = new List<AudienceDataSO>();

    [Tooltip("所有回忆数据")]
    public List<MemoryDataSO> allMemories = new List<MemoryDataSO>();

    [Header("=== 管理器引用 ===")]
    [Tooltip("关键词管理器")]
    public KeywordManager keywordManager;

    [Tooltip("保存管理器")]
    public ScriptableObjectSaveManager saveManager;

    [Header("=== 游戏设置 ===")]
    [Tooltip("每夜最大互动次数")]
    public int maxInteractionsPerNight = 5;

    [Tooltip("DTV增长基础概率")]
    [Range(0f, 1f)]
    public float baseDTVGrowthChance = 0.3f;

    [Tooltip("关键词过期检查间隔（秒）")]
    public float keywordExpiryCheckInterval = 60f;

    [Header("=== 事件系统 ===")]
    [Tooltip("夜晚开始事件")]
    public UnityEvent<int> OnNightStart;

    [Tooltip("夜晚结束事件")]
    public UnityEvent<int> OnNightEnd;

    [Tooltip("DTV变化事件")]
    public UnityEvent<AudienceDataSO, int> OnDTVChanged;

    [Tooltip("关键词收集事件")]
    public UnityEvent<KeywordDataSO> OnKeywordCollected;

    [Tooltip("故事解锁事件")]
    public UnityEvent<AudienceDataSO, int> OnStoryUnlocked;

    // 私有字段
    private float lastKeywordCheck;
    private int currentInteractionCount;
    private bool isNightActive = false;

    // 单例模式
    public static GameManager Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        // 单例模式设置
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeGame();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        StartCoroutine(GameUpdateLoop());
    }

    private void Update()
    {
        // 定期检查关键词过期
        if (Time.time - lastKeywordCheck >= keywordExpiryCheckInterval)
        {
            CheckKeywordExpiry();
            lastKeywordCheck = Time.time;
        }
    }

    #endregion

    #region 游戏初始化

    /// <summary>
    /// 初始化游戏
    /// </summary>
    private void InitializeGame()
    {
        // 确保数据引用正确
        if (coreGameData == null)
        {
            Debug.LogError("CoreGameData未设置！");
            return;
        }

        // 初始化管理器引用
        if (keywordManager == null)
        {
            keywordManager = KeywordManager.Instance;
        }

        if (saveManager == null)
        {
            saveManager = ScriptableObjectSaveManager.Instance;
        }

        // 设置保存管理器的数据引用
        if (saveManager != null)
        {
            saveManager.coreGameData = coreGameData;
            saveManager.allAudiences = allAudiences;
            saveManager.allMemories = allMemories;
        }

        Debug.Log("游戏管理器初始化完成");
    }

    #endregion

    #region 游戏循环

    /// <summary>
    /// 游戏更新循环
    /// </summary>
    private IEnumerator GameUpdateLoop()
    {
        while (true)
        {
            // 每秒执行一次更新
            yield return new WaitForSeconds(1f);

            if (isNightActive)
            {
                UpdateNightProgress();
            }

            UpdateMemorySystem();
            ProcessKeywordEvolution();
        }
    }

    /// <summary>
    /// 更新夜晚进度
    /// </summary>
    private void UpdateNightProgress()
    {
        // 检查是否需要结束夜晚
        if (currentInteractionCount >= maxInteractionsPerNight)
        {
            EndNight();
        }
    }

    /// <summary>
    /// 更新回忆系统
    /// </summary>
    private void UpdateMemorySystem()
    {
        foreach (var memory in allMemories)
        {
            if (memory != null && memory.CanUnlock(coreGameData, allAudiences))
            {
                memory.UnlockMemory();
            }
        }
    }

    /// <summary>
    /// 处理关键词演化
    /// </summary>
    private void ProcessKeywordEvolution()
    {
        if (keywordManager != null)
        {
            var evolvedKeywords = keywordManager.ProcessKeywordEvolution();
            foreach (var keyword in evolvedKeywords)
            {
                OnKeywordCollected?.Invoke(keyword);
            }
        }
    }

    #endregion

    #region 夜晚管理

    /// <summary>
    /// 开始新的夜晚
    /// </summary>
    public void StartNight()
    {
        if (isNightActive)
        {
            Debug.LogWarning("夜晚已经开始了！");
            return;
        }

        isNightActive = true;
        currentInteractionCount = 0;
        coreGameData.currentPhase = GamePhase.Preparation;

        // 重置每夜的状态
        coreGameData.currentWarmth = 5f;

        // 清理过期关键词
        CheckKeywordExpiry();

        OnNightStart?.Invoke(coreGameData.currentNight);
        Debug.Log($"开始第 {coreGameData.currentNight} 夜");
    }

    /// <summary>
    /// 结束当前夜晚
    /// </summary>
    public void EndNight()
    {
        if (!isNightActive)
        {
            Debug.LogWarning("夜晚尚未开始！");
            return;
        }

        isNightActive = false;

        // 计算夜晚结算
        CalculateNightSettlement();

        OnNightEnd?.Invoke(coreGameData.currentNight);

        // 进入下一夜
        coreGameData.AdvanceToNextNight();

        // 自动保存
        if (saveManager != null)
        {
            saveManager.AutoSave();
        }

        Debug.Log($"第 {coreGameData.currentNight - 1} 夜结束");
    }

    /// <summary>
    /// 计算夜晚结算
    /// </summary>
    private void CalculateNightSettlement()
    {
        // 计算粉丝增长
        int fanGrowth = coreGameData.CalculateFanGrowth();
        coreGameData.AddFans(fanGrowth);

        // 计算收入
        float income = coreGameData.CalculateBaseIncome();
        coreGameData.ModifyMoney(income);

        // 恢复精力
        float staminaRestore = 20f + (coreGameData.currentWarmth - 5f) * 5f;
        coreGameData.RestoreStamina(staminaRestore);

        Debug.Log($"夜晚结算：粉丝+{fanGrowth}，收入+{income:F2}，精力+{staminaRestore:F1}");
    }

    #endregion

    #region 听众互动

    /// <summary>
    /// 与听众互动
    /// </summary>
    public bool InteractWithAudience(AudienceDataSO audience, DialogueOptionSO option)
    {
        if (!isNightActive)
        {
            Debug.LogWarning("夜晚尚未开始，无法互动！");
            return false;
        }

        if (currentInteractionCount >= maxInteractionsPerNight)
        {
            Debug.LogWarning("今夜互动次数已达上限！");
            return false;
        }

        if (audience == null || option == null)
        {
            Debug.LogWarning("听众或选项为空！");
            return false;
        }

        // 执行对话选项
        bool success = option.ExecuteOption(audience, coreGameData);

        if (success)
        {
            currentInteractionCount++;
            coreGameData.currentPhase = GamePhase.Connection;

            // 触发事件
            OnDTVChanged?.Invoke(audience, audience.currentDTV);
        }

        return success;
    }

    /// <summary>
    /// 收集关键词
    /// </summary>
    public bool CollectKeyword(AudienceDataSO audience, string keywordText)
    {
        if (audience == null || string.IsNullOrEmpty(keywordText))
        {
            return false;
        }

        if (keywordManager != null)
        {
            var keyword = keywordManager.CreateKeyword(keywordText, audience.audienceId);
            if (keyword != null && audience.CollectKeyword(keyword))
            {
                OnKeywordCollected?.Invoke(keyword);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 尝试解锁故事块
    /// </summary>
    public bool TryUnlockStory(AudienceDataSO audience, int level)
    {
        if (audience == null || level < 1 || level > 6)
        {
            return false;
        }

        if (audience.UnlockStoryBlock(level))
        {
            OnStoryUnlocked?.Invoke(audience, level);

            // 触发故事块
            var storyBlock = audience.GetStoryBlock(level);
            if (storyBlock != null)
            {
                storyBlock.TriggerStory(audience, coreGameData);
            }

            return true;
        }

        return false;
    }

    #endregion

    #region 关键词管理

    /// <summary>
    /// 检查关键词过期
    /// </summary>
    private void CheckKeywordExpiry()
    {
        foreach (var audience in allAudiences)
        {
            if (audience != null)
            {
                audience.CleanupExpiredKeywords();
            }
        }

        if (keywordManager != null)
        {
            keywordManager.CleanupExpiredKeywords();
        }
    }

    /// <summary>
    /// 执行关键词组合
    /// </summary>
    public KeywordDataSO ExecuteKeywordCombination(KeywordCombinationSO combination, List<KeywordDataSO> availableKeywords)
    {
        if (keywordManager != null)
        {
            var result = keywordManager.ExecuteCombination(combination, availableKeywords, coreGameData);
            if (result != null)
            {
                OnKeywordCollected?.Invoke(result);
            }
            return result;
        }
        return null;
    }

    /// <summary>
    /// 获取组合提示
    /// </summary>
    public List<string> GetCombinationHints(AudienceDataSO audience)
    {
        if (keywordManager != null && audience != null)
        {
            return keywordManager.GetCombinationHints(audience.GetValidKeywords());
        }
        return new List<string>();
    }

    #endregion

    #region 回忆系统

    /// <summary>
    /// 激活回忆
    /// </summary>
    public bool ActivateMemory(MemoryDataSO memory)
    {
        if (memory != null && memory.isUnlocked && !memory.isActive && !memory.isSuppressed)
        {
            memory.ActivateMemory(coreGameData);
            return true;
        }
        return false;
    }

    /// <summary>
    /// 压制回忆
    /// </summary>
    public bool SuppressMemory(MemoryDataSO memory)
    {
        if (memory != null)
        {
            return memory.SuppressMemory(coreGameData);
        }
        return false;
    }

    #endregion

    #region 数据查询

    /// <summary>
    /// 获取听众通过ID
    /// </summary>
    public AudienceDataSO GetAudienceById(string audienceId)
    {
        return allAudiences.FirstOrDefault(a => a != null && a.audienceId == audienceId);
    }

    /// <summary>
    /// 获取所有可互动的听众
    /// </summary>
    public List<AudienceDataSO> GetInteractableAudiences()
    {
        return allAudiences.Where(a => a != null && a.currentState != AudienceState.Unknown).ToList();
    }

    /// <summary>
    /// 获取游戏统计信息
    /// </summary>
    public GameStatistics GetGameStatistics()
    {
        return new GameStatistics
        {
            currentNight = coreGameData.currentNight,
            totalFans = coreGameData.totalFans,
            totalMoney = coreGameData.money,
            totalAudiences = allAudiences.Count,
            discoveredAudiences = allAudiences.Count(a => a.currentState != AudienceState.Unknown),
            completedStories = allAudiences.Sum(a => a.unlockedStoryBlocks.Count(unlocked => unlocked)),
            unlockedMemories = allMemories.Count(m => m.isUnlocked),
            activeKeywords = allAudiences.Sum(a => a.GetValidKeywords().Count)
        };
    }

    #endregion

    #region 调试方法

    [ContextMenu("开始新夜晚")]
    private void DebugStartNight()
    {
        if (Application.isPlaying)
        {
            StartNight();
        }
    }

    [ContextMenu("结束当前夜晚")]
    private void DebugEndNight()
    {
        if (Application.isPlaying)
        {
            EndNight();
        }
    }

    [ContextMenu("显示游戏统计")]
    private void DebugShowStatistics()
    {
        if (Application.isPlaying)
        {
            var stats = GetGameStatistics();
            Debug.Log($"游戏统计：第{stats.currentNight}夜，粉丝{stats.totalFans}，金钱{stats.totalMoney:F2}");
        }
    }

    #endregion

    #region 保存/加载接口

    /// <summary>
    /// 手动保存游戏
    /// </summary>
    public bool SaveGame(string slotName = null)
    {
        if (saveManager != null)
        {
            return saveManager.SaveGame(slotName);
        }
        return false;
    }

    /// <summary>
    /// 加载游戏
    /// </summary>
    public bool LoadGame(string slotName = null)
    {
        if (saveManager != null)
        {
            bool success = saveManager.LoadGame(slotName);
            if (success)
            {
                // 重新初始化游戏状态
                RefreshGameState();
            }
            return success;
        }
        return false;
    }

    /// <summary>
    /// 刷新游戏状态
    /// </summary>
    private void RefreshGameState()
    {
        // 重新设置当前阶段
        if (coreGameData.currentPhase == GamePhase.Broadcasting ||
            coreGameData.currentPhase == GamePhase.Connection)
        {
            isNightActive = true;
        }
        else
        {
            isNightActive = false;
        }

        // 清理过期关键词
        CheckKeywordExpiry();

        Debug.Log("游戏状态已刷新");
    }

    /// <summary>
    /// 获取存档列表
    /// </summary>
    public List<SaveSlotInfo> GetSaveSlots()
    {
        if (saveManager != null)
        {
            return saveManager.GetAllSaveSlots();
        }
        return new List<SaveSlotInfo>();
    }

    /// <summary>
    /// 删除存档
    /// </summary>
    public bool DeleteSave(string slotName)
    {
        if (saveManager != null)
        {
            return saveManager.DeleteSave(slotName);
        }
        return false;
    }

    #endregion
}

/// <summary>
/// 游戏统计信息
/// </summary>
[System.Serializable]
public class GameStatistics
{
    public int currentNight;
    public int totalFans;
    public float totalMoney;
    public int totalAudiences;
    public int discoveredAudiences;
    public int completedStories;
    public int unlockedMemories;
    public int activeKeywords;
}
