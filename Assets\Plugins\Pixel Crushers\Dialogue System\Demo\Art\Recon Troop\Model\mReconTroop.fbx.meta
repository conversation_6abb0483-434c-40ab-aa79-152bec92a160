fileFormatVersion: 2
guid: a9cfab5a8b4a0a0448926ba5ca880442
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip01_L_Finger01
    100002: Bip01_R_UpperArm
    100004: Bip01_L_Finger21
    100006: LeftHandLitFingerB
    100008: Bip01_L_UpperArm
    100010: Bip01_R_Finger12
    100012: LeftHandLitFingerA
    100014: Bip01_R_Finger01
    100016: Bip01_R_Clavicle
    100018: InfiltratorPistol
    100020: Bip01_L_Finger11
    100022: Bip01_R_Finger11
    100024: Bip01_R_Finger02
    100026: Bip01_L_Finger12
    100028: Bip01_L_Clavicle
    100030: Bip01_R_Calf
    100032: Bip01_R_Thigh
    100034: //RootNode
    100036: Bip01_L_Toe0
    100038: Bip01_L_Foot
    100040: Head
    100042: Bip01_L_Calf
    100044: ReconTroop
    100046: Bip01_R_Foot
    100048: Bip01_L_Thigh
    100050: Bip01_R_Finger2
    100052: Bip01_R_Finger1
    100054: Bip01_R_Finger0
    100056: Bip01_R_Hand
    100058: Bip01_R_Forearm
    100060: Bip01_L_Finger2
    100062: Bip01_L_Finger1
    100064: Bip01_L_Finger0
    100066: Bip01_L_Hand
    100068: Bip01_L_Forearm
    100070: Bip01_Head
    100072: Bip01_Neck
    100074: Bip01_Spine2
    100076: Bip01_Spine1
    100078: Bip01_Spine
    100080: Bip01_Pelvis
    100082: Bip01
    100084: AssaultGun
    100086: Bip01_R_Toe0
    100088: ReconTroopHelmet
    400000: Bip01_L_Finger01
    400002: Bip01_R_UpperArm
    400004: Bip01_L_Finger21
    400006: LeftHandLitFingerB
    400008: Bip01_L_UpperArm
    400010: Bip01_R_Finger12
    400012: LeftHandLitFingerA
    400014: Bip01_R_Finger01
    400016: Bip01_R_Clavicle
    400018: InfiltratorPistol
    400020: Bip01_L_Finger11
    400022: Bip01_R_Finger11
    400024: Bip01_R_Finger02
    400026: Bip01_L_Finger12
    400028: Bip01_L_Clavicle
    400030: Bip01_R_Calf
    400032: Bip01_R_Thigh
    400034: //RootNode
    400036: Bip01_L_Toe0
    400038: Bip01_L_Foot
    400040: Head
    400042: Bip01_L_Calf
    400044: ReconTroop
    400046: Bip01_R_Foot
    400048: Bip01_L_Thigh
    400050: Bip01_R_Finger2
    400052: Bip01_R_Finger1
    400054: Bip01_R_Finger0
    400056: Bip01_R_Hand
    400058: Bip01_R_Forearm
    400060: Bip01_L_Finger2
    400062: Bip01_L_Finger1
    400064: Bip01_L_Finger0
    400066: Bip01_L_Hand
    400068: Bip01_L_Forearm
    400070: Bip01_Head
    400072: Bip01_Neck
    400074: Bip01_Spine2
    400076: Bip01_Spine1
    400078: Bip01_Spine
    400080: Bip01_Pelvis
    400082: Bip01
    400084: AssaultGun
    400086: Bip01_R_Toe0
    400088: ReconTroopHelmet
    4300000: AssaultGun
    4300002: InfiltratorPistol
    4300004: ReconTroop
    4300006: ReconTroopHelmet
    4300008: Head
    7400000: Animation
    7400002: NPcUseObject
    7400004: PistolCombatIdle
    7400006: PistolFromCombatToSquatA
    7400008: PistolFromCombatToSquatB
    7400010: PistolFromLyingToSquat
    7400012: PistolFromLyingToStand
    7400014: PistolFromSquatToCombatA
    7400016: PistolFromSquatToCombatB
    7400018: PistolFromStandToLying
    7400020: PistolFronSquatToLying
    7400022: PistolLeftFootKick
    7400024: PistolLookingAround
    7400026: PistolReloadOneHandedSquat
    7400028: PistolOneHandLookingAroundKneeling
    7400030: PistolOneHandShootingStanding
    7400032: PistolPuttingGunBack
    7400034: PistolReloadOneHandSquat
    7400036: PistolReloadStandingStill
    7400038: PistolReloadTwoHands
    7400040: PistolRightFootkick
    7400042: PistolTakingGunOut
    7400044: PistolTwoHandsLookingAround
    7400046: PistolTwoHandsLyingShooting
    7400048: PistolTwoHandsShooting
    7400050: PistolTwoHandsSquatPointing
    7400052: PistolTwoHandsFireStandingStill
    7400054: TwoHandGunIButtkick
    7400056: TwoHandGunIdle
    7400058: TwoHandGunIDyinglying
    7400060: HandGunIDyingback
    7400062: TwoHandGunIDyingsquat
    7400064: TwoHandGunIFirelying
    7400066: TwoHandGunIFiresquat
    7400068: TwoHandGunIFirestanding
    7400070: TwoHandGunIFireReadysquat
    7400072: TwoHandGunIFireReadystanding
    7400074: TwoHandGunFromLayingToStanding
    7400076: TwoHandGunFromSquatToStanding
    7400078: TwoHandGunIFromLayingtoSquat
    7400080: TwoHandGunIFromSquattoLaying
    7400082: TwoHandGunIGrenadethrow
    7400084: TwoHandGunIJump
    7400086: TwoHandGunILookingAroundSquatA
    7400088: TwoHandGunIMovingleft
    7400090: TwoHandGunIMovingright
    7400092: TwoHandGunIReloadsquat
    7400094: TwoHandGunIReloadstanding
    7400096: TwoHandGunIRun
    7400098: TwoHandGunIFromStandingToSquat
    7400100: TwoHandGunIWalking
    7400102: TwoHandGunIWalkingback
    9500000: //RootNode
    11100000: //RootNode
    13700000: InfiltratorPistol
    13700002: Head
    13700004: ReconTroop
    13700006: AssaultGun
    13700008: ReconTroopHelmet
    2186277476908879412: ImportLogs
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: AssaultGun
    second: {fileID: 2100000, guid: 5eff153ada2024d40ace67fb4049f1b0, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Head
    second: {fileID: 2100000, guid: 67e66cd37fc0b794aa0a01c6e5d9a7cb, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: InfiltratorPistol
    second: {fileID: 2100000, guid: 359754cc85114274b96e79e18b55f692, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: ReconTroop
    second: {fileID: 2100000, guid: 92cc50cb1813aea47b6096bec7d25a1a, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: ReconTroopHelmet
    second: {fileID: 2100000, guid: 2b14441d002ea3940a79460708b788f4, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: "\nClip 'NPcUseObject' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Hand'
      has scale animation that will be discarded.\n\t'Bip01_R_Finger0' has scale animation
      that will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_L_Thigh' has scale animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'PistolCombatIdle'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is
      inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Hand'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_L_Thigh' has scale animation that
      will be discarded.\n\t'Bip01_R_Thigh' has translation animation that will be
      discarded.\n\nClip 'PistolFromCombatToSquatA' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine' has translation
      animation that will be discarded.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Hand'
      has scale animation that will be discarded.\n\t'Bip01_R_Clavicle' has scale
      animation that will be discarded.\n\t'Bip01_R_UpperArm' has scale animation
      that will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'PistolFromCombatToSquatB' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be
      discarded.\n\t'Bip01_L_Thigh' has scale animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'PistolFromLyingToSquat'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be
      discarded.\n\t'Bip01_L_Foot' has scale animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'PistolFromLyingToStand'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Finger0' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be
      discarded.\n\t'Bip01_L_Foot' has scale animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'PistolFromSquatToCombatA'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Toe0' has scale animation that
      will be discarded.\n\nClip 'PistolFromSquatToCombatB' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine' has translation
      animation that will be discarded.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_Hand' has scale
      animation that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation
      that will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'PistolFromStandToLying' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Finger0' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_R_UpperArm' has scale animation that will be discarded.\n\t'Bip01_L_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Foot' has scale animation that
      will be discarded.\n\nClip 'PistolFronSquatToLying' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine' has translation
      animation that will be discarded.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine2'
      has translation animation that will be discarded.\n\t'Bip01_L_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has scale
      animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation that
      will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that will
      be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'PistolLeftFootKick'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_UpperArm' has scale animation that will
      be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Foot' has scale
      animation that will be discarded.\n\nClip 'PistolLookingAround' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_R_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'PistolOneHandedSquat' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Hand'
      has scale animation that will be discarded.\n\t'Bip01_R_Finger0' has scale animation
      that will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\t'Bip01_R_Thigh'
      has scale animation that will be discarded.\n\nClip 'PistolOneHandKneeling'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is
      inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_R_Finger0' has scale animation
      that will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'PistolOneHandShottingStanding' has import animation warnings that might lower
      retargeting quality:\nNote: Activate translation DOF on avatar to improve retargeting
      quality.\n\t'Bip01_Spine' is inbetween humanoid transforms and has rotation
      animation that will be discarded.\n\t'Bip01_Spine1' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_R_Thigh' has scale animation that will be
      discarded.\n\nClip 'PistolPuttingGunBack' has import animation warnings that
      might lower retargeting quality:\nNote: Activate translation DOF on avatar to
      improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Thigh' has translation
      animation that will be discarded.\n\nClip 'PistolReloadOneHandSquat' has import
      animation warnings that might lower retargeting quality:\nNote: Activate translation
      DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\t'Bip01_R_Thigh'
      has scale animation that will be discarded.\n\nClip 'PistolReloadStandingStill'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is
      inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Finger0' has scale animation that will be
      discarded.\n\t'Bip01_L_Thigh' has translation animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'PistolReloadTwoHands'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is
      inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_R_UpperArm' has scale animation that will be discarded.\n\t'Bip01_R_Finger0'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'PistolRightFootkick' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine' has translation
      animation that will be discarded.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Hand'
      has scale animation that will be discarded.\n\t'Bip01_R_UpperArm' has scale
      animation that will be discarded.\n\t'Bip01_L_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_L_Foot' has scale animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'PistolTakingGunOut'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is
      inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_L_Thigh' has scale animation that
      will be discarded.\n\t'Bip01_R_Thigh' has translation animation that will be
      discarded.\n\nClip 'PistolTwoHandsLookingAround' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be
      discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'PistolTwoHandsShooting' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_R_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'PistolTwoHandsSquatPointing' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_R_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'PistolTwoHandsStandingStill' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Thigh' has translation
      animation that will be discarded.\n\nClip 'TwoHandGunIButtkick' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      has translation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine2'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_UpperArm' has scale animation that will
      be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'TwoHandGunIdle' has
      import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is
      inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Thigh' has translation
      animation that will be discarded.\n\nClip 'TwoHandGunIDyinglying' has import
      animation warnings that might lower retargeting quality:\nNote: Activate translation
      DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Hand'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'HandGunIDyingback' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine' has translation
      animation that will be discarded.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' has translation
      animation that will be discarded.\n\t'Bip01_Spine1' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine2' has translation
      animation that will be discarded.\n\t'Bip01_L_Clavicle' has translation animation
      that will be discarded.\n\t'Bip01_L_UpperArm' has scale animation that will
      be discarded.\n\t'Bip01_R_Clavicle' has translation animation that will be discarded.\n\t'Bip01_R_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_R_Foot' has scale animation that will be discarded.\n\nClip
      'TwoHandGunIDyingsquat' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_R_Clavicle' has translation
      animation that will be discarded.\n\t'Bip01_L_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_R_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_R_Foot' has scale animation that will be discarded.\n\nClip
      'TwoHandGunIFiresquat' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_L_Forearm'
      has scale animation that will be discarded.\n\nClip 'TwoHandGunIFirestanding'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is
      inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Foot' has scale animation that
      will be discarded.\n\nClip 'TwoHandGunIFireReadysquat' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be
      discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'TwoHandGunIFireReadystanding' has import animation warnings that might lower
      retargeting quality:\nNote: Activate translation DOF on avatar to improve retargeting
      quality.\n\t'Bip01_Pelvis' is inbetween humanoid transforms and has rotation
      animation that will be discarded.\n\t'Bip01_Spine' has translation animation
      that will be discarded.\n\t'Bip01_Spine' is inbetween humanoid transforms and
      has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Neck'
      has scale animation that will be discarded.\n\t'Bip01_L_Clavicle' has translation
      animation that will be discarded.\n\t'Bip01_L_UpperArm' has scale animation
      that will be discarded.\n\t'Bip01_L_Hand' has scale animation that will be discarded.\n\t'Bip01_R_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'toyingdGunIFromStanding' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be
      discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'touatndGunIFromstanding' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_R_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'TwoHandGunIFromLayingtoSquat' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_L_Finger0' has scale animation that will be
      discarded.\n\t'Bip01_R_Clavicle' has translation animation that will be discarded.\n\t'Bip01_R_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_L_Thigh' has scale animation that
      will be discarded.\n\t'Bip01_L_Foot' has scale animation that will be discarded.\n\t'Bip01_R_Thigh'
      has translation animation that will be discarded.\n\nClip 'TwoHandGunIFromSquattoLaying'
      has import animation warnings that might lower retargeting quality:\nNote: Activate
      translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_L_Finger0' has scale animation that will be
      discarded.\n\t'Bip01_R_Clavicle' has translation animation that will be discarded.\n\t'Bip01_R_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_R_Thigh' has scale animation that will be
      discarded.\n\nClip 'TwoHandGunIGrenadethrow' has import animation warnings that
      might lower retargeting quality:\nNote: Activate translation DOF on avatar to
      improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine' has translation
      animation that will be discarded.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_R_UpperArm' has scale
      animation that will be discarded.\n\t'Bip01_L_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_R_Thigh' has translation animation that will
      be discarded.\n\t'Bip01_R_Foot' has scale animation that will be discarded.\n\nClip
      'TwoHandGunIJump' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Hand'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'TwoHandGunILookingAroundSquatA' has import
      animation warnings that might lower retargeting quality:\nNote: Activate translation
      DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_R_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'TwoHandGunIMovingright' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Neck' has scale animation
      that will be discarded.\n\t'Bip01_R_UpperArm' has scale animation that will
      be discarded.\n\nClip 'TwoHandGunIReloadsquat' has import animation warnings
      that might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Bip01_Spine' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n\t'Bip01_Spine1' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_L_Thigh' has translation animation that will be
      discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'TwoHandGunIReloadstanding' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Spine'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_L_UpperArm' has
      scale animation that will be discarded.\n\t'Bip01_L_Hand' has scale animation
      that will be discarded.\n\t'Bip01_R_Clavicle' has translation animation that
      will be discarded.\n\t'Bip01_R_Finger0' has scale animation that will be discarded.\n\t'Bip01_L_Thigh'
      has translation animation that will be discarded.\n\t'Bip01_R_Thigh' has translation
      animation that will be discarded.\n\nClip 'TwoHandGunIRun' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_R_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_R_UpperArm' has scale
      animation that will be discarded.\n\t'Bip01_L_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_R_Thigh' has translation animation that will
      be discarded.\n\nClip 'toandingunITwoHandGunIFromsquat' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Clavicle'
      has translation animation that will be discarded.\n\t'Bip01_R_Clavicle' has
      translation animation that will be discarded.\n\t'Bip01_R_UpperArm' has scale
      animation that will be discarded.\n\t'Bip01_L_Thigh' has translation animation
      that will be discarded.\n\t'Bip01_L_Thigh' has scale animation that will be
      discarded.\n\t'Bip01_R_Thigh' has translation animation that will be discarded.\n\nClip
      'TwoHandGunIWalking' has import animation warnings that might lower retargeting
      quality:\nNote: Activate translation DOF on avatar to improve retargeting quality.\n\t'Bip01_Pelvis'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_R_UpperArm'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n\nClip 'TwoHandGunIWalkingback' has import animation
      warnings that might lower retargeting quality:\nNote: Activate translation DOF
      on avatar to improve retargeting quality.\n\t'Bip01_Pelvis' is inbetween humanoid
      transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine'
      has translation animation that will be discarded.\n\t'Bip01_Spine' is inbetween
      humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_Spine1'
      is inbetween humanoid transforms and has rotation animation that will be discarded.\n\t'Bip01_L_Forearm'
      has scale animation that will be discarded.\n\t'Bip01_L_Thigh' has translation
      animation that will be discarded.\n\t'Bip01_R_Thigh' has translation animation
      that will be discarded.\n"
    animationRetargetingWarnings: "\nRetargeting quality report for clip 'Animation':\n\tLeftLowerArm
      average position error 2.9 mm and maximum position error 2.9 mm at time 0: 0
      (0.0%) Frame 0\n\tRightLowerArm average position error 2.9 mm and maximum position
      error 2.9 mm at time 0: 0 (0.0%) Frame 0\n\nRetargeting quality report for clip
      'NPcUseObject':\n\tLeftLowerLeg average position error 17.9 mm and maximum position
      error 21.3 mm at time 0:13 (33.3%) Frame 13\n\tLeftFoot average position error
      36.8 mm and maximum position error 47.4 mm at time 0:13 (33.3%) Frame 13\n\tRightLowerLeg
      average position error 15.1 mm and maximum position error 16.1 mm at time 0:
      0 (0.0%) Frame 0\n\tRightFoot average position error 15.4 mm and maximum position
      error 18.0 mm at time 1: 9 (100.0%) Frame 39\n\tLeftUpperArm average position
      error 48.3 mm and maximum position error 63.4 mm at time 0:12 (30.8%) Frame
      12\n\tLeftLowerArm average position error 21.1 mm and maximum position error
      26.3 mm at time 0:22 (56.4%) Frame 22\n\tLeftHand average position error 20.6
      mm and maximum position error 23.4 mm at time 0:22 (56.4%) Frame 22\n\tRightUpperArm
      average position error 49.7 mm and maximum position error 65.3 mm at time 0:12
      (30.8%) Frame 12\n\tRightLowerArm average position error 34.8 mm and maximum
      position error 51.3 mm at time 0:12 (30.8%) Frame 12\n\tRightHand average position
      error 44.2 mm and maximum position error 61.9 mm at time 0:24 (61.5%) Frame
      24\n\tHead average position error 53.1 mm and maximum position error 68.1 mm
      at time 0:12 (30.8%) Frame 12\n\nRetargeting quality report for clip 'PistolCombatIdle':\n\tLeftLowerLeg
      average position error 13.2 mm and maximum position error 16.9 mm at time 0:18
      (22.5%) Frame 18\n\tLeftFoot average position error 23.9 mm and maximum position
      error 31.0 mm at time 0:18 (22.5%) Frame 18\n\tRightLowerLeg average position
      error 23.4 mm and maximum position error 27.3 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot
      average position error 46.2 mm and maximum position error 54.2 mm at time 0:
      0 (0.0%) Frame 0\n\tLeftUpperArm average position error 20.9 mm and maximum
      position error 25.2 mm at time 0: 0 (0.0%) Frame 0\n\tLeftLowerArm average position
      error 8.6 mm and maximum position error 11.6 mm at time 0:22 (27.5%) Frame 22\n\tLeftHand
      average position error 12.9 mm and maximum position error 15.3 mm at time 0:23
      (28.8%) Frame 23\n\tRightUpperArm average position error 20.9 mm and maximum
      position error 25.2 mm at time 0: 0 (0.0%) Frame 0\n\tRightLowerArm average
      position error 9.3 mm and maximum position error 11.2 mm at time 0:22 (27.5%)
      Frame 22\n\tRightHand average position error 11.6 mm and maximum position error
      16.1 mm at time 0: 0 (0.0%) Frame 0\n\tHead average position error 25.9 mm and
      maximum position error 33.1 mm at time 0: 0 (0.0%) Frame 0\n\nRetargeting quality
      report for clip 'PistolFromCombatToSquatA':\n\tLeftLowerLeg average position
      error 39.1 mm and maximum position error 109.1 mm at time 1: 1 (100.0%) Frame
      31\n\tLeftFoot average position error 37.9 mm and maximum position error 87.2
      mm at time 1: 1 (100.0%) Frame 31\n\tRightLowerLeg average position error 34.8
      mm and maximum position error 99.7 mm at time 1: 1 (100.0%) Frame 31\n\tRightFoot
      average position error 34.9 mm and maximum position error 55.1 mm at time 1:
      1 (100.0%) Frame 31\n\tLeftUpperArm average position error 86.6 mm and maximum
      position error 131.0 mm at time 0:21 (67.7%) Frame 21\n\tLeftLowerArm average
      position error 29.4 mm and maximum position error 45.4 mm at time 0:21 (67.7%)
      Frame 21\n\tLeftHand average position error 35.5 mm and maximum position error
      53.1 mm at time 0:17 (54.8%) Frame 17\n\tRightUpperArm average position error
      86.6 mm and maximum position error 131.0 mm at time 0:21 (67.7%) Frame 21\n\tRightLowerArm
      average position error 64.2 mm and maximum position error 117.1 mm at time 0:22
      (71.0%) Frame 22\n\tRightHand average position error 70.8 mm and maximum position
      error 123.6 mm at time 0:21 (67.7%) Frame 21\n\tHead average position error
      98.0 mm and maximum position error 142.8 mm at time 0:21 (67.7%) Frame 21\n\nRetargeting
      quality report for clip 'PistolFromCombatToSquatB':\n\tLeftLowerLeg average
      position error 31.3 mm and maximum position error 61.3 mm at time 0:16 (35.6%)
      Frame 16\n\tLeftFoot average position error 52.5 mm and maximum position error
      105.9 mm at time 0:15 (33.3%) Frame 15\n\tRightLowerLeg average position error
      51.3 mm and maximum position error 71.3 mm at time 0:20 (44.4%) Frame 20\n\tRightFoot
      average position error 83.5 mm and maximum position error 127.2 mm at time 0:20
      (44.4%) Frame 20\n\tLeftUpperArm average position error 147.5 mm and maximum
      position error 215.1 mm at time 0:18 (40.0%) Frame 18\n\tLeftLowerArm average
      position error 107.6 mm and maximum position error 194.3 mm at time 0:19 (42.2%)
      Frame 19\n\tLeftHand average position error 116.7 mm and maximum position error
      204.7 mm at time 0:18 (40.0%) Frame 18\n\tRightUpperArm average position error
      147.6 mm and maximum position error 212.4 mm at time 0:13 (28.9%) Frame 13\n\tRightLowerArm
      average position error 104.7 mm and maximum position error 220.7 mm at time
      0:13 (28.9%) Frame 13\n\tRightHand average position error 121.8 mm and maximum
      position error 216.5 mm at time 0:14 (31.1%) Frame 14\n\tHead average position
      error 156.7 mm and maximum position error 196.7 mm at time 0:19 (42.2%) Frame
      19\n\nRetargeting quality report for clip 'PistolFromLyingToSquat':\n\tLeftLowerLeg
      average position error 69.8 mm and maximum position error 140.6 mm at time 0:13
      (34.2%) Frame 13\n\tLeftFoot average position error 49.7 mm and maximum position
      error 87.2 mm at time 1: 8 (100.0%) Frame 38\n\tRightLowerLeg average position
      error 33.9 mm and maximum position error 99.7 mm at time 1: 8 (100.0%) Frame
      38\n\tRightFoot average position error 24.8 mm and maximum position error 55.1
      mm at time 1: 8 (100.0%) Frame 38\n\tLeftUpperArm average position error 112.7
      mm and maximum position error 222.9 mm at time 0:17 (44.7%) Frame 17\n\tLeftLowerArm
      average position error 68.5 mm and maximum position error 139.3 mm at time 0:17
      (44.7%) Frame 17\n\tLeftHand average position error 76.0 mm and maximum position
      error 148.9 mm at time 0:21 (55.3%) Frame 21\n\tRightUpperArm average position
      error 112.7 mm and maximum position error 222.9 mm at time 0:17 (44.7%) Frame
      17\n\tRightLowerArm average position error 78.3 mm and maximum position error
      141.1 mm at time 0:17 (44.7%) Frame 17\n\tRightHand average position error 87.5
      mm and maximum position error 149.7 mm at time 0:17 (44.7%) Frame 17\n\tHead
      average position error 116.4 mm and maximum position error 229.2 mm at time
      0:17 (44.7%) Frame 17\n\nRetargeting quality report for clip 'PistolFromLyingToStand':\n\tLeftLowerLeg
      average position error 6.6 mm and maximum position error 14.7 mm at time 0:
      6 (17.1%) Frame 6\n\tLeftFoot average position error 15.3 mm and maximum position
      error 29.2 mm at time 0: 6 (17.1%) Frame 6\n\tRightLowerLeg average position
      error 16.9 mm and maximum position error 27.3 mm at time 1: 5 (100.0%) Frame
      35\n\tRightFoot average position error 25.6 mm and maximum position error 54.2
      mm at time 1: 5 (100.0%) Frame 35\n\tLeftUpperArm average position error 69.8
      mm and maximum position error 112.2 mm at time 0:16 (45.7%) Frame 16\n\tLeftLowerArm
      average position error 57.1 mm and maximum position error 94.4 mm at time 0:16
      (45.7%) Frame 16\n\tLeftHand average position error 61.5 mm and maximum position
      error 101.5 mm at time 0:21 (60.0%) Frame 21\n\tRightUpperArm average position
      error 60.5 mm and maximum position error 93.0 mm at time 0:17 (48.6%) Frame
      17\n\tRightLowerArm average position error 31.5 mm and maximum position error
      58.5 mm at time 0: 5 (14.3%) Frame 5\n\tRightHand average position error 44.9
      mm and maximum position error 68.3 mm at time 0:20 (57.1%) Frame 20\n\tHead
      average position error 74.1 mm and maximum position error 118.8 mm at time 0:16
      (45.7%) Frame 16\n\tLeftFoot average orientation error 0.2 deg and maximum orientation
      error 0.5 deg at time 0:16 (45.7%) Frame 16\n\nRetargeting quality report for
      clip 'PistolFromSquatToCombatA':\n\tLeftLowerLeg average position error 43.2
      mm and maximum position error 109.5 mm at time 0: 0 (0.0%) Frame 0\n\tLeftFoot
      average position error 55.4 mm and maximum position error 101.7 mm at time 0:
      6 (17.1%) Frame 6\n\tRightLowerLeg average position error 15.9 mm and maximum
      position error 64.5 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot average position
      error 18.7 mm and maximum position error 54.2 mm at time 1: 5 (100.0%) Frame
      35\n\tLeftUpperArm average position error 84.4 mm and maximum position error
      148.2 mm at time 0:10 (28.6%) Frame 10\n\tLeftLowerArm average position error
      30.9 mm and maximum position error 56.7 mm at time 0: 8 (22.9%) Frame 8\n\tLeftHand
      average position error 28.5 mm and maximum position error 44.2 mm at time 0:
      7 (20.0%) Frame 7\n\tRightUpperArm average position error 84.4 mm and maximum
      position error 148.2 mm at time 0:10 (28.6%) Frame 10\n\tRightLowerArm average
      position error 49.4 mm and maximum position error 113.8 mm at time 0: 7 (20.0%)
      Frame 7\n\tRightHand average position error 53.8 mm and maximum position error
      129.6 mm at time 0: 7 (20.0%) Frame 7\n\tHead average position error 94.7 mm
      and maximum position error 157.0 mm at time 0:10 (28.6%) Frame 10\n\nRetargeting
      quality report for clip 'PistolFromSquatToCombatB':\n\tLeftLowerLeg average
      position error 10.7 mm and maximum position error 14.2 mm at time 0:18 (51.4%)
      Frame 18\n\tLeftFoot average position error 17.6 mm and maximum position error
      29.1 mm at time 0: 0 (0.0%) Frame 0\n\tRightLowerLeg average position error
      31.6 mm and maximum position error 75.7 mm at time 0: 3 (8.6%) Frame 3\n\tRightFoot
      average position error 49.1 mm and maximum position error 88.5 mm at time 0:
      1 (2.9%) Frame 1\n\tLeftUpperArm average position error 86.5 mm and maximum
      position error 157.1 mm at time 0: 5 (14.3%) Frame 5\n\tLeftLowerArm average
      position error 49.9 mm and maximum position error 126.2 mm at time 0: 3 (8.6%)
      Frame 3\n\tLeftHand average position error 55.2 mm and maximum position error
      134.3 mm at time 0: 3 (8.6%) Frame 3\n\tRightUpperArm average position error
      76.3 mm and maximum position error 134.1 mm at time 0: 5 (14.3%) Frame 5\n\tRightLowerArm
      average position error 45.5 mm and maximum position error 97.9 mm at time 0:
      5 (14.3%) Frame 5\n\tRightHand average position error 50.7 mm and maximum position
      error 101.9 mm at time 0: 5 (14.3%) Frame 5\n\tHead average position error 97.9
      mm and maximum position error 166.7 mm at time 0: 5 (14.3%) Frame 5\n\nRetargeting
      quality report for clip 'PistolFromStandToLying':\n\tLeftLowerLeg average position
      error 16.9 mm and maximum position error 33.6 mm at time 0:13 (31.0%) Frame
      13\n\tLeftFoot average position error 35.1 mm and maximum position error 75.3
      mm at time 0:14 (33.3%) Frame 14\n\tRightLowerLeg average position error 39.4
      mm and maximum position error 79.0 mm at time 0:15 (35.7%) Frame 15\n\tRightFoot
      average position error 54.8 mm and maximum position error 89.5 mm at time 0:
      4 (9.5%) Frame 4\n\tLeftUpperArm average position error 102.5 mm and maximum
      position error 201.9 mm at time 0:12 (28.6%) Frame 12\n\tLeftLowerArm average
      position error 78.6 mm and maximum position error 169.5 mm at time 0:11 (26.2%)
      Frame 11\n\tLeftHand average position error 87.4 mm and maximum position error
      172.7 mm at time 0:10 (23.8%) Frame 10\n\tRightUpperArm average position error
      97.4 mm and maximum position error 190.3 mm at time 0:12 (28.6%) Frame 12\n\tRightLowerArm
      average position error 86.0 mm and maximum position error 173.0 mm at time 0:15
      (35.7%) Frame 15\n\tRightHand average position error 93.1 mm and maximum position
      error 189.1 mm at time 0:13 (31.0%) Frame 13\n\tHead average position error
      95.6 mm and maximum position error 194.0 mm at time 0:12 (28.6%) Frame 12\n\nRetargeting
      quality report for clip 'PistolFronSquatToLying':\n\tLeftLowerLeg average position
      error 47.5 mm and maximum position error 109.2 mm at time 0: 1 (2.6%) Frame
      1\n\tLeftFoot average position error 39.8 mm and maximum position error 87.2
      mm at time 0: 0 (0.0%) Frame 0\n\tRightLowerLeg average position error 19.8
      mm and maximum position error 99.7 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot
      average position error 21.1 mm and maximum position error 55.1 mm at time 0:
      0 (0.0%) Frame 0\n\tLeftUpperArm average position error 94.2 mm and maximum
      position error 251.0 mm at time 0:11 (28.9%) Frame 11\n\tLeftLowerArm average
      position error 60.1 mm and maximum position error 153.6 mm at time 0:11 (28.9%)
      Frame 11\n\tLeftHand average position error 61.7 mm and maximum position error
      181.5 mm at time 0:11 (28.9%) Frame 11\n\tRightUpperArm average position error
      94.2 mm and maximum position error 251.0 mm at time 0:11 (28.9%) Frame 11\n\tRightLowerArm
      average position error 72.8 mm and maximum position error 179.1 mm at time 0:12
      (31.6%) Frame 12\n\tRightHand average position error 78.8 mm and maximum position
      error 208.7 mm at time 0:12 (31.6%) Frame 12\n\tHead average position error
      94.6 mm and maximum position error 254.2 mm at time 0:11 (28.9%) Frame 11\n\nRetargeting
      quality report for clip 'PistolLeftFootKick':\n\tLeftLowerLeg average position
      error 19.0 mm and maximum position error 31.9 mm at time 0: 9 (30.0%) Frame
      9\n\tLeftFoot average position error 29.6 mm and maximum position error 52.4
      mm at time 0: 6 (20.0%) Frame 6\n\tRightLowerLeg average position error 32.1
      mm and maximum position error 41.3 mm at time 0:14 (46.7%) Frame 14\n\tRightFoot
      average position error 63.9 mm and maximum position error 77.5 mm at time 0:14
      (46.7%) Frame 14\n\tLeftUpperArm average position error 48.1 mm and maximum
      position error 88.3 mm at time 0:15 (50.0%) Frame 15\n\tLeftLowerArm average
      position error 25.9 mm and maximum position error 55.1 mm at time 0:15 (50.0%)
      Frame 15\n\tLeftHand average position error 17.4 mm and maximum position error
      36.3 mm at time 0:11 (36.7%) Frame 11\n\tRightUpperArm average position error
      48.1 mm and maximum position error 88.3 mm at time 0:15 (50.0%) Frame 15\n\tRightLowerArm
      average position error 35.2 mm and maximum position error 87.0 mm at time 0:15
      (50.0%) Frame 15\n\tRightHand average position error 29.2 mm and maximum position
      error 59.2 mm at time 0:17 (56.7%) Frame 17\n\tHead average position error 57.2
      mm and maximum position error 95.1 mm at time 0:15 (50.0%) Frame 15\n\nRetargeting
      quality report for clip 'PistolLookingAround':\n\tLeftLowerLeg average position
      error 17.1 mm and maximum position error 29.9 mm at time 1:18 (57.1%) Frame
      48\n\tLeftFoot average position error 32.4 mm and maximum position error 58.1
      mm at time 1:18 (57.1%) Frame 48\n\tRightLowerLeg average position error 10.2
      mm and maximum position error 19.5 mm at time 1:20 (59.5%) Frame 50\n\tRightFoot
      average position error 19.5 mm and maximum position error 37.8 mm at time 1:20
      (59.5%) Frame 50\n\tLeftUpperArm average position error 22.4 mm and maximum
      position error 32.7 mm at time 1:21 (60.7%) Frame 51\n\tLeftLowerArm average
      position error 10.9 mm and maximum position error 17.0 mm at time 1:23 (63.1%)
      Frame 53\n\tLeftHand average position error 10.6 mm and maximum position error
      12.7 mm at time 2:24 (100.0%) Frame 84\n\tRightUpperArm average position error
      22.4 mm and maximum position error 32.7 mm at time 1:21 (60.7%) Frame 51\n\tRightLowerArm
      average position error 17.1 mm and maximum position error 29.9 mm at time 1:14
      (52.4%) Frame 44\n\tRightHand average position error 17.3 mm and maximum position
      error 29.2 mm at time 1:13 (51.2%) Frame 43\n\tHead average position error 28.1
      mm and maximum position error 35.5 mm at time 1:18 (57.1%) Frame 48\n\nRetargeting
      quality report for clip 'PistolOneHandedSquat':\n\tLeftLowerLeg average position
      error 61.8 mm and maximum position error 109.0 mm at time 2: 9 (100.0%) Frame
      69\n\tLeftFoot average position error 51.5 mm and maximum position error 87.2
      mm at time 2: 9 (100.0%) Frame 69\n\tRightLowerLeg average position error 69.0
      mm and maximum position error 99.8 mm at time 0:21 (30.4%) Frame 21\n\tRightFoot
      average position error 36.8 mm and maximum position error 55.1 mm at time 2:
      9 (100.0%) Frame 69\n\tLeftUpperArm average position error 42.4 mm and maximum
      position error 54.2 mm at time 2: 9 (100.0%) Frame 69\n\tLeftLowerArm average
      position error 19.9 mm and maximum position error 35.0 mm at time 1: 7 (53.6%)
      Frame 37\n\tLeftHand average position error 15.4 mm and maximum position error
      31.0 mm at time 1:26 (81.2%) Frame 56\n\tRightUpperArm average position error
      42.4 mm and maximum position error 54.2 mm at time 2: 9 (100.0%) Frame 69\n\tRightLowerArm
      average position error 34.3 mm and maximum position error 52.3 mm at time 2:
      9 (100.0%) Frame 69\n\tRightHand average position error 32.6 mm and maximum
      position error 51.6 mm at time 2: 9 (100.0%) Frame 69\n\tHead average position
      error 51.2 mm and maximum position error 61.3 mm at time 2: 9 (100.0%) Frame
      69\n\nRetargeting quality report for clip 'PistolOneHandKneeling':\n\tLeftLowerLeg
      average position error 111.8 mm and maximum position error 133.1 mm at time
      1:25 (64.7%) Frame 55\n\tLeftFoot average position error 91.0 mm and maximum
      position error 113.0 mm at time 1:20 (58.8%) Frame 50\n\tRightLowerLeg average
      position error 101.4 mm and maximum position error 147.4 mm at time 1:15 (52.9%)
      Frame 45\n\tRightFoot average position error 60.1 mm and maximum position error
      86.1 mm at time 1:19 (57.6%) Frame 49\n\tLeftUpperArm average position error
      51.1 mm and maximum position error 55.0 mm at time 0: 7 (8.2%) Frame 7\n\tLeftLowerArm
      average position error 20.1 mm and maximum position error 27.6 mm at time 1:29
      (69.4%) Frame 59\n\tLeftHand average position error 10.2 mm and maximum position
      error 19.0 mm at time 0:22 (25.9%) Frame 22\n\tRightUpperArm average position
      error 51.1 mm and maximum position error 55.0 mm at time 0: 7 (8.2%) Frame 7\n\tRightLowerArm
      average position error 41.7 mm and maximum position error 55.1 mm at time 2:22
      (96.5%) Frame 82\n\tRightHand average position error 38.7 mm and maximum position
      error 53.8 mm at time 2:22 (96.5%) Frame 82\n\tHead average position error 59.7
      mm and maximum position error 61.3 mm at time 0: 7 (8.2%) Frame 7\n\nRetargeting
      quality report for clip 'PistolOneHandShottingStanding':\n\tLeftLowerLeg average
      position error 13.5 mm and maximum position error 16.1 mm at time 0:10 (28.6%)
      Frame 10\n\tLeftFoot average position error 25.0 mm and maximum position error
      30.2 mm at time 0:10 (28.6%) Frame 10\n\tRightLowerLeg average position error
      6.1 mm and maximum position error 8.5 mm at time 0:10 (28.6%) Frame 10\n\tRightFoot
      average position error 10.8 mm and maximum position error 15.8 mm at time 0:
      7 (20.0%) Frame 7\n\tLeftUpperArm average position error 17.8 mm and maximum
      position error 21.0 mm at time 0:10 (28.6%) Frame 10\n\tLeftLowerArm average
      position error 9.6 mm and maximum position error 10.8 mm at time 1: 4 (97.1%)
      Frame 34\n\tLeftHand average position error 12.5 mm and maximum position error
      12.8 mm at time 1: 3 (94.3%) Frame 33\n\tRightUpperArm average position error
      17.8 mm and maximum position error 21.0 mm at time 0: 9 (25.7%) Frame 9\n\tRightLowerArm
      average position error 18.3 mm and maximum position error 20.9 mm at time 0:
      7 (20.0%) Frame 7\n\tRightHand average position error 19.8 mm and maximum position
      error 23.5 mm at time 0: 7 (20.0%) Frame 7\n\tHead average position error 24.4
      mm and maximum position error 27.7 mm at time 0: 9 (25.7%) Frame 9\n\nRetargeting
      quality report for clip 'PistolPuttingGunBack':\n\tLeftLowerLeg average position
      error 4.8 mm and maximum position error 14.0 mm at time 0: 0 (0.0%) Frame 0\n\tLeftFoot
      average position error 7.2 mm and maximum position error 25.4 mm at time 0:
      0 (0.0%) Frame 0\n\tRightLowerLeg average position error 6.8 mm and maximum
      position error 27.3 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot average position
      error 12.7 mm and maximum position error 54.2 mm at time 0: 0 (0.0%) Frame 0\n\tLeftUpperArm
      average position error 24.2 mm and maximum position error 28.6 mm at time 0:18
      (45.0%) Frame 18\n\tLeftLowerArm average position error 9.3 mm and maximum position
      error 15.9 mm at time 0:15 (37.5%) Frame 15\n\tLeftHand average position error
      14.7 mm and maximum position error 17.4 mm at time 0:21 (52.5%) Frame 21\n\tRightUpperArm
      average position error 24.2 mm and maximum position error 28.6 mm at time 0:19
      (47.5%) Frame 19\n\tRightLowerArm average position error 18.7 mm and maximum
      position error 30.3 mm at time 0:23 (57.5%) Frame 23\n\tRightHand average position
      error 13.8 mm and maximum position error 16.6 mm at time 0: 8 (20.0%) Frame
      8\n\tHead average position error 34.1 mm and maximum position error 36.5 mm
      at time 0:18 (45.0%) Frame 18\n\nRetargeting quality report for clip 'PistolReloadOneHandSquat':\n\tLeftLowerLeg
      average position error 62.4 mm and maximum position error 109.0 mm at time 2:10
      (100.0%) Frame 70\n\tLeftFoot average position error 52.0 mm and maximum position
      error 87.2 mm at time 2:10 (100.0%) Frame 70\n\tRightLowerLeg average position
      error 69.4 mm and maximum position error 99.8 mm at time 0:21 (30.0%) Frame
      21\n\tRightFoot average position error 37.0 mm and maximum position error 55.1
      mm at time 2:10 (100.0%) Frame 70\n\tLeftUpperArm average position error 42.6
      mm and maximum position error 54.2 mm at time 2:10 (100.0%) Frame 70\n\tLeftLowerArm
      average position error 20.0 mm and maximum position error 35.0 mm at time 1:
      8 (54.3%) Frame 38\n\tLeftHand average position error 15.3 mm and maximum position
      error 31.0 mm at time 1:27 (81.4%) Frame 57\n\tRightUpperArm average position
      error 42.6 mm and maximum position error 54.2 mm at time 2:10 (100.0%) Frame
      70\n\tRightLowerArm average position error 34.6 mm and maximum position error
      52.3 mm at time 2:10 (100.0%) Frame 70\n\tRightHand average position error 32.8
      mm and maximum position error 51.6 mm at time 2:10 (100.0%) Frame 70\n\tHead
      average position error 51.3 mm and maximum position error 61.3 mm at time 2:10
      (100.0%) Frame 70\n\nRetargeting quality report for clip 'PistolReloadStandingStill':\n\tLeftLowerLeg
      average position error 8.4 mm and maximum position error 15.0 mm at time 0:14
      (20.0%) Frame 14\n\tLeftFoot average position error 15.2 mm and maximum position
      error 27.4 mm at time 0:14 (20.0%) Frame 14\n\tRightLowerLeg average position
      error 14.5 mm and maximum position error 29.6 mm at time 0:14 (20.0%) Frame
      14\n\tRightFoot average position error 28.3 mm and maximum position error 58.7
      mm at time 0:14 (20.0%) Frame 14\n\tLeftUpperArm average position error 27.8
      mm and maximum position error 33.8 mm at time 1:23 (75.7%) Frame 53\n\tLeftLowerArm
      average position error 15.5 mm and maximum position error 29.9 mm at time 1:
      8 (54.3%) Frame 38\n\tLeftHand average position error 16.4 mm and maximum position
      error 28.4 mm at time 1:25 (78.6%) Frame 55\n\tRightUpperArm average position
      error 27.8 mm and maximum position error 33.8 mm at time 1:22 (74.3%) Frame
      52\n\tRightLowerArm average position error 15.2 mm and maximum position error
      21.5 mm at time 1:21 (72.9%) Frame 51\n\tRightHand average position error 18.7
      mm and maximum position error 22.4 mm at time 1:22 (74.3%) Frame 52\n\tHead
      average position error 37.5 mm and maximum position error 46.1 mm at time 1:22
      (74.3%) Frame 52\n\nRetargeting quality report for clip 'PistolReloadTwoHands':\n\tLeftLowerLeg
      average position error 19.7 mm and maximum position error 27.7 mm at time 1:
      0 (43.5%) Frame 30\n\tLeftFoot average position error 40.6 mm and maximum position
      error 55.7 mm at time 1: 0 (43.5%) Frame 30\n\tRightLowerLeg average position
      error 53.0 mm and maximum position error 61.3 mm at time 2: 9 (100.0%) Frame
      69\n\tRightFoot average position error 73.1 mm and maximum position error 87.5
      mm at time 2: 9 (100.0%) Frame 69\n\tLeftUpperArm average position error 106.8
      mm and maximum position error 114.8 mm at time 1:22 (75.4%) Frame 52\n\tLeftLowerArm
      average position error 67.3 mm and maximum position error 106.0 mm at time 2:
      9 (100.0%) Frame 69\n\tLeftHand average position error 60.9 mm and maximum position
      error 102.6 mm at time 2: 8 (98.6%) Frame 68\n\tRightUpperArm average position
      error 118.7 mm and maximum position error 137.3 mm at time 1:22 (75.4%) Frame
      52\n\tRightLowerArm average position error 87.4 mm and maximum position error
      117.6 mm at time 0: 3 (4.3%) Frame 3\n\tRightHand average position error 72.5
      mm and maximum position error 114.5 mm at time 0: 1 (1.4%) Frame 1\n\tHead average
      position error 110.9 mm and maximum position error 121.4 mm at time 2: 9 (100.0%)
      Frame 69\n\nRetargeting quality report for clip 'PistolRightFootkick':\n\tLeftLowerLeg
      average position error 23.4 mm and maximum position error 43.0 mm at time 0:14
      (46.7%) Frame 14\n\tLeftFoot average position error 44.0 mm and maximum position
      error 81.2 mm at time 0:15 (50.0%) Frame 15\n\tRightLowerLeg average position
      error 21.1 mm and maximum position error 31.9 mm at time 0:10 (33.3%) Frame
      10\n\tRightFoot average position error 31.4 mm and maximum position error 54.2
      mm at time 1: 0 (100.0%) Frame 30\n\tLeftUpperArm average position error 46.1
      mm and maximum position error 88.3 mm at time 0:15 (50.0%) Frame 15\n\tLeftLowerArm
      average position error 22.1 mm and maximum position error 60.0 mm at time 0:18
      (60.0%) Frame 18\n\tLeftHand average position error 20.3 mm and maximum position
      error 40.5 mm at time 0:19 (63.3%) Frame 19\n\tRightUpperArm average position
      error 46.1 mm and maximum position error 88.3 mm at time 0:15 (50.0%) Frame
      15\n\tRightLowerArm average position error 29.1 mm and maximum position error
      52.3 mm at time 0:15 (50.0%) Frame 15\n\tRightHand average position error 27.1
      mm and maximum position error 35.1 mm at time 0:11 (36.7%) Frame 11\n\tHead
      average position error 56.7 mm and maximum position error 95.1 mm at time 0:15
      (50.0%) Frame 15\n\nRetargeting quality report for clip 'PistolTakingGunOut':\n\tLeftLowerLeg
      average position error 5.5 mm and maximum position error 14.0 mm at time 1:
      9 (100.0%) Frame 39\n\tLeftFoot average position error 8.7 mm and maximum position
      error 25.4 mm at time 1: 9 (100.0%) Frame 39\n\tRightLowerLeg average position
      error 8.3 mm and maximum position error 27.3 mm at time 1: 9 (100.0%) Frame
      39\n\tRightFoot average position error 15.7 mm and maximum position error 54.2
      mm at time 1: 9 (100.0%) Frame 39\n\tLeftUpperArm average position error 24.9
      mm and maximum position error 28.6 mm at time 0:18 (46.2%) Frame 18\n\tLeftLowerArm
      average position error 12.7 mm and maximum position error 20.4 mm at time 0:17
      (43.6%) Frame 17\n\tLeftHand average position error 15.5 mm and maximum position
      error 19.5 mm at time 0:18 (46.2%) Frame 18\n\tRightUpperArm average position
      error 24.9 mm and maximum position error 28.6 mm at time 0:17 (43.6%) Frame
      17\n\tRightLowerArm average position error 22.4 mm and maximum position error
      47.0 mm at time 0:17 (43.6%) Frame 17\n\tRightHand average position error 14.2
      mm and maximum position error 18.4 mm at time 0:24 (61.5%) Frame 24\n\tHead
      average position error 34.3 mm and maximum position error 36.5 mm at time 0:17
      (43.6%) Frame 17\n\nRetargeting quality report for clip 'PistolTwoHandsLookingAround':\n\tLeftLowerLeg
      average position error 19.1 mm and maximum position error 41.0 mm at time 3:
      9 (81.8%) Frame 99\n\tLeftFoot average position error 36.7 mm and maximum position
      error 80.4 mm at time 3:14 (86.0%) Frame 104\n\tRightLowerLeg average position
      error 29.7 mm and maximum position error 50.1 mm at time 3:12 (84.3%) Frame
      102\n\tRightFoot average position error 59.7 mm and maximum position error 102.2
      mm at time 3:19 (90.1%) Frame 109\n\tLeftUpperArm average position error 42.7
      mm and maximum position error 56.9 mm at time 2:23 (68.6%) Frame 83\n\tLeftLowerArm
      average position error 31.9 mm and maximum position error 48.4 mm at time 0:21
      (17.4%) Frame 21\n\tLeftHand average position error 33.2 mm and maximum position
      error 51.6 mm at time 3:15 (86.8%) Frame 105\n\tRightUpperArm average position
      error 43.1 mm and maximum position error 61.1 mm at time 2:23 (68.6%) Frame
      83\n\tRightLowerArm average position error 30.9 mm and maximum position error
      46.9 mm at time 3: 1 (75.2%) Frame 91\n\tRightHand average position error 27.5
      mm and maximum position error 37.2 mm at time 1:17 (38.8%) Frame 47\n\tHead
      average position error 41.8 mm and maximum position error 52.2 mm at time 3:18
      (89.3%) Frame 108\n\nRetargeting quality report for clip 'PistolTwoHandsLyingShooting':\n\tLeftLowerLeg
      average position error 6.3 mm and maximum position error 6.3 mm at time 0:18
      (81.8%) Frame 18\n\tLeftFoot average position error 12.6 mm and maximum position
      error 12.6 mm at time 0:18 (81.8%) Frame 18\n\tRightLowerLeg average position
      error 3.3 mm and maximum position error 3.3 mm at time 0:18 (81.8%) Frame 18\n\tRightFoot
      average position error 6.5 mm and maximum position error 6.5 mm at time 0:18
      (81.8%) Frame 18\n\tLeftUpperArm average position error 23.0 mm and maximum
      position error 23.0 mm at time 0:22 (100.0%) Frame 22\n\tLeftLowerArm average
      position error 22.9 mm and maximum position error 23.0 mm at time 0: 4 (18.2%)
      Frame 4\n\tLeftHand average position error 22.7 mm and maximum position error
      22.7 mm at time 0: 4 (18.2%) Frame 4\n\tRightUpperArm average position error
      23.0 mm and maximum position error 23.0 mm at time 0:22 (100.0%) Frame 22\n\tRightLowerArm
      average position error 23.0 mm and maximum position error 23.0 mm at time 0:15
      (68.2%) Frame 15\n\tRightHand average position error 22.4 mm and maximum position
      error 22.5 mm at time 0: 4 (18.2%) Frame 4\n\tHead average position error 1.9
      mm and maximum position error 1.9 mm at time 0:22 (100.0%) Frame 22\n\nRetargeting
      quality report for clip 'PistolTwoHandsShooting':\n\tLeftLowerLeg average position
      error 13.5 mm and maximum position error 14.4 mm at time 1: 0 (88.2%) Frame
      30\n\tLeftFoot average position error 29.1 mm and maximum position error 31.5
      mm at time 1: 0 (88.2%) Frame 30\n\tRightLowerLeg average position error 59.6
      mm and maximum position error 61.6 mm at time 0: 4 (11.8%) Frame 4\n\tRightFoot
      average position error 84.8 mm and maximum position error 87.8 mm at time 1:
      0 (88.2%) Frame 30\n\tLeftUpperArm average position error 103.0 mm and maximum
      position error 109.0 mm at time 1: 0 (88.2%) Frame 30\n\tLeftLowerArm average
      position error 100.5 mm and maximum position error 106.9 mm at time 1: 0 (88.2%)
      Frame 30\n\tLeftHand average position error 96.3 mm and maximum position error
      102.3 mm at time 1: 0 (88.2%) Frame 30\n\tRightUpperArm average position error
      109.8 mm and maximum position error 115.2 mm at time 1: 0 (88.2%) Frame 30\n\tRightLowerArm
      average position error 104.6 mm and maximum position error 109.8 mm at time
      1: 0 (88.2%) Frame 30\n\tRightHand average position error 105.4 mm and maximum
      position error 110.2 mm at time 1: 0 (88.2%) Frame 30\n\tHead average position
      error 114.6 mm and maximum position error 122.2 mm at time 1: 0 (88.2%) Frame
      30\n\nRetargeting quality report for clip 'PistolTwoHandsSquatPointing':\n\tLeftLowerLeg
      average position error 25.2 mm and maximum position error 47.5 mm at time 3:
      0 (75.0%) Frame 90\n\tLeftFoot average position error 35.7 mm and maximum position
      error 47.0 mm at time 3: 3 (77.5%) Frame 93\n\tRightLowerLeg average position
      error 60.1 mm and maximum position error 80.4 mm at time 3: 2 (76.7%) Frame
      92\n\tRightFoot average position error 78.7 mm and maximum position error 101.0
      mm at time 3: 7 (80.8%) Frame 97\n\tLeftUpperArm average position error 107.4
      mm and maximum position error 132.2 mm at time 1:28 (48.3%) Frame 58\n\tLeftLowerArm
      average position error 86.7 mm and maximum position error 131.7 mm at time 1:20
      (41.7%) Frame 50\n\tLeftHand average position error 71.4 mm and maximum position
      error 124.9 mm at time 1:14 (36.7%) Frame 44\n\tRightUpperArm average position
      error 113.3 mm and maximum position error 142.5 mm at time 1:26 (46.7%) Frame
      56\n\tRightLowerArm average position error 92.9 mm and maximum position error
      138.2 mm at time 1:28 (48.3%) Frame 58\n\tRightHand average position error 88.8
      mm and maximum position error 142.8 mm at time 1:28 (48.3%) Frame 58\n\tHead
      average position error 109.2 mm and maximum position error 135.7 mm at time
      1:28 (48.3%) Frame 58\n\nRetargeting quality report for clip 'PistolTwoHandsStandingStill':\n\tLeftLowerLeg
      average position error 13.9 mm and maximum position error 14.5 mm at time 1:
      4 (100.0%) Frame 34\n\tLeftFoot average position error 16.4 mm and maximum position
      error 18.1 mm at time 0:18 (52.9%) Frame 18\n\tRightLowerLeg average position
      error 35.6 mm and maximum position error 38.5 mm at time 1: 4 (100.0%) Frame
      34\n\tRightFoot average position error 76.8 mm and maximum position error 83.0
      mm at time 1: 4 (100.0%) Frame 34\n\tLeftUpperArm average position error 75.7
      mm and maximum position error 90.5 mm at time 1: 4 (100.0%) Frame 34\n\tLeftLowerArm
      average position error 73.9 mm and maximum position error 89.2 mm at time 1:
      4 (100.0%) Frame 34\n\tLeftHand average position error 65.3 mm and maximum position
      error 82.1 mm at time 1: 4 (100.0%) Frame 34\n\tRightUpperArm average position
      error 75.7 mm and maximum position error 90.5 mm at time 1: 4 (100.0%) Frame
      34\n\tRightLowerArm average position error 80.9 mm and maximum position error
      94.5 mm at time 1: 4 (100.0%) Frame 34\n\tRightHand average position error 73.9
      mm and maximum position error 89.0 mm at time 1: 4 (100.0%) Frame 34\n\tHead
      average position error 88.3 mm and maximum position error 102.8 mm at time 1:
      4 (100.0%) Frame 34\n\nRetargeting quality report for clip 'TwoHandGunIButtkick':\n\tLeftLowerLeg
      average position error 18.5 mm and maximum position error 31.0 mm at time 0:10
      (40.0%) Frame 10\n\tLeftFoot average position error 37.7 mm and maximum position
      error 63.3 mm at time 0:10 (40.0%) Frame 10\n\tRightLowerLeg average position
      error 24.5 mm and maximum position error 32.5 mm at time 0: 3 (12.0%) Frame
      3\n\tRightFoot average position error 34.3 mm and maximum position error 49.4
      mm at time 0:25 (100.0%) Frame 25\n\tLeftUpperArm average position error 76.0
      mm and maximum position error 83.5 mm at time 0: 9 (36.0%) Frame 9\n\tLeftLowerArm
      average position error 67.1 mm and maximum position error 83.7 mm at time 0:
      8 (32.0%) Frame 8\n\tLeftHand average position error 61.1 mm and maximum position
      error 83.5 mm at time 0: 8 (32.0%) Frame 8\n\tRightUpperArm average position
      error 53.6 mm and maximum position error 84.1 mm at time 0:10 (40.0%) Frame
      10\n\tRightLowerArm average position error 53.4 mm and maximum position error
      85.7 mm at time 0:10 (40.0%) Frame 10\n\tRightHand average position error 46.3
      mm and maximum position error 85.0 mm at time 0: 9 (36.0%) Frame 9\n\tHead average
      position error 69.3 mm and maximum position error 80.5 mm at time 0:10 (40.0%)
      Frame 10\n\nRetargeting quality report for clip 'TwoHandGunIdle':\n\tLeftLowerLeg
      average position error 13.1 mm and maximum position error 13.6 mm at time 1:19
      (100.0%) Frame 49\n\tLeftFoot average position error 21.1 mm and maximum position
      error 21.6 mm at time 0:17 (34.7%) Frame 17\n\tRightLowerLeg average position
      error 32.5 mm and maximum position error 33.3 mm at time 0:27 (55.1%) Frame
      27\n\tRightFoot average position error 50.3 mm and maximum position error 50.9
      mm at time 0:19 (38.8%) Frame 19\n\tLeftUpperArm average position error 68.0
      mm and maximum position error 69.2 mm at time 1:19 (100.0%) Frame 49\n\tLeftLowerArm
      average position error 57.5 mm and maximum position error 58.1 mm at time 1:19
      (100.0%) Frame 49\n\tLeftHand average position error 54.7 mm and maximum position
      error 55.9 mm at time 1:19 (100.0%) Frame 49\n\tRightUpperArm average position
      error 26.9 mm and maximum position error 29.8 mm at time 1:19 (100.0%) Frame
      49\n\tRightLowerArm average position error 30.7 mm and maximum position error
      31.7 mm at time 1:19 (100.0%) Frame 49\n\tRightHand average position error 24.1
      mm and maximum position error 25.8 mm at time 0:18 (36.7%) Frame 18\n\tHead
      average position error 58.8 mm and maximum position error 60.3 mm at time 1:19
      (100.0%) Frame 49\n\nRetargeting quality report for clip 'TwoHandGunIDyinglying':\n\tLeftLowerLeg
      average position error 12.4 mm and maximum position error 13.1 mm at time 0:
      5 (33.3%) Frame 5\n\tLeftFoot average position error 13.5 mm and maximum position
      error 22.7 mm at time 0: 0 (0.0%) Frame 0\n\tRightLowerLeg average position
      error 21.6 mm and maximum position error 27.7 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot
      average position error 39.9 mm and maximum position error 54.6 mm at time 0:
      0 (0.0%) Frame 0\n\tLeftUpperArm average position error 51.5 mm and maximum
      position error 55.9 mm at time 0: 5 (33.3%) Frame 5\n\tLeftLowerArm average
      position error 29.5 mm and maximum position error 30.6 mm at time 0: 5 (33.3%)
      Frame 5\n\tLeftHand average position error 23.3 mm and maximum position error
      26.7 mm at time 0: 3 (20.0%) Frame 3\n\tRightUpperArm average position error
      30.6 mm and maximum position error 35.7 mm at time 0: 5 (33.3%) Frame 5\n\tRightLowerArm
      average position error 34.9 mm and maximum position error 39.9 mm at time 0:
      5 (33.3%) Frame 5\n\tRightHand average position error 17.6 mm and maximum position
      error 21.5 mm at time 0: 5 (33.3%) Frame 5\n\tHead average position error 33.4
      mm and maximum position error 38.1 mm at time 0: 5 (33.3%) Frame 5\n\nRetargeting
      quality report for clip 'HandGunIDyingback':\n\tLeftLowerLeg average position
      error 14.4 mm and maximum position error 29.8 mm at time 0:10 (27.0%) Frame
      10\n\tLeftFoot average position error 26.7 mm and maximum position error 54.7
      mm at time 0:13 (35.1%) Frame 13\n\tRightLowerLeg average position error 9.4
      mm and maximum position error 31.3 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot
      average position error 15.7 mm and maximum position error 47.7 mm at time 0:
      0 (0.0%) Frame 0\n\tLeftUpperArm average position error 55.6 mm and maximum
      position error 140.3 mm at time 0: 5 (13.5%) Frame 5\n\tLeftLowerArm average
      position error 36.6 mm and maximum position error 132.4 mm at time 0: 5 (13.5%)
      Frame 5\n\tLeftHand average position error 50.5 mm and maximum position error
      137.8 mm at time 0: 5 (13.5%) Frame 5\n\tRightUpperArm average position error
      54.6 mm and maximum position error 140.3 mm at time 0: 5 (13.5%) Frame 5\n\tRightLowerArm
      average position error 34.1 mm and maximum position error 99.6 mm at time 0:
      7 (18.9%) Frame 7\n\tRightHand average position error 43.3 mm and maximum position
      error 109.4 mm at time 0: 7 (18.9%) Frame 7\n\tHead average position error 73.9
      mm and maximum position error 153.7 mm at time 0: 5 (13.5%) Frame 5\n\nRetargeting
      quality report for clip 'TwoHandGunIDyingsquat':\n\tLeftLowerLeg average position
      error 39.2 mm and maximum position error 57.4 mm at time 0:13 (39.4%) Frame
      13\n\tLeftFoot average position error 31.9 mm and maximum position error 39.1
      mm at time 0:10 (30.3%) Frame 10\n\tRightLowerLeg average position error 12.8
      mm and maximum position error 34.6 mm at time 1: 3 (100.0%) Frame 33\n\tRightFoot
      average position error 18.6 mm and maximum position error 47.9 mm at time 1:
      3 (100.0%) Frame 33\n\tLeftUpperArm average position error 41.3 mm and maximum
      position error 67.4 mm at time 0: 7 (21.2%) Frame 7\n\tLeftLowerArm average
      position error 30.5 mm and maximum position error 48.4 mm at time 0:11 (33.3%)
      Frame 11\n\tLeftHand average position error 35.8 mm and maximum position error
      53.8 mm at time 0:10 (30.3%) Frame 10\n\tRightUpperArm average position error
      55.9 mm and maximum position error 93.5 mm at time 0: 7 (21.2%) Frame 7\n\tRightLowerArm
      average position error 42.6 mm and maximum position error 62.0 mm at time 0:14
      (42.4%) Frame 14\n\tRightHand average position error 39.7 mm and maximum position
      error 57.5 mm at time 0: 6 (18.2%) Frame 6\n\tHead average position error 50.6
      mm and maximum position error 87.0 mm at time 0: 7 (21.2%) Frame 7\n\nRetargeting
      quality report for clip 'TwoHandGunIFirelying':\n\tLeftLowerLeg average position
      error 14.1 mm and maximum position error 14.1 mm at time 0: 2 (13.3%) Frame
      2\n\tLeftFoot average position error 27.2 mm and maximum position error 27.2
      mm at time 0: 2 (13.3%) Frame 2\n\tRightLowerLeg average position error 29.9
      mm and maximum position error 29.9 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot
      average position error 59.3 mm and maximum position error 59.3 mm at time 0:
      1 (6.7%) Frame 1\n\tLeftUpperArm average position error 27.0 mm and maximum
      position error 27.0 mm at time 0:14 (93.3%) Frame 14\n\tLeftLowerArm average
      position error 23.6 mm and maximum position error 23.7 mm at time 0:14 (93.3%)
      Frame 14\n\tLeftHand average position error 20.8 mm and maximum position error
      20.8 mm at time 0:14 (93.3%) Frame 14\n\tRightUpperArm average position error
      6.8 mm and maximum position error 6.8 mm at time 0: 5 (33.3%) Frame 5\n\tRightLowerArm
      average position error 13.0 mm and maximum position error 13.4 mm at time 0:
      1 (6.7%) Frame 1\n\tRightHand average position error 9.6 mm and maximum position
      error 9.7 mm at time 0: 0 (0.0%) Frame 0\n\tHead average position error 8.6
      mm and maximum position error 8.6 mm at time 0: 2 (13.3%) Frame 2\n\nRetargeting
      quality report for clip 'TwoHandGunIFiresquat':\n\tLeftLowerLeg average position
      error 21.9 mm and maximum position error 21.9 mm at time 0: 0 (0.0%) Frame 0\n\tLeftFoot
      average position error 23.7 mm and maximum position error 23.7 mm at time 0:
      9 (81.8%) Frame 9\n\tRightLowerLeg average position error 18.7 mm and maximum
      position error 18.7 mm at time 0: 6 (54.5%) Frame 6\n\tRightFoot average position
      error 36.7 mm and maximum position error 36.7 mm at time 0: 8 (72.7%) Frame
      8\n\tLeftUpperArm average position error 18.0 mm and maximum position error
      18.3 mm at time 0: 3 (27.3%) Frame 3\n\tLeftLowerArm average position error
      17.7 mm and maximum position error 18.3 mm at time 0: 1 (9.1%) Frame 1\n\tLeftHand
      average position error 16.0 mm and maximum position error 16.5 mm at time 0:
      1 (9.1%) Frame 1\n\tRightUpperArm average position error 23.8 mm and maximum
      position error 24.1 mm at time 0: 3 (27.3%) Frame 3\n\tRightLowerArm average
      position error 21.4 mm and maximum position error 21.8 mm at time 0: 3 (27.3%)
      Frame 3\n\tRightHand average position error 24.9 mm and maximum position error
      25.3 mm at time 0: 3 (27.3%) Frame 3\n\tHead average position error 13.3 mm
      and maximum position error 13.3 mm at time 0:11 (100.0%) Frame 11\n\nRetargeting
      quality report for clip 'TwoHandGunIFirestanding':\n\tLeftLowerLeg average position
      error 21.1 mm and maximum position error 24.3 mm at time 0:20 (51.3%) Frame
      20\n\tLeftFoot average position error 27.3 mm and maximum position error 33.0
      mm at time 0:20 (51.3%) Frame 20\n\tRightLowerLeg average position error 31.1
      mm and maximum position error 34.6 mm at time 0:14 (35.9%) Frame 14\n\tRightFoot
      average position error 49.2 mm and maximum position error 56.0 mm at time 0:14
      (35.9%) Frame 14\n\tLeftUpperArm average position error 44.4 mm and maximum
      position error 60.9 mm at time 0:12 (30.8%) Frame 12\n\tLeftLowerArm average
      position error 56.2 mm and maximum position error 69.9 mm at time 0:11 (28.2%)
      Frame 11\n\tLeftHand average position error 48.5 mm and maximum position error
      60.1 mm at time 0:12 (30.8%) Frame 12\n\tRightUpperArm average position error
      26.6 mm and maximum position error 31.2 mm at time 0:14 (35.9%) Frame 14\n\tRightLowerArm
      average position error 15.9 mm and maximum position error 29.9 mm at time 0:20
      (51.3%) Frame 20\n\tRightHand average position error 12.9 mm and maximum position
      error 24.5 mm at time 0:11 (28.2%) Frame 11\n\tHead average position error 36.9
      mm and maximum position error 49.7 mm at time 0:11 (28.2%) Frame 11\n\nRetargeting
      quality report for clip 'TwoHandGunIFireReadysquat':\n\tLeftLowerLeg average
      position error 15.8 mm and maximum position error 21.9 mm at time 0:10 (66.7%)
      Frame 10\n\tLeftFoot average position error 21.3 mm and maximum position error
      23.7 mm at time 0:11 (73.3%) Frame 11\n\tRightLowerLeg average position error
      27.0 mm and maximum position error 43.8 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot
      average position error 43.5 mm and maximum position error 55.0 mm at time 0:
      0 (0.0%) Frame 0\n\tLeftUpperArm average position error 41.1 mm and maximum
      position error 109.2 mm at time 0: 0 (0.0%) Frame 0\n\tLeftLowerArm average
      position error 39.9 mm and maximum position error 102.9 mm at time 0: 0 (0.0%)
      Frame 0\n\tLeftHand average position error 38.3 mm and maximum position error
      94.1 mm at time 0: 0 (0.0%) Frame 0\n\tRightUpperArm average position error
      38.0 mm and maximum position error 97.6 mm at time 0: 0 (0.0%) Frame 0\n\tRightLowerArm
      average position error 19.2 mm and maximum position error 33.8 mm at time 0:
      0 (0.0%) Frame 0\n\tRightHand average position error 24.1 mm and maximum position
      error 33.8 mm at time 0: 1 (6.7%) Frame 1\n\tHead average position error 37.0
      mm and maximum position error 104.2 mm at time 0: 0 (0.0%) Frame 0\n\nRetargeting
      quality report for clip 'TwoHandGunIFireReadystanding':\n\tLeftLowerLeg average
      position error 15.2 mm and maximum position error 20.2 mm at time 0: 9 (100.0%)
      Frame 9\n\tLeftFoot average position error 20.6 mm and maximum position error
      25.6 mm at time 0: 9 (100.0%) Frame 9\n\tRightLowerLeg average position error
      32.4 mm and maximum position error 33.0 mm at time 0: 3 (33.3%) Frame 3\n\tRightFoot
      average position error 51.6 mm and maximum position error 52.7 mm at time 0:
      3 (33.3%) Frame 3\n\tLeftUpperArm average position error 71.1 mm and maximum
      position error 84.9 mm at time 0: 3 (33.3%) Frame 3\n\tLeftLowerArm average
      position error 76.6 mm and maximum position error 90.3 mm at time 0: 3 (33.3%)
      Frame 3\n\tLeftHand average position error 70.6 mm and maximum position error
      84.3 mm at time 0: 3 (33.3%) Frame 3\n\tRightUpperArm average position error
      34.2 mm and maximum position error 44.8 mm at time 0: 3 (33.3%) Frame 3\n\tRightLowerArm
      average position error 13.5 mm and maximum position error 23.3 mm at time 0:
      0 (0.0%) Frame 0\n\tRightHand average position error 25.3 mm and maximum position
      error 35.8 mm at time 0: 3 (33.3%) Frame 3\n\tHead average position error 60.0
      mm and maximum position error 73.2 mm at time 0: 3 (33.3%) Frame 3\n\nRetargeting
      quality report for clip 'toyingdGunIFromStanding':\n\tLeftLowerLeg average position
      error 22.3 mm and maximum position error 51.5 mm at time 0:22 (61.1%) Frame
      22\n\tLeftFoot average position error 31.5 mm and maximum position error 56.4
      mm at time 0:22 (61.1%) Frame 22\n\tRightLowerLeg average position error 37.8
      mm and maximum position error 81.5 mm at time 0:21 (58.3%) Frame 21\n\tRightFoot
      average position error 60.6 mm and maximum position error 103.4 mm at time 0:21
      (58.3%) Frame 21\n\tLeftUpperArm average position error 109.3 mm and maximum
      position error 244.3 mm at time 0:22 (61.1%) Frame 22\n\tLeftLowerArm average
      position error 89.9 mm and maximum position error 200.3 mm at time 0:23 (63.9%)
      Frame 23\n\tLeftHand average position error 91.8 mm and maximum position error
      208.3 mm at time 0:21 (58.3%) Frame 21\n\tRightUpperArm average position error
      95.3 mm and maximum position error 238.7 mm at time 0:22 (61.1%) Frame 22\n\tRightLowerArm
      average position error 73.8 mm and maximum position error 220.4 mm at time 0:22
      (61.1%) Frame 22\n\tRightHand average position error 83.4 mm and maximum position
      error 235.0 mm at time 0:22 (61.1%) Frame 22\n\tHead average position error
      100.7 mm and maximum position error 240.3 mm at time 0:22 (61.1%) Frame 22\n\nRetargeting
      quality report for clip 'touatndGunIFromstanding':\n\tLeftLowerLeg average position
      error 11.1 mm and maximum position error 16.9 mm at time 0: 0 (0.0%) Frame 0\n\tLeftFoot
      average position error 14.1 mm and maximum position error 20.5 mm at time 0:26
      (100.0%) Frame 26\n\tRightLowerLeg average position error 37.1 mm and maximum
      position error 46.0 mm at time 0: 2 (7.7%) Frame 2\n\tRightFoot average position
      error 58.0 mm and maximum position error 65.7 mm at time 0:13 (50.0%) Frame
      13\n\tLeftUpperArm average position error 103.5 mm and maximum position error
      122.6 mm at time 0: 3 (11.5%) Frame 3\n\tLeftLowerArm average position error
      85.6 mm and maximum position error 108.7 mm at time 0: 0 (0.0%) Frame 0\n\tLeftHand
      average position error 82.2 mm and maximum position error 98.8 mm at time 0:
      0 (0.0%) Frame 0\n\tRightUpperArm average position error 78.6 mm and maximum
      position error 109.6 mm at time 0: 0 (0.0%) Frame 0\n\tRightLowerArm average
      position error 30.9 mm and maximum position error 42.6 mm at time 0: 0 (0.0%)
      Frame 0\n\tRightHand average position error 28.2 mm and maximum position error
      35.3 mm at time 0:14 (53.8%) Frame 14\n\tHead average position error 95.4 mm
      and maximum position error 116.8 mm at time 0: 2 (7.7%) Frame 2\n\nRetargeting
      quality report for clip 'TwoHandGunIFromLayingtoSquat':\n\tLeftLowerLeg average
      position error 47.7 mm and maximum position error 114.4 mm at time 0:16 (41.0%)
      Frame 16\n\tLeftFoot average position error 36.8 mm and maximum position error
      87.1 mm at time 0:16 (41.0%) Frame 16\n\tRightLowerLeg average position error
      23.5 mm and maximum position error 45.2 mm at time 1: 9 (100.0%) Frame 39\n\tRightFoot
      average position error 25.5 mm and maximum position error 55.6 mm at time 1:
      9 (100.0%) Frame 39\n\tLeftUpperArm average position error 123.6 mm and maximum
      position error 234.4 mm at time 0:18 (46.2%) Frame 18\n\tLeftLowerArm average
      position error 93.4 mm and maximum position error 179.4 mm at time 0:18 (46.2%)
      Frame 18\n\tLeftHand average position error 101.8 mm and maximum position error
      177.1 mm at time 0:18 (46.2%) Frame 18\n\tRightUpperArm average position error
      122.1 mm and maximum position error 234.8 mm at time 0:18 (46.2%) Frame 18\n\tRightLowerArm
      average position error 61.1 mm and maximum position error 144.3 mm at time 0:18
      (46.2%) Frame 18\n\tRightHand average position error 65.3 mm and maximum position
      error 143.2 mm at time 0:19 (48.7%) Frame 19\n\tHead average position error
      128.1 mm and maximum position error 242.2 mm at time 0:18 (46.2%) Frame 18\n\nRetargeting
      quality report for clip 'TwoHandGunIFromSquattoLaying':\n\tLeftLowerLeg average
      position error 38.7 mm and maximum position error 107.8 mm at time 0:12 (30.8%)
      Frame 12\n\tLeftFoot average position error 33.9 mm and maximum position error
      65.0 mm at time 0:11 (28.2%) Frame 11\n\tRightLowerLeg average position error
      19.8 mm and maximum position error 41.6 mm at time 0: 0 (0.0%) Frame 0\n\tRightFoot
      average position error 28.9 mm and maximum position error 59.3 mm at time 1:
      9 (100.0%) Frame 39\n\tLeftUpperArm average position error 90.3 mm and maximum
      position error 223.1 mm at time 0:11 (28.2%) Frame 11\n\tLeftLowerArm average
      position error 70.0 mm and maximum position error 154.4 mm at time 0: 9 (23.1%)
      Frame 9\n\tLeftHand average position error 73.7 mm and maximum position error
      171.6 mm at time 0:12 (30.8%) Frame 12\n\tRightUpperArm average position error
      90.7 mm and maximum position error 223.4 mm at time 0:11 (28.2%) Frame 11\n\tRightLowerArm
      average position error 57.8 mm and maximum position error 153.7 mm at time 0:12
      (30.8%) Frame 12\n\tRightHand average position error 62.4 mm and maximum position
      error 177.8 mm at time 0:12 (30.8%) Frame 12\n\tHead average position error
      91.2 mm and maximum position error 230.2 mm at time 0:11 (28.2%) Frame 11\n\nRetargeting
      quality report for clip 'TwoHandGunIGrenadethrow':\n\tLeftLowerLeg average position
      error 27.6 mm and maximum position error 57.2 mm at time 0:14 (35.0%) Frame
      14\n\tLeftFoot average position error 49.1 mm and maximum position error 108.8
      mm at time 0:13 (32.5%) Frame 13\n\tRightLowerLeg average position error 33.6
      mm and maximum position error 50.2 mm at time 0:25 (62.5%) Frame 25\n\tRightFoot
      average position error 59.5 mm and maximum position error 92.4 mm at time 0:24
      (60.0%) Frame 24\n\tLeftUpperArm average position error 83.0 mm and maximum
      position error 132.5 mm at time 0:21 (52.5%) Frame 21\n\tLeftLowerArm average
      position error 61.2 mm and maximum position error 117.8 mm at time 0:21 (52.5%)
      Frame 21\n\tLeftHand average position error 54.5 mm and maximum position error
      119.8 mm at time 0:21 (52.5%) Frame 21\n\tRightUpperArm average position error
      53.8 mm and maximum position error 98.2 mm at time 0:21 (52.5%) Frame 21\n\tRightLowerArm
      average position error 27.3 mm and maximum position error 33.2 mm at time 0:18
      (45.0%) Frame 18\n\tRightHand average position error 26.4 mm and maximum position
      error 43.2 mm at time 0:18 (45.0%) Frame 18\n\tHead average position error 75.1
      mm and maximum position error 125.1 mm at time 0:21 (52.5%) Frame 21\n\nRetargeting
      quality report for clip 'TwoHandGunIJump':\n\tLeftLowerLeg average position
      error 27.4 mm and maximum position error 69.6 mm at time 0:16 (35.6%) Frame
      16\n\tLeftFoot average position error 34.3 mm and maximum position error 68.8
      mm at time 0:15 (33.3%) Frame 15\n\tRightLowerLeg average position error 38.6
      mm and maximum position error 74.0 mm at time 0:24 (53.3%) Frame 24\n\tRightFoot
      average position error 58.4 mm and maximum position error 100.5 mm at time 0:24
      (53.3%) Frame 24\n\tLeftUpperArm average position error 52.7 mm and maximum
      position error 148.5 mm at time 1: 1 (68.9%) Frame 31\n\tLeftLowerArm average
      position error 24.1 mm and maximum position error 75.3 mm at time 1: 0 (66.7%)
      Frame 30\n\tLeftHand average position error 29.9 mm and maximum position error
      97.6 mm at time 1: 0 (66.7%) Frame 30\n\tRightUpperArm average position error
      52.7 mm and maximum position error 148.5 mm at time 1: 1 (68.9%) Frame 31\n\tRightLowerArm
      average position error 24.6 mm and maximum position error 85.7 mm at time 1:
      1 (68.9%) Frame 31\n\tRightHand average position error 31.3 mm and maximum position
      error 104.5 mm at time 1: 1 (68.9%) Frame 31\n\tHead average position error
      65.3 mm and maximum position error 163.0 mm at time 1: 1 (68.9%) Frame 31\n\nRetargeting
      quality report for clip 'TwoHandGunILookingAroundSquatA':\n\tLeftLowerLeg average
      position error 27.9 mm and maximum position error 63.3 mm at time 2:16 (58.9%)
      Frame 76\n\tLeftFoot average position error 27.6 mm and maximum position error
      48.9 mm at time 2:16 (58.9%) Frame 76\n\tRightLowerLeg average position error
      36.3 mm and maximum position error 45.2 mm at time 4: 8 (99.2%) Frame 128\n\tRightFoot
      average position error 51.3 mm and maximum position error 57.5 mm at time 3:
      0 (69.8%) Frame 90\n\tLeftUpperArm average position error 62.4 mm and maximum
      position error 136.7 mm at time 3:19 (84.5%) Frame 109\n\tLeftLowerArm average
      position error 57.9 mm and maximum position error 120.9 mm at time 3:20 (85.3%)
      Frame 110\n\tLeftHand average position error 52.1 mm and maximum position error
      118.3 mm at time 3:19 (84.5%) Frame 109\n\tRightUpperArm average position error
      53.1 mm and maximum position error 121.0 mm at time 3:19 (84.5%) Frame 109\n\tRightLowerArm
      average position error 31.1 mm and maximum position error 47.1 mm at time 3:19
      (84.5%) Frame 109\n\tRightHand average position error 23.4 mm and maximum position
      error 30.1 mm at time 3: 7 (75.2%) Frame 97\n\tHead average position error 53.0
      mm and maximum position error 128.4 mm at time 3:19 (84.5%) Frame 109\n\nRetargeting
      quality report for clip 'TwoHandGunIMovingleft':\n\tLeftLowerLeg average position
      error 15.5 mm and maximum position error 16.6 mm at time 0:10 (50.0%) Frame
      10\n\tLeftFoot average position error 23.8 mm and maximum position error 25.5
      mm at time 0: 9 (45.0%) Frame 9\n\tRightLowerLeg average position error 33.8
      mm and maximum position error 35.9 mm at time 0:15 (75.0%) Frame 15\n\tRightFoot
      average position error 50.5 mm and maximum position error 51.7 mm at time 0:10
      (50.0%) Frame 10\n\tLeftUpperArm average position error 69.2 mm and maximum
      position error 69.2 mm at time 0:18 (90.0%) Frame 18\n\tLeftLowerArm average
      position error 59.2 mm and maximum position error 59.4 mm at time 0:11 (55.0%)
      Frame 11\n\tLeftHand average position error 54.7 mm and maximum position error
      56.0 mm at time 0:20 (100.0%) Frame 20\n\tRightUpperArm average position error
      29.8 mm and maximum position error 29.8 mm at time 0:17 (85.0%) Frame 17\n\tRightLowerArm
      average position error 38.1 mm and maximum position error 43.5 mm at time 0:
      8 (40.0%) Frame 8\n\tRightHand average position error 23.4 mm and maximum position
      error 24.8 mm at time 0: 8 (40.0%) Frame 8\n\tHead average position error 60.3
      mm and maximum position error 60.3 mm at time 0:15 (75.0%) Frame 15\n\nRetargeting
      quality report for clip 'TwoHandGunIMovingright':\n\tLeftLowerLeg average position
      error 15.5 mm and maximum position error 16.7 mm at time 0:10 (52.6%) Frame
      10\n\tLeftFoot average position error 24.0 mm and maximum position error 25.8
      mm at time 0:10 (52.6%) Frame 10\n\tRightLowerLeg average position error 36.8
      mm and maximum position error 41.3 mm at time 0: 4 (21.1%) Frame 4\n\tRightFoot
      average position error 50.2 mm and maximum position error 51.4 mm at time 0:10
      (52.6%) Frame 10\n\tLeftUpperArm average position error 69.2 mm and maximum
      position error 69.2 mm at time 0: 3 (15.8%) Frame 3\n\tLeftLowerArm average
      position error 60.6 mm and maximum position error 61.1 mm at time 0: 7 (36.8%)
      Frame 7\n\tLeftHand average position error 55.9 mm and maximum position error
      56.0 mm at time 0: 0 (0.0%) Frame 0\n\tRightUpperArm average position error
      29.8 mm and maximum position error 29.8 mm at time 0:10 (52.6%) Frame 10\n\tRightLowerArm
      average position error 35.1 mm and maximum position error 37.4 mm at time 0:
      7 (36.8%) Frame 7\n\tRightHand average position error 22.4 mm and maximum position
      error 22.8 mm at time 0: 7 (36.8%) Frame 7\n\tHead average position error 60.3
      mm and maximum position error 60.3 mm at time 0:14 (73.7%) Frame 14\n\nRetargeting
      quality report for clip 'TwoHandGunIReloadsquat':\n\tLeftLowerLeg average position
      error 16.9 mm and maximum position error 22.9 mm at time 0:18 (24.7%) Frame
      18\n\tLeftFoot average position error 21.7 mm and maximum position error 24.0
      mm at time 0:18 (24.7%) Frame 18\n\tRightLowerLeg average position error 22.8
      mm and maximum position error 47.5 mm at time 1:12 (57.5%) Frame 42\n\tRightFoot
      average position error 31.6 mm and maximum position error 37.1 mm at time 0:18
      (24.7%) Frame 18\n\tLeftUpperArm average position error 16.1 mm and maximum
      position error 25.6 mm at time 1:13 (58.9%) Frame 43\n\tLeftLowerArm average
      position error 13.9 mm and maximum position error 25.2 mm at time 1:11 (56.2%)
      Frame 41\n\tLeftHand average position error 14.4 mm and maximum position error
      28.8 mm at time 1:10 (54.8%) Frame 40\n\tRightUpperArm average position error
      26.3 mm and maximum position error 38.4 mm at time 1:12 (57.5%) Frame 42\n\tRightLowerArm
      average position error 18.9 mm and maximum position error 30.9 mm at time 1:
      2 (43.8%) Frame 32\n\tRightHand average position error 23.4 mm and maximum position
      error 35.5 mm at time 1: 1 (42.5%) Frame 31\n\tHead average position error 16.3
      mm and maximum position error 31.6 mm at time 1:12 (57.5%) Frame 42\n\nRetargeting
      quality report for clip 'TwoHandGunIReloadstanding':\n\tLeftLowerLeg average
      position error 26.6 mm and maximum position error 32.0 mm at time 0:20 (26.7%)
      Frame 20\n\tLeftFoot average position error 36.7 mm and maximum position error
      46.4 mm at time 0:20 (26.7%) Frame 20\n\tRightLowerLeg average position error
      19.4 mm and maximum position error 31.6 mm at time 2:15 (100.0%) Frame 75\n\tRightFoot
      average position error 25.8 mm and maximum position error 50.5 mm at time 2:15
      (100.0%) Frame 75\n\tLeftUpperArm average position error 22.3 mm and maximum
      position error 48.9 mm at time 2:15 (100.0%) Frame 75\n\tLeftLowerArm average
      position error 26.0 mm and maximum position error 60.0 mm at time 2:15 (100.0%)
      Frame 75\n\tLeftHand average position error 26.3 mm and maximum position error
      53.0 mm at time 2:15 (100.0%) Frame 75\n\tRightUpperArm average position error
      25.2 mm and maximum position error 40.1 mm at time 1:12 (56.0%) Frame 42\n\tRightLowerArm
      average position error 26.4 mm and maximum position error 32.3 mm at time 1:
      1 (41.3%) Frame 31\n\tRightHand average position error 25.8 mm and maximum position
      error 37.1 mm at time 1: 1 (41.3%) Frame 31\n\tHead average position error 20.0
      mm and maximum position error 39.3 mm at time 2:15 (100.0%) Frame 75\n\nRetargeting
      quality report for clip 'TwoHandGunIRun':\n\tLeftLowerLeg average position error
      6.8 mm and maximum position error 14.7 mm at time 0: 8 (36.4%) Frame 8\n\tLeftFoot
      average position error 11.1 mm and maximum position error 21.4 mm at time 0:
      8 (36.4%) Frame 8\n\tRightLowerLeg average position error 26.0 mm and maximum
      position error 44.4 mm at time 0:22 (100.0%) Frame 22\n\tRightFoot average position
      error 40.5 mm and maximum position error 49.2 mm at time 0: 6 (27.3%) Frame
      6\n\tLeftUpperArm average position error 50.0 mm and maximum position error
      62.9 mm at time 0: 7 (31.8%) Frame 7\n\tLeftLowerArm average position error
      35.6 mm and maximum position error 44.6 mm at time 0: 7 (31.8%) Frame 7\n\tLeftHand
      average position error 34.3 mm and maximum position error 44.1 mm at time 0:
      6 (27.3%) Frame 6\n\tRightUpperArm average position error 32.2 mm and maximum
      position error 42.3 mm at time 0: 7 (31.8%) Frame 7\n\tRightLowerArm average
      position error 20.8 mm and maximum position error 25.5 mm at time 0:22 (100.0%)
      Frame 22\n\tRightHand average position error 29.3 mm and maximum position error
      35.6 mm at time 0: 6 (27.3%) Frame 6\n\tHead average position error 56.7 mm
      and maximum position error 67.7 mm at time 0: 7 (31.8%) Frame 7\n\nRetargeting
      quality report for clip 'toandingunITwoHandGunIFromsquat':\n\tLeftLowerLeg average
      position error 11.0 mm and maximum position error 17.0 mm at time 0:24 (100.0%)
      Frame 24\n\tLeftFoot average position error 13.6 mm and maximum position error
      22.2 mm at time 0: 1 (4.2%) Frame 1\n\tRightLowerLeg average position error
      38.9 mm and maximum position error 48.1 mm at time 0:19 (79.2%) Frame 19\n\tRightFoot
      average position error 55.5 mm and maximum position error 62.8 mm at time 0:15
      (62.5%) Frame 15\n\tLeftUpperArm average position error 108.5 mm and maximum
      position error 122.0 mm at time 0:21 (87.5%) Frame 21\n\tLeftLowerArm average
      position error 88.4 mm and maximum position error 112.1 mm at time 0:24 (100.0%)
      Frame 24\n\tLeftHand average position error 82.5 mm and maximum position error
      99.5 mm at time 0:24 (100.0%) Frame 24\n\tRightUpperArm average position error
      88.2 mm and maximum position error 110.2 mm at time 0:21 (87.5%) Frame 21\n\tRightLowerArm
      average position error 35.8 mm and maximum position error 43.5 mm at time 0:24
      (100.0%) Frame 24\n\tRightHand average position error 23.0 mm and maximum position
      error 27.4 mm at time 0:19 (79.2%) Frame 19\n\tHead average position error 98.9
      mm and maximum position error 116.4 mm at time 0:21 (87.5%) Frame 21\n\nRetargeting
      quality report for clip 'TwoHandGunIWalking':\n\tLeftLowerLeg average position
      error 8.3 mm and maximum position error 16.3 mm at time 0: 4 (11.1%) Frame 4\n\tLeftFoot
      average position error 11.2 mm and maximum position error 18.9 mm at time 0:
      4 (11.1%) Frame 4\n\tRightLowerLeg average position error 18.1 mm and maximum
      position error 26.3 mm at time 0: 4 (11.1%) Frame 4\n\tRightFoot average position
      error 32.7 mm and maximum position error 38.8 mm at time 0: 1 (2.8%) Frame 1\n\tLeftUpperArm
      average position error 10.8 mm and maximum position error 12.8 mm at time 0:15
      (41.7%) Frame 15\n\tLeftLowerArm average position error 7.6 mm and maximum position
      error 12.5 mm at time 0:22 (61.1%) Frame 22\n\tLeftHand average position error
      10.6 mm and maximum position error 15.9 mm at time 0:22 (61.1%) Frame 22\n\tRightUpperArm
      average position error 10.8 mm and maximum position error 12.8 mm at time 0:15
      (41.7%) Frame 15\n\tRightLowerArm average position error 17.2 mm and maximum
      position error 21.1 mm at time 0:22 (61.1%) Frame 22\n\tRightHand average position
      error 15.5 mm and maximum position error 22.5 mm at time 0:22 (61.1%) Frame
      22\n\tHead average position error 22.3 mm and maximum position error 23.0 mm
      at time 0:15 (41.7%) Frame 15\n\nRetargeting quality report for clip 'TwoHandGunIWalkingback':\n\tLeftLowerLeg
      average position error 11.6 mm and maximum position error 19.3 mm at time 0:29
      (82.9%) Frame 29\n\tLeftFoot average position error 18.3 mm and maximum position
      error 21.8 mm at time 0:13 (37.1%) Frame 13\n\tRightLowerLeg average position
      error 20.7 mm and maximum position error 25.1 mm at time 0:29 (82.9%) Frame
      29\n\tRightFoot average position error 36.9 mm and maximum position error 43.6
      mm at time 0:12 (34.3%) Frame 12\n\tLeftUpperArm average position error 12.0
      mm and maximum position error 17.6 mm at time 0:26 (74.3%) Frame 26\n\tLeftLowerArm
      average position error 9.1 mm and maximum position error 14.8 mm at time 0:14
      (40.0%) Frame 14\n\tLeftHand average position error 10.9 mm and maximum position
      error 16.8 mm at time 0:14 (40.0%) Frame 14\n\tRightUpperArm average position
      error 12.0 mm and maximum position error 17.6 mm at time 0:26 (74.3%) Frame
      26\n\tRightLowerArm average position error 18.4 mm and maximum position error
      22.5 mm at time 0:12 (34.3%) Frame 12\n\tRightHand average position error 15.4
      mm and maximum position error 23.0 mm at time 0:13 (37.1%) Frame 13\n\tHead
      average position error 17.2 mm and maximum position error 25.7 mm at time 0:26
      (74.3%) Frame 26\n"
    animationDoRetargetingWarnings: 1
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 2
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Animation
      takeName: Animation
      firstFrame: 0
      lastFrame: 4
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: NPcUseObject
      takeName: NPcUseObject
      firstFrame: 0
      lastFrame: 39
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolCombatIdle
      takeName: PistolCombatIdle
      firstFrame: 0
      lastFrame: 80
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFromCombatToSquatA
      takeName: PistolFromCombatToSquatA
      firstFrame: 0
      lastFrame: 31
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFromCombatToSquatB
      takeName: PistolFromCombatToSquatB
      firstFrame: 0
      lastFrame: 45.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFromLyingToSquat
      takeName: PistolFromLyingToSquat
      firstFrame: 0
      lastFrame: 38.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFromLyingToStand
      takeName: PistolFromLyingToStand
      firstFrame: 0
      lastFrame: 35.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFromSquatToCombatA
      takeName: PistolFromSquatToCombatA
      firstFrame: 0
      lastFrame: 35.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFromSquatToCombatB
      takeName: PistolFromSquatToCombatB
      firstFrame: 0
      lastFrame: 35.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFromStandToLying
      takeName: PistolFromStandToLying
      firstFrame: 0
      lastFrame: 42.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolFronSquatToLying
      takeName: PistolFronSquatToLying
      firstFrame: 0
      lastFrame: 38.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolLeftFootKick
      takeName: PistolLeftFootKick
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolLookingAround
      takeName: PistolLookingAround
      firstFrame: 0
      lastFrame: 84
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolReloadOneHandedSquat
      takeName: PistolOneHandedSquat
      firstFrame: 0
      lastFrame: 69.00001
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolOneHandLookingAroundKneeling
      takeName: PistolOneHandKneeling
      firstFrame: 0
      lastFrame: 85
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolOneHandShootingStanding
      takeName: PistolOneHandShottingStanding
      firstFrame: 0
      lastFrame: 35
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: AssaultGun
        weight: 0
      - path: Bip01
        weight: 1
      - path: Bip01/Bip01_Pelvis
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_L_Thigh
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_L_Thigh/Bip01_L_Calf
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_L_Thigh/Bip01_L_Calf/Bip01_L_Foot
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_L_Thigh/Bip01_L_Calf/Bip01_L_Foot/Bip01_L_Toe0
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_R_Thigh
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_R_Thigh/Bip01_R_Calf
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_R_Thigh/Bip01_R_Calf/Bip01_R_Foot
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_R_Thigh/Bip01_R_Calf/Bip01_R_Foot/Bip01_R_Toe0
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_Head
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand/Bip01_L_Finger0
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand/Bip01_L_Finger0/Bip01_L_Finger01
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand/Bip01_L_Finger1
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand/Bip01_L_Finger1/Bip01_L_Finger11
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand/Bip01_L_Finger1/Bip01_L_Finger11/Bip01_L_Finger12
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand/Bip01_L_Finger2
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_L_Clavicle/Bip01_L_UpperArm/Bip01_L_Forearm/Bip01_L_Hand/Bip01_L_Finger2/Bip01_L_Finger21
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/Bip01_R_Finger0
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/Bip01_R_Finger0/Bip01_R_Finger01
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/Bip01_R_Finger0/Bip01_R_Finger01/Bip01_R_Finger02
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/Bip01_R_Finger1
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/Bip01_R_Finger1/Bip01_R_Finger11
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/Bip01_R_Finger1/Bip01_R_Finger11/Bip01_R_Finger12
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/Bip01_R_Finger2
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/LeftHandLitFingerA
        weight: 1
      - path: Bip01/Bip01_Pelvis/Bip01_Spine/Bip01_Spine1/Bip01_Spine2/Bip01_Neck/Bip01_R_Clavicle/Bip01_R_UpperArm/Bip01_R_Forearm/Bip01_R_Hand/LeftHandLitFingerA/LeftHandLitFingerB
        weight: 1
      - path: Head
        weight: 0
      - path: InfiltratorPistol
        weight: 0
      - path: ReconTroop
        weight: 0
      - path: ReconTroopHelmet
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolPuttingGunBack
      takeName: PistolPuttingGunBack
      firstFrame: 0
      lastFrame: 40
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolReloadOneHandSquat
      takeName: PistolReloadOneHandSquat
      firstFrame: 0
      lastFrame: 70.00001
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolReloadStandingStill
      takeName: PistolReloadStandingStill
      firstFrame: 0
      lastFrame: 70.00001
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolReloadTwoHands
      takeName: PistolReloadTwoHands
      firstFrame: 0
      lastFrame: 69.00001
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolRightFootkick
      takeName: PistolRightFootkick
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolTakingGunOut
      takeName: PistolTakingGunOut
      firstFrame: 0
      lastFrame: 39.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolTwoHandsLookingAround
      takeName: PistolTwoHandsLookingAround
      firstFrame: 0
      lastFrame: 120
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolTwoHandsLyingShooting
      takeName: PistolTwoHandsLyingShooting
      firstFrame: 0
      lastFrame: 22
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolTwoHandsShooting
      takeName: PistolTwoHandsShooting
      firstFrame: 0
      lastFrame: 34.000004
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolTwoHandsSquatPointing
      takeName: PistolTwoHandsSquatPointing
      firstFrame: 0
      lastFrame: 120
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PistolTwoHandsFireStandingStill
      takeName: PistolTwoHandsStandingStill
      firstFrame: 0
      lastFrame: 34.000004
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIButtkick
      takeName: TwoHandGunIButtkick
      firstFrame: 0
      lastFrame: 25.000002
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIdle
      takeName: TwoHandGunIdle
      firstFrame: 0
      lastFrame: 49
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIDyinglying
      takeName: TwoHandGunIDyinglying
      firstFrame: 0
      lastFrame: 15
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: HandGunIDyingback
      takeName: HandGunIDyingback
      firstFrame: 0
      lastFrame: 37
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIDyingsquat
      takeName: TwoHandGunIDyingsquat
      firstFrame: 0
      lastFrame: 33
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFirelying
      takeName: TwoHandGunIFirelying
      firstFrame: 0
      lastFrame: 15
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFiresquat
      takeName: TwoHandGunIFiresquat
      firstFrame: 0
      lastFrame: 11
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFirestanding
      takeName: TwoHandGunIFirestanding
      firstFrame: 0
      lastFrame: 39.000004
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFireReadysquat
      takeName: TwoHandGunIFireReadysquat
      firstFrame: 0
      lastFrame: 15
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFireReadystanding
      takeName: TwoHandGunIFireReadystanding
      firstFrame: 0
      lastFrame: 9
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunFromLayingToStanding
      takeName: toyingdGunIFromStanding
      firstFrame: 0
      lastFrame: 36
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunFromSquatToStanding
      takeName: touatndGunIFromstanding
      firstFrame: 0
      lastFrame: 26.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFromLayingtoSquat
      takeName: TwoHandGunIFromLayingtoSquat
      firstFrame: 0
      lastFrame: 39.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFromSquattoLaying
      takeName: TwoHandGunIFromSquattoLaying
      firstFrame: 0
      lastFrame: 39.000004
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIGrenadethrow
      takeName: TwoHandGunIGrenadethrow
      firstFrame: 0
      lastFrame: 40
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIJump
      takeName: TwoHandGunIJump
      firstFrame: 0
      lastFrame: 120
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunILookingAroundSquatA
      takeName: TwoHandGunILookingAroundSquatA
      firstFrame: 0
      lastFrame: 129
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIMovingleft
      takeName: TwoHandGunIMovingleft
      firstFrame: 0
      lastFrame: 20
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIMovingright
      takeName: TwoHandGunIMovingright
      firstFrame: 0
      lastFrame: 19
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIReloadsquat
      takeName: TwoHandGunIReloadsquat
      firstFrame: 0
      lastFrame: 73
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIReloadstanding
      takeName: TwoHandGunIReloadstanding
      firstFrame: 0
      lastFrame: 75.00001
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIRun
      takeName: TwoHandGunIRun
      firstFrame: 0
      lastFrame: 22
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIFromStandingToSquat
      takeName: toandingunITwoHandGunIFromsquat
      firstFrame: 0
      lastFrame: 24.000002
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIWalking
      takeName: TwoHandGunIWalking
      firstFrame: 0
      lastFrame: 36
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TwoHandGunIWalkingback
      takeName: TwoHandGunIWalkingback
      firstFrame: 0
      lastFrame: 35
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: Bip01
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Thigh
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Thigh
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Calf
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Calf
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Foot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Foot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_Spine2
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_Neck
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Clavicle
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Clavicle
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_UpperArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_UpperArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Forearm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Forearm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Hand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Hand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Toe0
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Toe0
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Finger0
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Finger01
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Finger1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Finger11
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Finger12
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Finger2
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_L_Finger21
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Finger0
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Finger01
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Finger02
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Finger1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Finger11
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Finger12
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandLitFingerA
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandLitFingerB
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Bip01_R_Finger2
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: mReconTroop(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: AssaultGun
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: InfiltratorPistol
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ReconTroop
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ReconTroopHelmet
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Bip01
      parentName: 
      position: {x: 0, y: 1.028672, z: -0.05030605}
      rotation: {x: 0.5, y: -0.5, z: -0.5, w: -0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: Bip01_Pelvis
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0.49999973, y: -0.5000003, z: -0.49999982, w: -0.50000024}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: Bip01_Spine
      parentName: 
      position: {x: -0.11083122, y: 0.01619429, z: 0.000000109999995}
      rotation: {x: -0.0000020712614, y: 0.00000044703484, z: -0.0003979951, w: 1}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000001}
    - name: Bip01_Spine1
      parentName: 
      position: {x: -0.12334061, y: -0.0001117, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Bip01_Spine2
      parentName: 
      position: {x: -0.1402845, y: -0.00018424001, z: 0}
      rotation: {x: 0.00000005960463, y: 0.00000005960463, z: -0.00000005960463, w: 1}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
    - name: Bip01_Neck
      parentName: 
      position: {x: -0.24319641, y: 0.00022475, z: 0}
      rotation: {x: 3.8670002e-14, y: 0.00000097437, z: -0.22495098, w: 0.97437006}
      scale: {x: 0.9999999, y: 0.9999995, z: 1.0000004}
    - name: Bip01_L_Clavicle
      parentName: 
      position: {x: 0.04835464, y: 0.02337128, z: 0.04107449}
      rotation: {x: 0.68451256, y: 0.17732362, z: -0.6928533, w: 0.14125621}
      scale: {x: 1.0000002, y: 0.99999887, z: 0.99999946}
    - name: Bip01_L_UpperArm
      parentName: 
      position: {x: -0.17559762, y: 0, z: 0.00000014999999}
      rotation: {x: 0.019702567, y: -0.022935737, z: 0.02480455, w: 0.999235}
      scale: {x: 1.000001, y: 0.9999997, z: 0.9999994}
    - name: Bip01_L_Forearm
      parentName: 
      position: {x: -0.335524, y: 0.00000003, z: 0.00000031}
      rotation: {x: 0.000000037252903, y: 0.000000029802322, z: 0.026705474, w: 0.9996434}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000001}
    - name: Bip01_L_Hand
      parentName: 
      position: {x: -0.23164749, y: 0.00000002, z: -0.00000014999999}
      rotation: {x: 0.7068253, y: 0.00000009441281, z: 0.000000119209275, w: -0.7073882}
      scale: {x: 0.9999998, y: 0.99999905, z: 0.9999998}
    - name: Bip01_L_Finger0
      parentName: 
      position: {x: -0.027958259, y: -0.00014191, z: -0.059544675}
      rotation: {x: -0.67314374, y: 0.109859206, z: -0.042999834, w: -0.7300408}
      scale: {x: 1.000002, y: 0.9999969, z: 1.0000019}
    - name: Bip01_L_Finger01
      parentName: 
      position: {x: -0.03240257, y: -0.00579735, z: 0.00036591}
      rotation: {x: -0.00000007450578, y: -0.000000029802312, z: -0.11640367, w: 0.993202}
      scale: {x: 1.0000004, y: 0.9999989, z: 1.0000001}
    - name: Bip01_L_Finger1
      parentName: 
      position: {x: -0.109862514, y: -0.0059758797, z: -0.03212524}
      rotation: {x: -0.009288611, y: -0.05459943, z: 0.0405406, w: 0.9976418}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000005}
    - name: Bip01_L_Finger11
      parentName: 
      position: {x: -0.03864689, y: 0.00494186, z: -0.00181967}
      rotation: {x: -0.000000016472764, y: -0.00000006763729, z: -0.11640325, w: 0.9932021}
      scale: {x: 1.0000006, y: 1.0000005, z: 1}
    - name: Bip01_L_Finger12
      parentName: 
      position: {x: -0.027164878, y: -0.00073532, z: -0.00170808}
      rotation: {x: -0.00000014819668, y: 0.00000004102184, z: -0.11640325, w: 0.9932021}
      scale: {x: 1.0000007, y: 0.9999999, z: 1.0000001}
    - name: Bip01_L_Finger2
      parentName: 
      position: {x: -0.098807484, y: 0.0129557, z: 0.044207778}
      rotation: {x: -0.011603716, y: -0.08097456, z: 0.053356048, w: 0.9952194}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000005}
    - name: Bip01_L_Finger21
      parentName: 
      position: {x: -0.0330509, y: 0.0047791298, z: 0.00031238}
      rotation: {x: -0.00000001658918, y: -0.0000000685104, z: -0.11640325, w: 0.9932021}
      scale: {x: 1.000001, y: 1.0000006, z: 1.0000004}
    - name: Bip01_R_Clavicle
      parentName: 
      position: {x: 0.04835449, y: 0.023371661, z: -0.04107431}
      rotation: {x: -0.6845128, y: -0.17731693, z: -0.69285554, w: 0.14125238}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000001}
    - name: Bip01_R_UpperArm
      parentName: 
      position: {x: -0.1755976, y: 0, z: 0.00000014999999}
      rotation: {x: 0.019702218, y: -0.022936132, z: -0.024805112, w: -0.9992349}
      scale: {x: 1.0000002, y: 0.9999996, z: 1.0000001}
    - name: Bip01_R_Forearm
      parentName: 
      position: {x: -0.33552468, y: 0.00000001, z: -0.00000014999999}
      rotation: {x: -0.00000008940696, y: 0.0000002533197, z: 0.026705425, w: 0.9996433}
      scale: {x: 1.0000001, y: 1.0000005, z: 0.9999999}
    - name: Bip01_R_Hand
      parentName: 
      position: {x: -0.23164749, y: -0.00000001, z: -0.00000008}
      rotation: {x: -0.70682544, y: 0.00000005960463, z: 0.00000002549495, w: -0.70738804}
      scale: {x: 1.0000002, y: 1.000001, z: 1.0000005}
    - name: Bip01_R_Finger0
      parentName: 
      position: {x: -0.03543877, y: -0.00065559, z: 0.06791017}
      rotation: {x: -0.67244935, y: 0.079089925, z: 0.0019634655, w: 0.7359027}
      scale: {x: 1.0000005, y: 1.0000012, z: 0.99999833}
    - name: Bip01_R_Finger01
      parentName: 
      position: {x: -0.03327984, y: -0.01047363, z: -0.00100273}
      rotation: {x: -0.007657526, y: 0.007671533, z: 0.017629724, w: 0.9997859}
      scale: {x: 1.0000001, y: 0.9999999, z: 1}
    - name: Bip01_R_Finger02
      parentName: 
      position: {x: -0.0359169, y: -0.0072495695, z: -0.00191544}
      rotation: {x: 0.000000059604638, y: -0.000000029802319, z: 0.11640224, w: -0.9932022}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    - name: Bip01_R_Finger1
      parentName: 
      position: {x: -0.11277363, y: -0.00323029, z: 0.03297887}
      rotation: {x: 0.00434088, y: 0.034809988, z: 0.016878603, w: 0.999242}
      scale: {x: 0.99999964, y: 0.9999999, z: 1.0000002}
    - name: Bip01_R_Finger11
      parentName: 
      position: {x: -0.03224649, y: 0.00257278, z: 0.00222764}
      rotation: {x: -0.00000009220092, y: -0.00000004738103, z: -0.11640228, w: 0.99320215}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: Bip01_R_Finger12
      parentName: 
      position: {x: -0.02746078, y: -0.0034492498, z: 0.00046687}
      rotation: {x: -0.000000068917856, y: 0.000000054016695, z: -0.11640221, w: 0.9932022}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: Bip01_R_Finger2
      parentName: 
      position: {x: -0.10548046, y: -0.00079857, z: -0.01990018}
      rotation: {x: 0.0003980296, y: -0.000000025145711, z: 0.000000021977941, w: 0.99999994}
      scale: {x: 0.99999964, y: 0.9999999, z: 1.0000002}
    - name: LeftHandLitFingerA
      parentName: 
      position: {x: -0.103507996, y: 0.02096764, z: -0.03766356}
      rotation: {x: -0.13935408, y: 0.068204515, z: -0.9025504, w: 0.40166077}
      scale: {x: 0.99999917, y: 0.9999987, z: 0.9999989}
    - name: LeftHandLitFingerB
      parentName: 
      position: {x: 0.017533489, y: -0.01901581, z: -0.00462614}
      rotation: {x: -0.0000000060534284, y: -0.000000095693274, z: 0.00000011273911,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Bip01_Head
      parentName: 
      position: {x: -0.07228119, y: 0.00000014999999, z: 0}
      rotation: {x: -0.00000005960463, y: -0.00000044703472, z: 0.15682803, w: 0.98762596}
      scale: {x: 1.0000004, y: 1.0000006, z: 1}
    - name: Bip01_L_Thigh
      parentName: 
      position: {x: 0.11084419, y: -0.01610635, z: 0.12878789}
      rotation: {x: 0.026414987, y: -0.9996053, z: 0.00027172265, w: -0.00956872}
      scale: {x: 0.999999, y: 1.0000001, z: 1.0000005}
    - name: Bip01_L_Calf
      parentName: 
      position: {x: -0.43528107, y: 0, z: -0.00000004}
      rotation: {x: 0.00000020861621, y: 0.00000005960463, z: 0.08948854, w: 0.9959879}
      scale: {x: 1.0000004, y: 1.0000002, z: 0.9999997}
    - name: Bip01_L_Foot
      parentName: 
      position: {x: -0.46156985, y: 0.00000003, z: 0.00000002}
      rotation: {x: 0.0005801915, y: 0.009555308, z: -0.06354149, w: 0.9979333}
      scale: {x: 0.99999976, y: 0.99999857, z: 1.0000005}
    - name: Bip01_L_Toe0
      parentName: 
      position: {x: -0.13227695, y: 0.17023475, z: 0.00000006}
      rotation: {x: -0.000000029802319, y: -0.00000006325484, z: -0.7071067, w: 0.707107}
      scale: {x: 0.9999998, y: 0.9999996, z: 1.0000001}
    - name: Bip01_R_Thigh
      parentName: 
      position: {x: 0.11084411, y: -0.016105639, z: -0.1287879}
      rotation: {x: 0.026414927, y: -0.9996053, z: -0.0002748817, w: 0.009569063}
      scale: {x: 0.9999998, y: 0.9999997, z: 1.0000002}
    - name: Bip01_R_Calf
      parentName: 
      position: {x: -0.435281, y: -0.00000001, z: 0.00000002}
      rotation: {x: 0.00000011920926, y: 0.000000089406946, z: 0.08948841, w: 0.9959879}
      scale: {x: 1.0000006, y: 1.0000002, z: 1}
    - name: Bip01_R_Foot
      parentName: 
      position: {x: -0.46156988, y: 0.00000002, z: -0.00000002}
      rotation: {x: -0.0005802213, y: -0.009555308, z: -0.06354138, w: 0.99793327}
      scale: {x: 0.99999833, y: 0.99999857, z: 1.0000004}
    - name: Bip01_R_Toe0
      parentName: 
      position: {x: -0.13227692, y: 0.17023471, z: 0.000000049999997}
      rotation: {x: -0.00000017881389, y: -0.00000016153739, z: -0.7071067, w: 0.7071068}
      scale: {x: 1.0000012, y: 0.99999905, z: 1.0000002}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
