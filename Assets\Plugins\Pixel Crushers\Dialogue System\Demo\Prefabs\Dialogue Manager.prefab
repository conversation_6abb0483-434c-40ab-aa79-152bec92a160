%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1003643122459378
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4240220743476432}
  - component: {fileID: 114937748117785014}
  - component: {fileID: 114041376378916948}
  - component: {fileID: 114877596569806122}
  - component: {fileID: 114889499020232316}
  - component: {fileID: 114418064281648646}
  - component: {fileID: 114160787819640068}
  - component: {fileID: 114791677188177214}
  - component: {fileID: 114974341299790918}
  - component: {fileID: 114561334627962148}
  m_Layer: 0
  m_Name: Dialogue Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4240220743476432
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 224904129982485434}
  - {fileID: 224890378261823126}
  - {fileID: 4003996800428378}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114937748117785014
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab8b685e62a9aeb4a9472b30ec2d86d9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialDatabase: {fileID: 11400000, guid: c1cc03cd82f6cdb40afbc0575be25564, type: 2}
  displaySettings:
    conversationOverrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    dialogueUI: {fileID: 161038, guid: 5c56f58e2d14e894c96171a7540a6a1d, type: 3}
    defaultCanvas: {fileID: 223944997660514316}
    localizationSettings:
      language: 
      useSystemLanguage: 0
      textTable: {fileID: 0}
    subtitleSettings:
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      allowPCSubtitleReminders: 0
      skipPCSubtitleAfterResponseMenu: 1
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      richTextEmphases: 1
      informSequenceStartAndEnd: 0
    cameraSettings:
      sequencerCamera: {fileID: 0}
      alternateCameraObject: {fileID: 0}
      cameraAngles: {fileID: 0}
      keepCameraPositionAtConversationEnd: 0
      defaultSequence: AudioWait(entrytag); Delay({{end}})
      defaultPlayerSequence: AudioWait(entrytag)
      defaultResponseMenuSequence: 
      entrytagFormat: 0
      disableInternalSequencerCommands: 0
    inputSettings:
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      responseTimeoutAction: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      qteButtons:
      - Fire1
      - Fire2
      cancel:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    barkSettings:
      allowBarksDuringConversations: 1
      barkCharsPerSecond: 0
      minBarkSeconds: 0
    alertSettings:
      allowAlertsDuringConversations: 0
      alertCheckFrequency: 0
      alertCharsPerSecond: 20
      minAlertSeconds: 3
  persistentDataSettings:
    recordPersistentDataOn: 0
    includeActorData: 1
    includeAllItemData: 0
    includeLocationData: 0
    includeStatusAndRelationshipData: 1
    includeAllConversationFields: 0
    saveConversationSimStatusWithField: 
    saveDialogueEntrySimStatusWithField: 
    asyncGameObjectBatchSize: 1000
    asyncDialogueEntryBatchSize: 100
    initializeNewVariables: 1
  allowSimultaneousConversations: 0
  interruptActiveConversations: 0
  includeSimStatus: 0
  instantiateDatabase: 0
  preloadResources: 1
  warmUpConversationController: 0
  dontHideImmediateDuringWarmup: 0
  dontDestroyOnLoad: 1
  allowOnlyOneInstance: 1
  dialogueTimeMode: 0
  debugLevel: 2
--- !u!114 &114041376378916948
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3b24a5606b164c84db0d3ed3ee8256e1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_parent: {fileID: 224904129982485434}
  m_prefabs:
  - {fileID: 168688, guid: a5c98dfcec76e1847a0aa8d7c8a93459, type: 3}
  - {fileID: 158168, guid: 1faea9b6a0e6e5544bcc70c3c4579717, type: 3}
  - {fileID: 174416, guid: f873a592e0f5eb7469d72cf7162ed7ad, type: 3}
  m_position: 0
--- !u!114 &114877596569806122
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dddab24af7a02a54c9631fd5c532d7c4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  inputDevice: 0
  joystickKeyCodesToCheck: 4a0100004b0100004c01000051010000
  joystickButtonsToCheck: []
  joystickAxesToCheck: []
  joystickAxisThreshold: 0.5
  keyButtonsToCheck: []
  keyCodesToCheck: 1b000000
  keyInputSwitchesModeTo: 1
  alwaysAutoFocus: 0
  detectMouseControl: 1
  mouseMoveThreshold: 0.1
  controlCursorState: 1
  enforceCursorOnPause: 0
  controlGraphicRaycasters: 0
  backKeyCodes: 4b010000
  backButtons:
  - Cancel
  submitButton: Submit
  singleton: 1
  onUseKeyboard:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  onUseJoystick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  onUseMouse:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  onUseTouch:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &114889499020232316
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fc03036aad05c334eab057a96938959a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_version: 0
  m_saveCurrentScene: 1
  m_framesToWaitBeforeApplyData: 1
  m_debug: 1
--- !u!114 &114418064281648646
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99738be95cdf96a4c83561277430a7a5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  pauseDuringTransition: 1
  leaveSceneTransition:
    animator: {fileID: 95430577421052368}
    trigger: Show
    animationDuration: 1
    minTransitionDuration: 1
    onTransitionStart:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
    onTransitionEnd:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  loadingSceneName: 
  enterSceneTransition:
    animator: {fileID: 95430577421052368}
    trigger: Hide
    animationDuration: 0
    minTransitionDuration: 0
    onTransitionStart:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
    onTransitionEnd:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
--- !u!114 &114160787819640068
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ad4d3f2fce70b2441bd76f6c5777e8ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_playerPrefsKeyBase: Save
  encrypt: 0
  encryptionPassword: My Password
  m_debug: 0
--- !u!114 &114791677188177214
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9fbafe64f40b0694b8d4ec9c082f8bf1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_prettyPrint: 0
--- !u!114 &114974341299790918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 689b4b1238e337d4590a91183947bcd5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_key: 
  m_appendSaverTypeToKey: 0
  m_saveAcrossSceneChanges: 1
  m_restoreStateOnStart: 0
  skipApplyDataAfterFramesIfApplyImmediate: 1
  saveRawData: 0
--- !u!114 &114561334627962148
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1003643122459378}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5dd6025e599c3a3409abb6c91a48f1b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1092739927580670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4003996800428378}
  - component: {fileID: 114499580892680844}
  - component: {fileID: 114124543433568960}
  m_Layer: 0
  m_Name: Demo Menu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4003996800428378
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1092739927580670}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 4240220743476432}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114499580892680844
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1092739927580670}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b7a2840b06cde3b479416623b4d15788, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  startMessage: 'Welcome to the Dialogue System for Unity!


    WASD moves, mouse looks, space interacts

    LMB fires, Escape opens menu'
  menuKey: 27
  guiSkin: {fileID: 11400000, guid: dbe874f440d0cf84cb5c28fd70e8123c, type: 2}
  closeWhenQuestLogOpen: 1
  lockCursorDuringPlay: 1
  onOpen:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  onClose:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.Events.UnityEvent, UnityEngine.CoreModule, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &114124543433568960
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1092739927580670}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a9990037fcb2384ba5469515c75575d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  once: 0
  sequence: Fade(in,1)
  speaker: {fileID: 0}
  listener: {fileID: 0}
  condition:
    luaConditions: []
    questConditions: []
    acceptedTags: []
    acceptedGameObjects: []
    luaWizardIndex: -1
    lastEvaluationValue: 0
  trigger: 16
  waitOneFrameOnStartOrEnable: 0
--- !u!1 &1172956102938826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 224197762265611294}
  - component: {fileID: 222864669892175608}
  - component: {fileID: 114812395505950690}
  m_Layer: 0
  m_Name: Black
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &224197762265611294
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172956102938826}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 224890378261823126}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &222864669892175608
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172956102938826}
  m_CullTransparentMesh: 0
--- !u!114 &114812395505950690
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172956102938826}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
--- !u!1 &1326164556581496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 224904129982485434}
  - component: {fileID: 223944997660514316}
  - component: {fileID: 114774322843125244}
  - component: {fileID: 3209549812856773629}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &224904129982485434
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1326164556581496}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 4240220743476432}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!223 &223944997660514316
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1326164556581496}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &114774322843125244
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1326164556581496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1301386320, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &3209549812856773629
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1326164556581496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1980459831, guid: f70555f144d8491a825f0804e09c671c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 960, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 1
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
--- !u!1 &1635533227216014
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 224890378261823126}
  - component: {fileID: 223956456205192646}
  - component: {fileID: 114354094338542502}
  - component: {fileID: 114613399014831548}
  - component: {fileID: 225810186479465586}
  - component: {fileID: 95430577421052368}
  m_Layer: 0
  m_Name: SceneFaderCanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &224890378261823126
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635533227216014}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 224197762265611294}
  m_Father: {fileID: 4240220743476432}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!223 &223956456205192646
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635533227216014}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 998
  m_TargetDisplay: 0
--- !u!114 &114354094338542502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635533227216014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1980459831, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
--- !u!114 &114613399014831548
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635533227216014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1301386320, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!225 &225810186479465586
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635533227216014}
  m_Enabled: 1
  m_Alpha: 0
  m_Interactable: 0
  m_BlocksRaycasts: 0
  m_IgnoreParentGroups: 0
--- !u!95 &95430577421052368
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635533227216014}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 7657962a860b89047801d76052645133, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 2
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
