using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 关键词管理器
/// 负责关键词的创建、查找、序列化和重建
/// </summary>
[CreateAssetMenu(fileName = "KeywordManager", menuName = "Midnight Broadcasting/Keyword Manager")]
public class KeywordManager : ScriptableObject
{
    [Header("=== 关键词库 ===")]
    [Tooltip("所有可用的关键词模板")]
    public List<KeywordDataSO> keywordTemplates = new List<KeywordDataSO>();
    
    [Tooltip("所有可用的关键词组合")]
    public List<KeywordCombinationSO> keywordCombinations = new List<KeywordCombinationSO>();

    [Header("=== 运行时数据 ===")]
    [Tooltip("当前活跃的关键词实例")]
    public List<KeywordDataSO> activeKeywords = new List<KeywordDataSO>();

    // 单例模式
    private static KeywordManager _instance;
    public static KeywordManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = Resources.Load<KeywordManager>("KeywordManager");
                if (_instance == null)
                {
                    Debug.LogError("KeywordManager not found in Resources folder!");
                }
            }
            return _instance;
        }
    }

    #region 关键词创建和查找

    /// <summary>
    /// 根据关键词文本创建关键词实例
    /// </summary>
    public KeywordDataSO CreateKeyword(string keywordText, string audienceId = "")
    {
        var template = FindKeywordTemplate(keywordText);
        if (template == null)
        {
            Debug.LogWarning($"未找到关键词模板：{keywordText}");
            return null;
        }

        var instance = Instantiate(template);
        instance.Initialize();
        instance.associatedAudienceId = audienceId;
        instance.name = template.name + "_Instance";

        activeKeywords.Add(instance);
        return instance;
    }

    /// <summary>
    /// 查找关键词模板
    /// </summary>
    public KeywordDataSO FindKeywordTemplate(string keywordText)
    {
        return keywordTemplates.FirstOrDefault(k => k != null && k.keyword == keywordText);
    }

    /// <summary>
    /// 获取所有关键词模板
    /// </summary>
    public List<KeywordDataSO> GetAllKeywordTemplates()
    {
        return keywordTemplates.Where(k => k != null).ToList();
    }

    /// <summary>
    /// 根据类型获取关键词模板
    /// </summary>
    public List<KeywordDataSO> GetKeywordsByType(KeywordType type)
    {
        return keywordTemplates.Where(k => k != null && k.type == type).ToList();
    }

    /// <summary>
    /// 获取稀有关键词
    /// </summary>
    public List<KeywordDataSO> GetRareKeywords()
    {
        return keywordTemplates.Where(k => k != null && k.isRare).ToList();
    }

    #endregion

    #region 关键词组合

    /// <summary>
    /// 查找可用的组合
    /// </summary>
    public List<KeywordCombinationSO> FindAvailableCombinations(List<KeywordDataSO> availableKeywords)
    {
        List<KeywordCombinationSO> availableCombinations = new List<KeywordCombinationSO>();

        foreach (var combination in keywordCombinations)
        {
            if (combination != null && combination.CanCombine(availableKeywords))
            {
                availableCombinations.Add(combination);
            }
        }

        return availableCombinations;
    }

    /// <summary>
    /// 执行关键词组合
    /// </summary>
    public KeywordDataSO ExecuteCombination(KeywordCombinationSO combination, List<KeywordDataSO> availableKeywords, CoreGameDataSO gameData = null)
    {
        if (combination == null) return null;

        var result = combination.ExecuteCombination(availableKeywords, gameData);
        if (result != null)
        {
            activeKeywords.Add(result);
        }

        return result;
    }

    /// <summary>
    /// 获取组合提示
    /// </summary>
    public List<string> GetCombinationHints(List<KeywordDataSO> availableKeywords)
    {
        List<string> hints = new List<string>();

        foreach (var combination in keywordCombinations)
        {
            if (combination != null && !combination.isHidden)
            {
                var missingKeywords = combination.GetMissingKeywords(availableKeywords);
                if (missingKeywords.Count > 0 && missingKeywords.Count <= 2) // 只显示接近完成的组合
                {
                    string hint = $"组合提示：{combination.combinationHint}";
                    if (!string.IsNullOrEmpty(hint))
                    {
                        hints.Add(hint);
                    }
                }
            }
        }

        return hints;
    }

    #endregion

    #region 序列化支持

    /// <summary>
    /// 序列化关键词列表
    /// </summary>
    public SerializableKeywordData[] SerializeKeywords(List<KeywordDataSO> keywords)
    {
        List<SerializableKeywordData> serializedList = new List<SerializableKeywordData>();

        foreach (var keyword in keywords)
        {
            if (keyword != null)
            {
                serializedList.Add(SerializeKeyword(keyword));
            }
        }

        return serializedList.ToArray();
    }

    /// <summary>
    /// 序列化单个关键词
    /// </summary>
    public SerializableKeywordData SerializeKeyword(KeywordDataSO keyword)
    {
        return new SerializableKeywordData
        {
            keyword = keyword.keyword,
            type = keyword.type,
            collectedTimeString = keyword.collectedTimeString,
            expiryTimeString = keyword.expiryTimeString,
            hasEvolved = keyword.hasEvolved,
            weight = keyword.weight,
            associatedAudienceId = keyword.associatedAudienceId
        };
    }

    /// <summary>
    /// 反序列化关键词列表
    /// </summary>
    public List<KeywordDataSO> DeserializeKeywords(SerializableKeywordData[] serializedKeywords, string audienceId = "")
    {
        List<KeywordDataSO> keywords = new List<KeywordDataSO>();

        foreach (var serializedKeyword in serializedKeywords)
        {
            var keyword = DeserializeKeyword(serializedKeyword, audienceId);
            if (keyword != null)
            {
                keywords.Add(keyword);
            }
        }

        return keywords;
    }

    /// <summary>
    /// 反序列化单个关键词
    /// </summary>
    public KeywordDataSO DeserializeKeyword(SerializableKeywordData serializedKeyword, string audienceId = "")
    {
        var template = FindKeywordTemplate(serializedKeyword.keyword);
        if (template == null)
        {
            Debug.LogWarning($"反序列化失败，未找到关键词模板：{serializedKeyword.keyword}");
            return null;
        }

        var instance = Instantiate(template);
        
        // 恢复序列化的数据
        instance.collectedTimeString = serializedKeyword.collectedTimeString;
        instance.expiryTimeString = serializedKeyword.expiryTimeString;
        instance.hasEvolved = serializedKeyword.hasEvolved;
        instance.weight = serializedKeyword.weight;
        instance.associatedAudienceId = !string.IsNullOrEmpty(serializedKeyword.associatedAudienceId) 
            ? serializedKeyword.associatedAudienceId : audienceId;
        
        instance.name = template.name + "_Restored";
        activeKeywords.Add(instance);

        return instance;
    }

    #endregion

    #region 关键词生命周期管理

    /// <summary>
    /// 清理过期关键词
    /// </summary>
    public List<KeywordDataSO> CleanupExpiredKeywords()
    {
        var expiredKeywords = activeKeywords.Where(k => k != null && k.IsExpired).ToList();
        
        foreach (var expired in expiredKeywords)
        {
            activeKeywords.Remove(expired);
            Debug.Log($"清理过期关键词：{expired.keyword}");
        }

        return expiredKeywords;
    }

    /// <summary>
    /// 处理关键词演化
    /// </summary>
    public List<KeywordDataSO> ProcessKeywordEvolution()
    {
        List<KeywordDataSO> evolvedKeywords = new List<KeywordDataSO>();

        var evolvableKeywords = activeKeywords.Where(k => k != null && k.CanEvolve).ToList();
        
        foreach (var keyword in evolvableKeywords)
        {
            var evolved = keyword.TryEvolve();
            if (evolved != null)
            {
                activeKeywords.Add(evolved);
                evolvedKeywords.Add(evolved);
            }
        }

        return evolvedKeywords;
    }

    /// <summary>
    /// 获取即将过期的关键词
    /// </summary>
    public List<KeywordDataSO> GetExpiringSoonKeywords()
    {
        return activeKeywords.Where(k => k != null && k.IsExpiringSoon()).ToList();
    }

    /// <summary>
    /// 移除关键词
    /// </summary>
    public bool RemoveKeyword(KeywordDataSO keyword)
    {
        if (keyword != null && activeKeywords.Contains(keyword))
        {
            activeKeywords.Remove(keyword);
            return true;
        }
        return false;
    }

    /// <summary>
    /// 清空所有活跃关键词
    /// </summary>
    public void ClearAllActiveKeywords()
    {
        activeKeywords.Clear();
    }

    #endregion

    #region Unity事件

    private void OnValidate()
    {
        // 移除空引用
        keywordTemplates.RemoveAll(k => k == null);
        keywordCombinations.RemoveAll(c => c == null);
        activeKeywords.RemoveAll(k => k == null);
    }

    #endregion

    #region 调试方法

    [ContextMenu("显示所有关键词")]
    private void ShowAllKeywords()
    {
        Debug.Log($"关键词模板数量：{keywordTemplates.Count}");
        Debug.Log($"活跃关键词数量：{activeKeywords.Count}");
        Debug.Log($"组合配方数量：{keywordCombinations.Count}");
    }

    [ContextMenu("清理过期关键词")]
    private void TestCleanupExpired()
    {
        if (Application.isPlaying)
        {
            var expired = CleanupExpiredKeywords();
            Debug.Log($"清理了 {expired.Count} 个过期关键词");
        }
    }

    [ContextMenu("处理关键词演化")]
    private void TestProcessEvolution()
    {
        if (Application.isPlaying)
        {
            var evolved = ProcessKeywordEvolution();
            Debug.Log($"演化了 {evolved.Count} 个关键词");
        }
    }

    #endregion
}

/// <summary>
/// 可序列化的关键词数据
/// </summary>
[System.Serializable]
public class SerializableKeywordData
{
    public string keyword;
    public KeywordType type;
    public string collectedTimeString;
    public string expiryTimeString;
    public bool hasEvolved;
    public float weight;
    public string associatedAudienceId;
}
