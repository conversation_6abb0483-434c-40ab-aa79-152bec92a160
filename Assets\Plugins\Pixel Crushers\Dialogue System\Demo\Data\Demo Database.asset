%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 935899b62f48ae5498594680ed17d133, type: 3}
  m_Name: Demo Database
  m_EditorClassIdentifier: 
  version: *******
  author: Pixel Crushers
  description: This project demonstrates the major features of the Dialogue System
    for Unity.  In this sci-fi scene, the player's squad has been sent to intercept
    launch codes to stop the launch of a weapon against their homeworld.
  globalUserScript: 
  emphasisSettings:
  - color: {r: 1, g: 0, b: 0, a: 1}
    bold: 1
    italic: 0
    underline: 0
  - color: {r: 0.26666668, g: 0.7137255, b: 0.20392157, a: 1}
    bold: 1
    italic: 0
    underline: 0
  - color: {r: 0.27450982, g: 0.53333336, b: 0.9019608, a: 1}
    bold: 1
    italic: 0
    underline: 0
  - color: {r: 0.8, g: 0.8, b: 0.8, a: 1}
    bold: 1
    italic: 0
    underline: 0
  baseID: 1
  actors:
  - id: 1
    fields:
    - title: Name
      value: Player
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[../../Art/Portraits/Player.png]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: The Player controls a soldier sent to intercept the evil space emperor's
        launch codes to prevent him from attacking your planet.
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 2800000, guid: e63e2479a8b2dc54ea311a230ce6c979, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 2
    fields:
    - title: Name
      value: Private Hart
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[../../Art/Portraits/Private Hart.png]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: Private Hart is a kind-hearted soldier who offers the quest to hack
        the emperor's computer to get the launch codes.
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 2800000, guid: e63e2479a8b2dc54ea311a230ce6c979, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 3
    fields:
    - title: Name
      value: Sergeant Graves
      type: 0
      typeString: 
    - title: Pictures
      value: '[../../Art/Portraits/Sergeant Graves.png]'
      type: 3
      typeString: 
    - title: Description
      value: Sergeant Graves is a warlike soldier who offers the quest to hold back
        enemy forces.
      type: 0
      typeString: 
    - title: IsPlayer
      value: False
      type: 2
      typeString: 
    portrait: {fileID: 2800000, guid: e63e2479a8b2dc54ea311a230ce6c979, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 4
    fields:
    - title: Name
      value: Terminal
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: The Terminal is a computer interface through which the player can get
        the launch codes.
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 2800000, guid: a491fca6456b0204080efb8fb6caa853, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 5
    fields:
    - title: Name
      value: Dead Enemy
      type: 0
      typeString: 
    - title: Pictures
      value: '[]'
      type: 3
      typeString: 
    - title: Description
      value: The evil emperor's guards. They have a note with the terminal password.
      type: 0
      typeString: 
    - title: IsPlayer
      value: False
      type: 2
      typeString: 
    portrait: {fileID: 2800000, guid: 3e54ad1935c5efc4db4454fafe3d8ae5, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 6
    fields:
    - title: Name
      value: Enemy
      type: 0
      typeString: 
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: The Enemy actor is used for enemies to bark menacing one-off gameplay
        lines that threaten the Player.
      type: 0
      typeString: 
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 2800000, guid: 3e54ad1935c5efc4db4454fafe3d8ae5, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  items:
  - id: 1
    fields:
    - title: Name
      value: Get the Launch Codes
      type: 0
      typeString: CustomFieldType_Text
    - title: Description
      value: Your squad has infiltrated the evil Emperor's weapons factory to intercept
        launch codes to stop the mega-weapon aimed at your homeworld.
      type: 0
      typeString: CustomFieldType_Text
    - title: Success Description
      value: You get the launch codes and can redirect the weapon away from your
        homeworld.
      type: 0
      typeString: CustomFieldType_Text
    - title: Failure Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Is Item
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Track
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Trackable
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
  - id: 2
    fields:
    - title: Name
      value: Enemy Attack
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: Sergeant Graves wants you to halt the enemy advance by taking out five
        of them.
      type: 0
      typeString: CustomFieldType_Text
    - title: Success Description
      value: You defeated the enemy soldiers.
      type: 0
      typeString: CustomFieldType_Text
    - title: Failure Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Is Item
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Trackable
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Track
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Entry Count
      value: 1
      type: 1
      typeString: CustomFieldType_Number
    - title: Entry 1 State
      value: active
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 1
      value: '[var=enemiesKilled]/5 killed'
      type: 0
      typeString: CustomFieldType_Text
  locations: []
  variables:
  - id: 0
    fields:
    - title: Name
      value: Alert
      type: 0
      typeString: 
    - title: Initial Value
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Description
      value: Show alert messages during gameplay by setting this variable.
      type: 0
      typeString: 
  - id: 1
    fields:
    - title: Name
      value: enemiesKilled
      type: 0
      typeString: 
    - title: Initial Value
      value: 0
      type: 1
      typeString: CustomFieldType_Number
    - title: Description
      value: Tracks the number of enemies killed.
      type: 0
      typeString: 
  - id: 3
    fields:
    - title: Name
      value: hasLaunchCodes
      type: 0
      typeString: 
    - title: Initial Value
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Description
      value: Tracks whether the player has found the launch codes.
      type: 0
      typeString: 
  - id: 4
    fields:
    - title: Name
      value: password
      type: 0
      typeString: 
    - title: Initial Value
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Description
      value: Holds player's input from Terminal conversation's TextInput() command.
      type: 0
      typeString: 
  conversations:
  - id: 1
    fields:
    - title: Title
      value: Private Hart
      type: 0
      typeString: CustomFieldType_Text
    - title: Description
      value: This conversation occurs between the Player and Private Hart, who explains
        the main quest (Get the Launch Codes). Nodes' Conditions fields branch the
        conversation based on the current quest state.
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: Red
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: 
      - title: Conversant
        value: 2
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 1
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 0
        destinationConversationID: 1
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 0
        destinationConversationID: 1
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 0
        destinationConversationID: 1
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 468
        y: 24
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: Used if the quest is unassigned. Cuts to an immediate closeup of the
          speaker (Hart).
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: We need to intercept the launch codes before the enemy launches their
          weapon at our home world.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: Camera(Closeup); {{default}}
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 1
        destinationConversationID: 1
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: CurrentQuestState("Get the Launch Codes") == "unassigned"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 252
        y: 84
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Any luck downloading the launch codes?
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 2
        destinationConversationID: 1
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      conditionsString: CurrentQuestState("Get the Launch Codes") == "active"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 468
        y: 84
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: You got the codes! Now we can get out of here. (End of demo)
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 3
        destinationConversationID: 1
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: CurrentQuestState("Get the Launch Codes") == "success"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 720
        y: 84
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Okay, what's the problem?
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 4
        destinationConversationID: 1
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 252
        y: 144
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Let's go.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 720
        y: 144
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: I can't break into the computer. It's password protected.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 6
        destinationConversationID: 1
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 253
        y: 204
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: I'll find the password.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 'SetQuestState("Get the Launch Codes", "active");

        Variable["Alert"]
        = "Mission: Get the Launch Codes"'
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 252
        y: 264
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: I'm still working on it.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 468
        y: 144
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 215, y: 0}
    canvasZoom: 1
  - id: 2
    fields:
    - title: Title
      value: Sergeant Graves
      type: 0
      typeString: 
    - title: Description
      value: This quest runs the Enemy Attack quest.
      type: 0
      typeString: 
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 3
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: Red
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: 
      - title: Conversant
        value: 3
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 2
      isRoot: 1
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 0
        destinationConversationID: 2
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 2
        originDialogueID: 0
        destinationConversationID: 2
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      - originConversationID: 2
        originDialogueID: 0
        destinationConversationID: 2
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 396
        y: 12
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: Quest Unassigned
        type: 0
        typeString: 
      - title: Actor
        value: 3
        type: 5
        typeString: 
      - title: Conversant
        value: 1
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: 
        type: 0
        typeString: 
      conversationID: 2
      isRoot: 0
      isGroup: 1
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 1
        destinationConversationID: 2
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      - originConversationID: 2
        originDialogueID: 1
        destinationConversationID: 2
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: CurrentQuestState("Enemy Attack") == "unassigned"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 120
        y: 60
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: There's [lua(5 - Variable["enemiesKilled"])] left. We can do it.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 2
        destinationConversationID: 2
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: CurrentQuestState("Enemy Attack") == "active"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 396
        y: 60
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Great shooting. You got 'em all.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 3
        destinationConversationID: 2
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      conditionsString: CurrentQuestState("Enemy Attack") == "success"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 600
        y: 60
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Help me take out those enemies before they call for reinforcements!
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 4
        destinationConversationID: 2
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: Variable["enemiesKilled"] < 5
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 24
        y: 120
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Great job taking out those enemies!
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: Variable["enemiesKilled"] >= 5
      userScript: SetQuestState("Enemy Attack", "success")
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 204
        y: 120
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: I'm on it.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: Variable["enemiesKilled"] < 5
      userScript: 'SetQuestState("Enemy Attack", "active");

        Variable["Alert"]
        = "Objective: Kill 5 Enemies"'
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 24
        y: 170
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: I'm going in!
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 396
        y: 120
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Right on.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 600
        y: 120
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 9, y: 0}
    canvasZoom: 1
  - id: 4
    fields:
    - title: Title
      value: Dead Enemy
      type: 0
      typeString: 
    - title: Description
      value: This conversation demonstrates how to use the dialogue system for other
        kinds of interaction (searching a body). The first node moves the camera
        to the "Down" angle on the body over 1 second. The last node moves back to
        the original position.
      type: 0
      typeString: 
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 5
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: Red
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: 
      - title: Conversant
        value: 5
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 4
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 4
      isRoot: 1
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 0
        destinationConversationID: 4
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 120
        y: 20
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 5
        type: 5
        typeString: 
      - title: Conversant
        value: 1
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: This was one of the evil emperor's guards.
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: 'Camera(Down,,1);

          Delay({{end}})'
        type: 0
        typeString: 
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 1
        destinationConversationID: 4
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      - originConversationID: 4
        originDialogueID: 1
        destinationConversationID: 4
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 120
        y: 70
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: 
      - title: Conversant
        value: 5
        type: 5
        typeString: 
      - title: Menu Text
        value: '[a]Search.'
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 2
        destinationConversationID: 4
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 30
        y: 120
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: 
      - title: Conversant
        value: 5
        type: 5
        typeString: 
      - title: Menu Text
        value: '[f][a]Done.'
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: Camera(original,,1)
        type: 0
        typeString: 
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 120
        y: 220
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 5
        type: 5
        typeString: 
      - title: Conversant
        value: 1
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 'He has a note that reads: password is ''dominate''.'
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: Delay({{end}})
        type: 0
        typeString: 
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 4
        destinationConversationID: 4
        destinationDialogueID: 3
        isConnector: 1
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 120
        y: 170
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 1
  - id: 5
    fields:
    - title: Title
      value: Terminal
      type: 0
      typeString: 
    - title: Description
      value: This conversation is for the computer terminal where the player can
        download the launch codes to complete the Get the Launch Codes quest. It
        overrides the Default Sequence to delay instead of playing voiceover (see
        below).
      type: 0
      typeString: 
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 4
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 1
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 1
      defaultSequence: Delay({{end}})
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: Red
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: 
      - title: Conversant
        value: 4
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 5
      isRoot: 1
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 0
        destinationConversationID: 5
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 432
        y: 24
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: Quest Active
        type: 0
        typeString: 
      - title: Actor
        value: 4
        type: 5
        typeString: 
      - title: Conversant
        value: 1
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 'Enter password:'
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: 'Camera(Terminal Camera Angle,,1);

          TextInput(Text Field
          UI,Password,password)'
        type: 0
        typeString: 
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 1
        destinationConversationID: 5
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 429
        y: 82
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: Get Player Input
        type: 0
        typeString: 
      - title: Actor
        value: 4
        type: 5
        typeString: 
      - title: Conversant
        value: 1
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 3
        destinationConversationID: 5
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      - originConversationID: 5
        originDialogueID: 3
        destinationConversationID: 5
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 429
        y: 142
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: Correct Password
        type: 0
        typeString: 
      - title: Actor
        value: 4
        type: 5
        typeString: 
      - title: Conversant
        value: 1
        type: 5
        typeString: 
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 'Enter command:'
        type: 0
        typeString: 
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: White
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 6
        destinationConversationID: 5
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      - originConversationID: 5
        originDialogueID: 6
        destinationConversationID: 5
        destinationDialogueID: 9
        isConnector: 0
        priority: 2
      conditionsString: Variable["password"] == "dominate"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 298
        y: 202
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 4
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Invalid password.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 'Camera(original,,1);

          Delay({{end}})'
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: Variable["password"] ~= "dominate"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 549
        y: 202
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 4
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: '[download launch codes]'
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 8
        destinationConversationID: 5
        destinationDialogueID: 10
        isConnector: 0
        priority: 2
      - originConversationID: 5
        originDialogueID: 8
        destinationConversationID: 5
        destinationDialogueID: 11
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 189
        y: 262
        width: 160
        height: 30
    - id: 9
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 4
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: '[logout]'
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: Camera(original,,1)
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 381
        y: 262
        width: 160
        height: 30
    - id: 10
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 4
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Launch codes downloaded.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: Delay({{end}})
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 'SetQuestState("Get the Launch Codes", "success");

        Variable["Alert"]
        = "Mission Complete: Launch Codes Acquired"'
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 84
        y: 324
        width: 160
        height: 30
    - id: 11
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 4
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Launch codes downloaded.
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: Delay({{end}})
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 11
        destinationConversationID: 5
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 288
        y: 324
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 1
  - id: 6
    fields:
    - title: Title
      value: Enemy Barks
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: These are one-liners barked by Enemy NPCs.
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 6
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 6
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Parenthetical
        value: 
        type: 0
        typeString: 
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 6
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 6
        originDialogueID: 0
        destinationConversationID: 6
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 6
        originDialogueID: 0
        destinationConversationID: 6
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      - originConversationID: 6
        originDialogueID: 0
        destinationConversationID: 6
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 20
        y: 80
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 6
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Die!
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: Audio(Enemy_6_1)
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 6
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 200
        y: 30
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 6
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: You're going down!
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: Audio(Enemy_6_2)
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 6
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 200
        y: 80
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: New Dialogue Entry
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 6
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: He's mine!
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: Audio(Enemy_6_3)
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 6
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 200
        y: 129
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 1
  syncInfo:
    syncActors: 0
    syncItems: 0
    syncLocations: 0
    syncVariables: 0
    syncActorsDatabase: {fileID: 0}
    syncItemsDatabase: {fileID: 0}
    syncLocationsDatabase: {fileID: 0}
    syncVariablesDatabase: {fileID: 0}
  templateJson: '{"treatItemsAsQuests":true,"actorFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"IsPlayer","value":"False","type":2,"typeString":"CustomFieldType_Boolean"}],"itemFields":[{"title":"Name","value":"","type":0,"typeString":""},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":""},{"title":"Is
    Item","value":"True","type":2,"typeString":"CustomFieldType_Boolean"}],"questFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Success
    Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Failure
    Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"State","value":"unassigned","type":0,"typeString":"CustomFieldType_QuestState"},{"title":"Is
    Item","value":"False","type":2,"typeString":"CustomFieldType_Boolean"}],"locationFields":[{"title":"Name","value":"","type":0,"typeString":""},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":""}],"variableFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Initial
    Value","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"}],"conversationFields":[{"title":"Title","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Actor","value":"0","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Conversant","value":"0","type":5,"typeString":"CustomFieldType_Actor"}],"dialogueEntryFields":[{"title":"Title","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Actor","value":"","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Conversant","value":"","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Menu
    Text","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Dialogue
    Text","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Parenthetical","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Audio
    Files","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Video
    File","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Sequence","value":"","type":0,"typeString":"CustomFieldType_Text"}],"actorPrimaryFieldTitles":[],"itemPrimaryFieldTitles":[],"questPrimaryFieldTitles":[],"locationPrimaryFieldTitles":[],"variablePrimaryFieldTitles":[],"conversationPrimaryFieldTitles":[],"dialogueEntryPrimaryFieldTitles":[],"npcLineColor":{"r":1.0,"g":0.0,"b":0.0,"a":1.0},"pcLineColor":{"r":0.0,"g":0.0,"b":1.0,"a":1.0},"repeatLineColor":{"r":0.5,"g":0.5,"b":0.5,"a":1.0}}'
