using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 架构测试脚本
/// 用于验证重构后的服务导向架构是否正常工作
/// </summary>
public class ArchitectureTest : MonoBehaviour
{
    [Header("=== 测试数据 ===")]
    [SerializeField] private CoreGameDataSO testCoreData;
    [SerializeField] private AudienceDataSO testAudience;
    [SerializeField] private KeywordDataSO testKeyword;
    [SerializeField] private DialogueOptionSO testDialogueOption;

    [Header("=== 测试控制 ===")]
    [SerializeField] private bool runTestsOnStart = false;

    private void Start()
    {
        if (runTestsOnStart)
        {
            RunAllTests();
        }
    }

    [ContextMenu("运行所有测试")]
    public void RunAllTests()
    {
        Debug.Log("=== 开始架构测试 ===");
        
        TestGameDataService();
        TestAudienceService();
        TestKeywordService();
        TestStoryService();
        TestMemoryService();
        
        Debug.Log("=== 架构测试完成 ===");
    }

    [ContextMenu("测试GameDataService")]
    public void TestGameDataService()
    {
        Debug.Log("--- 测试GameDataService ---");
        
        if (GameDataService.Instance == null)
        {
            Debug.LogError("GameDataService.Instance 为空！");
            return;
        }

        if (testCoreData == null)
        {
            Debug.LogError("testCoreData 未设置！");
            return;
        }

        // 设置测试数据
        GameDataService.Instance.coreGameData = testCoreData;
        
        float initialStamina = testCoreData.currentStamina;
        float initialWarmth = testCoreData.currentWarmth;
        float initialMoney = testCoreData.money;
        int initialFans = testCoreData.totalFans;

        // 测试精力系统
        bool canConsume = GameDataService.Instance.ConsumeStamina(10f);
        Debug.Log($"消耗精力测试：{canConsume}，精力变化：{initialStamina} -> {testCoreData.currentStamina}");

        GameDataService.Instance.RestoreStamina(5f);
        Debug.Log($"恢复精力测试：精力变化：{testCoreData.currentStamina - 5f} -> {testCoreData.currentStamina}");

        // 测试温度系统
        GameDataService.Instance.ModifyWarmth(1f);
        Debug.Log($"温度修改测试：温度变化：{initialWarmth} -> {testCoreData.currentWarmth}");

        // 测试金钱系统
        GameDataService.Instance.ModifyMoney(100f);
        Debug.Log($"金钱修改测试：金钱变化：{initialMoney} -> {testCoreData.money}");

        // 测试粉丝系统
        GameDataService.Instance.AddFans(50);
        Debug.Log($"粉丝增加测试：粉丝变化：{initialFans} -> {testCoreData.totalFans}");

        // 测试计算方法
        int fanGrowth = GameDataService.Instance.CalculateFanGrowth();
        float income = GameDataService.Instance.CalculateBaseIncome();
        Debug.Log($"计算测试：粉丝增长={fanGrowth}，基础收入={income:F2}");

        Debug.Log("GameDataService 测试完成");
    }

    [ContextMenu("测试AudienceService")]
    public void TestAudienceService()
    {
        Debug.Log("--- 测试AudienceService ---");
        
        if (AudienceService.Instance == null)
        {
            Debug.LogError("AudienceService.Instance 为空！");
            return;
        }

        if (testAudience == null)
        {
            Debug.LogError("testAudience 未设置！");
            return;
        }

        int initialDTV = testAudience.currentDTV;
        
        // 测试DTV增加
        bool dtvIncreased = AudienceService.Instance.IncreaseDTV(testAudience, 1);
        Debug.Log($"DTV增加测试：{dtvIncreased}，DTV变化：{initialDTV} -> {testAudience.currentDTV}");

        // 测试关键词收集
        if (testKeyword != null)
        {
            bool keywordCollected = AudienceService.Instance.CollectKeyword(testAudience, testKeyword);
            Debug.Log($"关键词收集测试：{keywordCollected}，关键词：{testKeyword.keyword}");
            
            bool hasKeyword = AudienceService.Instance.HasKeyword(testAudience, testKeyword.keyword);
            Debug.Log($"关键词检查测试：{hasKeyword}");
        }

        // 测试故事块解锁
        bool storyUnlocked = AudienceService.Instance.UnlockStoryBlock(testAudience, testAudience.currentDTV);
        Debug.Log($"故事块解锁测试：{storyUnlocked}，等级：{testAudience.currentDTV}");

        // 测试听众状态更新
        AudienceService.Instance.UpdateAudienceState(testAudience);
        Debug.Log($"听众状态更新测试：当前状态={testAudience.currentState}");

        Debug.Log("AudienceService 测试完成");
    }

    [ContextMenu("测试KeywordService")]
    public void TestKeywordService()
    {
        Debug.Log("--- 测试KeywordService ---");
        
        if (KeywordService.Instance == null)
        {
            Debug.LogError("KeywordService.Instance 为空！");
            return;
        }

        if (testKeyword == null)
        {
            Debug.LogError("testKeyword 未设置！");
            return;
        }

        // 测试关键词创建
        var createdKeyword = KeywordService.Instance.CreateKeyword(testKeyword.keyword, "test_audience");
        Debug.Log($"关键词创建测试：{createdKeyword != null}，关键词：{createdKeyword?.keyword}");

        if (createdKeyword != null)
        {
            // 测试过期检查
            bool isExpired = KeywordService.Instance.IsKeywordExpired(createdKeyword);
            Debug.Log($"关键词过期检查测试：{isExpired}");

            // 测试剩余时间
            float remainingHours = KeywordService.Instance.GetRemainingHours(createdKeyword);
            Debug.Log($"剩余时间测试：{remainingHours} 小时");

            // 测试状态描述
            string status = KeywordService.Instance.GetKeywordStatusDescription(createdKeyword);
            Debug.Log($"状态描述测试：{status}");

            // 测试演化检查
            bool canEvolve = KeywordService.Instance.CanEvolve(createdKeyword);
            Debug.Log($"演化检查测试：{canEvolve}");
        }

        Debug.Log("KeywordService 测试完成");
    }

    [ContextMenu("测试StoryService")]
    public void TestStoryService()
    {
        Debug.Log("--- 测试StoryService ---");
        
        if (StoryService.Instance == null)
        {
            Debug.LogError("StoryService.Instance 为空！");
            return;
        }

        if (testDialogueOption == null || testAudience == null)
        {
            Debug.LogError("testDialogueOption 或 testAudience 未设置！");
            return;
        }

        // 测试对话成功率计算
        var usedKeywords = new List<KeywordDataSO>();
        if (testKeyword != null)
        {
            usedKeywords.Add(testKeyword);
        }

        float successRate = StoryService.Instance.CalculateDialogueSuccessRate(testDialogueOption, testAudience, usedKeywords);
        Debug.Log($"对话成功率计算测试：{successRate:P2}");

        // 测试对话选项执行
        var result = StoryService.Instance.ExecuteDialogueOption(testDialogueOption, testAudience, usedKeywords);
        Debug.Log($"对话选项执行测试：成功={result.success}，消息={result.message}");

        Debug.Log("StoryService 测试完成");
    }

    [ContextMenu("测试MemoryService")]
    public void TestMemoryService()
    {
        Debug.Log("--- 测试MemoryService ---");
        
        if (MemoryService.Instance == null)
        {
            Debug.LogError("MemoryService.Instance 为空！");
            return;
        }

        // 测试回忆解锁检查
        MemoryService.Instance.CheckAllMemoryUnlocks();
        Debug.Log("回忆解锁检查测试完成");

        // 测试激活回忆检查
        bool hasActiveMemory = MemoryService.Instance.HasActiveMemory();
        Debug.Log($"激活回忆检查测试：{hasActiveMemory}");

        // 测试回忆状态更新
        MemoryService.Instance.UpdateMemoryStates();
        Debug.Log("回忆状态更新测试完成");

        Debug.Log("MemoryService 测试完成");
    }

    [ContextMenu("测试服务单例")]
    public void TestServiceSingletons()
    {
        Debug.Log("--- 测试服务单例 ---");
        
        Debug.Log($"GameDataService.Instance: {GameDataService.Instance != null}");
        Debug.Log($"AudienceService.Instance: {AudienceService.Instance != null}");
        Debug.Log($"KeywordService.Instance: {KeywordService.Instance != null}");
        Debug.Log($"StoryService.Instance: {StoryService.Instance != null}");
        Debug.Log($"MemoryService.Instance: {MemoryService.Instance != null}");
        
        Debug.Log("服务单例测试完成");
    }

    private void OnValidate()
    {
        // 确保测试数据有效
        if (testCoreData != null && testCoreData.currentStamina <= 0)
        {
            Debug.LogWarning("testCoreData 的精力值过低，可能影响测试结果");
        }
    }
}
